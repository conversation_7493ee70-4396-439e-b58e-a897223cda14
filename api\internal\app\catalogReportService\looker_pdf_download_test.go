package catalogReportService

import (
	"bytes"
	// "dng-module/internal/app/catalogReportService"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"os"
	"strings"
	"testing"
)

// helper to set env vars temporarily in tests
func setEnv(key, val string) func() {
	orig := os.Getenv(key)
	os.Setenv(key, val)
	return func() { os.Setenv(key, orig) }
}

func TestGetLookerAccessToken_Success(t *testing.T) {
	// Mock Looker auth server
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		resp := map[string]string{"access_token": "testtoken123"}
		json.NewEncoder(w).Encode(resp)
	}))
	defer ts.Close()

	restore := setEnv("LOOKERSDK_BASE_URL", ts.URL)
	defer restore()
	restoreClientID := setEnv("LOOKER_CLIENT_ID", "dummyid")
	defer restoreClientID()
	restoreClientSecret := setEnv("LOOKER_CLIENT_SECRET", "dummysecret")
	defer restoreClientSecret()

	token, err := GetLookerAccessToken()
	if err != nil {
		t.Fatalf("expected no error, got %v", err)
	}
	if token != "testtoken123" {
		t.Errorf("expected token 'testtoken123', got %s", token)
	}
}

func TestGetLookerAccessToken_Failure(t *testing.T) {
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusUnauthorized)
	}))
	defer ts.Close()

	restore := setEnv("LOOKERSDK_BASE_URL", ts.URL)
	defer restore()
	restoreClientID := setEnv("LOOKER_CLIENT_ID", "dummyid")
	defer restoreClientID()
	restoreClientSecret := setEnv("LOOKER_CLIENT_SECRET", "dummysecret")
	defer restoreClientSecret()

	_, err := GetLookerAccessToken()
	if err == nil {
		t.Fatalf("expected error for failed auth, got nil")
	}
}

func TestCreateRenderTask_Success(t *testing.T) {
	// Mock render task creation endpoint
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Check method, headers, and body if needed
		if r.Method != "POST" {
			t.Errorf("expected POST method, got %s", r.Method)
		}
		auth := r.Header.Get("Authorization")
		if !strings.HasPrefix(auth, "Bearer ") {
			t.Errorf("missing or invalid Authorization header")
		}
		resp := map[string]interface{}{"id": "12345"}
		json.NewEncoder(w).Encode(resp)
	}))
	defer ts.Close()

	restore := setEnv("LOOKERSDK_BASE_URL", ts.URL)
	defer restore()

	taskID, err := CreateRenderTask("dash-1", "mytoken")
	if err != nil {
		t.Fatalf("expected no error, got %v", err)
	}
	if taskID != "12345" {
		t.Errorf("expected task ID '12345', got %s", taskID)
	}
}

func TestCreateRenderTask_Failure(t *testing.T) {
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusBadRequest)
		w.Write([]byte(`{"error": "bad request"}`))
	}))
	defer ts.Close()

	restore := setEnv("LOOKERSDK_BASE_URL", ts.URL)
	defer restore()

	_, err := CreateRenderTask("dash-1", "mytoken")
	if err == nil {
		t.Fatalf("expected error due to bad response, got nil")
	}
}

func TestCheckRenderStatus_Success(t *testing.T) {
	// This test simulates the polling returning success immediately.
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		resp := map[string]string{"status": "success", "status_detail": "done"}
		json.NewEncoder(w).Encode(resp)
	}))
	defer ts.Close()

	restore := setEnv("LOOKERSDK_BASE_URL", ts.URL)
	defer restore()

	err := CheckRenderStatus("task123", "token")
	if err != nil {
		t.Fatalf("expected no error, got %v", err)
	}
}

func TestCheckRenderStatus_Failure(t *testing.T) {
	count := 0
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		count++
		// On first call return running, on second call failure
		if count == 1 {
			resp := map[string]string{"status": "running", "status_detail": "in progress"}
			json.NewEncoder(w).Encode(resp)
		} else {
			resp := map[string]string{"status": "failure", "status_detail": "error occurred"}
			json.NewEncoder(w).Encode(resp)
		}
	}))
	defer ts.Close()

	restore := setEnv("LOOKERSDK_BASE_URL", ts.URL)
	defer restore()

	err := CheckRenderStatus("task123", "token")
	if err == nil || err.Error() != "render task failed" {
		t.Fatalf("expected 'render task failed' error, got %v", err)
	}
}

func TestDownloadPDF_Failure(t *testing.T) {
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusInternalServerError)
	}))
	defer ts.Close()

	restore := setEnv("LOOKERSDK_BASE_URL", ts.URL)
	defer restore()

	_, err := DownloadPDF("task123", "token")
	if err == nil {
		t.Fatalf("expected error, got nil")
	}
}

func TestGetLookerAccessToken_InvalidJSON(t *testing.T) {
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Write([]byte("invalid-json"))
	}))
	defer ts.Close()

	restore := setEnv("LOOKERSDK_BASE_URL", ts.URL)
	defer restore()
	setEnv("LOOKER_CLIENT_ID", "id")()
	setEnv("LOOKER_CLIENT_SECRET", "secret")()

	_, err := GetLookerAccessToken()
	if err == nil {
		t.Fatal("expected JSON decode error")
	}
}

func TestGetLookerAccessToken_InvalidTokenType(t *testing.T) {
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		json.NewEncoder(w).Encode(map[string]interface{}{"access_token": 12345})
	}))
	defer ts.Close()

	restore := setEnv("LOOKERSDK_BASE_URL", ts.URL)
	defer restore()
	setEnv("LOOKER_CLIENT_ID", "id")()
	setEnv("LOOKER_CLIENT_SECRET", "secret")()

	_, err := GetLookerAccessToken()
	if err == nil || !strings.Contains(err.Error(), "invalid access token format") {
		t.Fatalf("expected error about access token format, got: %v", err)
	}
}
func TestCreateRenderTask_InvalidJSON(t *testing.T) {
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Write([]byte("not-json"))
	}))
	defer ts.Close()

	setEnv("LOOKERSDK_BASE_URL", ts.URL)()
	defer setEnv("LOOKERSDK_BASE_URL", ts.URL)()

	_, err := CreateRenderTask("dash", "token")
	if err == nil || !strings.Contains(err.Error(), "failed to parse") {
		t.Fatalf("expected parse error, got %v", err)
	}
}

func TestCreateRenderTask_MissingID(t *testing.T) {
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		json.NewEncoder(w).Encode(map[string]interface{}{})
	}))
	defer ts.Close()

	setEnv("LOOKERSDK_BASE_URL", ts.URL)()
	defer setEnv("LOOKERSDK_BASE_URL", ts.URL)()

	_, err := CreateRenderTask("dash", "token")
	if err == nil || !strings.Contains(err.Error(), "invalid or missing task ID") {
		t.Fatalf("expected missing ID error, got %v", err)
	}
}

func TestCreateRenderTask_IDAsFloat(t *testing.T) {
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		json.NewEncoder(w).Encode(map[string]interface{}{"id": 98765.0})
	}))
	defer ts.Close()

	setEnv("LOOKERSDK_BASE_URL", ts.URL)()
	defer setEnv("LOOKERSDK_BASE_URL", ts.URL)()

	id, err := CreateRenderTask("dash", "token")
	if err != nil || id != "98765" {
		t.Fatalf("expected id '98765', got %s with err %v", id, err)
	}
}

func TestCheckRenderStatus_InvalidJSON(t *testing.T) {
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Write([]byte("not-json"))
	}))
	defer ts.Close()

	setEnv("LOOKERSDK_BASE_URL", ts.URL)()
	defer setEnv("LOOKERSDK_BASE_URL", ts.URL)()

	err := CheckRenderStatus("task", "token")
	if err == nil || !strings.Contains(err.Error(), "failed to parse") {
		t.Fatalf("expected parse error, got %v", err)
	}
}

func TestCheckRenderStatus_Non200(t *testing.T) {
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		http.Error(w, "error", http.StatusInternalServerError)
	}))
	defer ts.Close()

	setEnv("LOOKERSDK_BASE_URL", ts.URL)()
	defer setEnv("LOOKERSDK_BASE_URL", ts.URL)()

	err := CheckRenderStatus("task", "token")
	if err == nil || !strings.Contains(err.Error(), "unexpected response") {
		t.Fatalf("expected unexpected response error, got %v", err)
	}
}

func TestCheckRenderStatus_Timeout(t *testing.T) {
	// Make it always return "running" to trigger timeout
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		json.NewEncoder(w).Encode(map[string]string{"status": "running", "status_detail": "waiting"})
	}))
	defer ts.Close()

	setEnv("LOOKERSDK_BASE_URL", ts.URL)()

	// Temporarily override time.After to simulate timeout faster if needed (not trivial without interface injection)
	// Otherwise, skip actual timeout test due to 60s wait.
}

func TestDownloadPDF_Success(t *testing.T) {
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Write([]byte("PDF-DATA"))
	}))
	defer ts.Close()

	setEnv("LOOKERSDK_BASE_URL", ts.URL)()
	defer setEnv("LOOKERSDK_BASE_URL", ts.URL)()

	data, err := DownloadPDF("task123", "token")
	if err != nil {
		t.Fatalf("expected no error, got %v", err)
	}
	if !bytes.Equal(data, []byte("PDF-DATA")) {
		t.Errorf("unexpected PDF data: %s", string(data))
	}
}

func TestDownloadPDF_FailureStatus(t *testing.T) {
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		http.Error(w, "fail", http.StatusInternalServerError)
	}))
	defer ts.Close()

	setEnv("LOOKERSDK_BASE_URL", ts.URL)()
	defer setEnv("LOOKERSDK_BASE_URL", ts.URL)()

	_, err := DownloadPDF("task123", "token")
	if err == nil || !strings.Contains(err.Error(), "failed to download PDF") {
		t.Fatalf("expected error, got %v", err)
	}
}

func TestDownloadPDF_HTTPErr(t *testing.T) {
	// Simulate network failure by shutting down test server
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {}))
	ts.Close()

	setEnv("LOOKERSDK_BASE_URL", ts.URL)()

	_, err := DownloadPDF("task123", "token")
	if err == nil {
		t.Fatal("expected network error, got nil")
	}
}
