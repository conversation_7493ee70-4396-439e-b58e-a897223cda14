include:
  - project: tde/mat/mat-dti-tools
    file: /.gitlab-ci/pipeline-go.yml


# See list of pipeline variables at https://gitrepo.dolgen.net/tde/mat/mat-dti-tools/-/blob/master/.gitlab-ci/pipeline-switches.yml
variables:
  INCLUDE_UNIT_TESTS: "OFF"
  INCLUDE_DB_VALIDATIONS: "OFF"
  PORT_MAPPING: "8080"
  DEV_DEPLOY_MANUAL: "OFF"
  APP_NAME: "app-module"
  APP_TIER: "api"
  APP_TECH_STACK: "go"

# Begin job list below


