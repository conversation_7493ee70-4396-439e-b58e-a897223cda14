import '@testing-library/jest-dom'
import { render, screen } from '@testing-library/react'
import ChatSections from '../../app/omni-bot/components/ChatSections'

const sections = [
    { title: 'Today', chats: ['Chat 1', 'Chat 2'] },
    { title: 'Yesterday', chats: ['Chat 3'] },
]

describe('ChatSections', () => {
    // Polyfill for crypto.randomUUID for test environment
    beforeAll(() => {
        if (!global.crypto) {
            // @ts-ignore
            global.crypto = {}
        }
        // @ts-ignore
        if (!global.crypto.randomUUID) {
            // @ts-ignore
            global.crypto.randomUUID = () => Math.random().toString(36).slice(2)
        }
    })

    it('renders section titles and chats', () => {
        render(<ChatSections sections={sections} />)
        expect(screen.getByText('Today')).toBeInTheDocument()
        expect(screen.getByText('Yesterday')).toBeInTheDocument()
        expect(screen.getByText('Chat 1')).toBeInTheDocument()
        expect(screen.getByText('Chat 3')).toBeInTheDocument()
    })
})
