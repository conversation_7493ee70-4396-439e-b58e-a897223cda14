'use client'
import React from 'react'
import dynamic from 'next/dynamic'
import { Box, Typography, Paper } from '@mui/material'

// Dynamically import ApexCharts to avoid SSR issues
const Chart = dynamic(() => import('react-apexcharts'), { ssr: false })

const SocialMediaChart: React.FC = () => {
    // Chart options
    const options = {
        chart: {
            id: 'social-media-impact',
            toolbar: {
                show: false,
            },
            fontFamily: 'Arial, sans-serif',
        },
        colors: ['#FFC107', '#FF5722', '#2196F3', '#9C27B0'],
        labels: ['Instagram', 'Facebook', 'TikTok', 'Twitter', 'Pinterest'],
        plotOptions: {
            radar: {
                size: 140,
                polygons: {
                    strokeColors: '#e9e9e9',
                    fill: {
                        colors: ['#f8f8f8', '#fff'],
                    },
                },
            },
        },
        title: {
            text: '',
        },
        stroke: {
            width: 2,
        },
        fill: {
            opacity: 0.1,
        },
        markers: {
            size: 4,
        },
        legend: {
            position: 'top',
            horizontalAlign: 'right',
            fontSize: '12px',
            fontFamily: 'Arial, sans-serif',
            labels: {
                colors: '#616161',
            },
        },
        tooltip: {
            theme: 'light',
            y: {
                formatter: function (value: number) {
                    return value + ' points'
                },
            },
        },
    }

    // Chart series data
    const series = [
        {
            name: 'Brand Awareness',
            data: [80, 65, 85, 40, 55],
        },
        {
            name: 'Engagement Rate',
            data: [70, 50, 90, 60, 75],
        },
        {
            name: 'Conversion Impact',
            data: [60, 45, 75, 30, 65],
        },
        {
            name: 'Customer Sentiment',
            data: [75, 60, 65, 50, 70],
        },
    ]

    return (
        <Paper
            elevation={0}
            sx={{
                p: 2,
                borderRadius: 3,
                border: '1px solid',
                borderColor: 'divider',
                backgroundColor: 'white',
                mb: 3,
            }}
        >
            <Typography variant="h6" gutterBottom sx={{ fontWeight: 'bold', color: '#424242' }}>
                Social Media Impact Analysis
            </Typography>
            <Typography variant="body2" sx={{ color: 'text.secondary', mb: 2 }}>
                Comparison of key performance metrics across social platforms (scale: 0-100)
            </Typography>
            <Box sx={{ height: 350 }}>
                <Chart options={options as any} series={series} type="radar" height="100%" width="100%" />
            </Box>
        </Paper>
    )
}

export default SocialMediaChart
