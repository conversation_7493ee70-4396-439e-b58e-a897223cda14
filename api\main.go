package main

import (
	"context"
	"errors"
	"log/slog"
	"net/http"
	"os"
	"os/signal"
	"path/filepath"
	"runtime/debug"
	"syscall"
	"time"

	"dng-module/internal/app"
)

var (
	version = "dev"
	commit  = "none"
	date    = "unknown"
	builtBy = "unknown"
)

func main() {
	logger := slog.New(slog.NewJSONHandler(os.Stdout, nil))
	slog.SetDefault(logger)

	bi, ok := debug.ReadBuildInfo()
	if !ok {
		slog.Error("Failed to read build info")
		os.Exit(1)
	}

	appName := filepath.Base(bi.Path)
	slog.Info("Starting application",
		"name", appName,
		"version", version,
		"commit", commit,
		"built_at", date,
		"built_by", builtBy,
	)

	// Determine environment
	env := "development"
	if e := os.Getenv("APP_ENV"); e != "" {
		env = e
	}

	// Create context with timeout for initialization
	initCtx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	application, err := app.NewApp(initCtx, env)
	if err != nil {
		slog.Error("Failed to initialize app", "error", err)
		os.Exit(1)
	}

	// Set up signal handling
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)

	// Run the application
	go func() {
		if err := application.Run(context.Background()); err != nil && !errors.Is(err, http.ErrServerClosed) {
			slog.Error("Application failed", "error", err)
			quit <- syscall.SIGTERM
		}
	}()

	<-quit
	slog.Info("Shutting down application...")

	// Cleanup using the public Close method
	if err := application.Close(); err != nil {
		slog.Error("Error during application shutdown", "error", err)
	}

	slog.Info("Application stopped")
}
