import { format, isSameDay, isSame<PERSON><PERSON><PERSON>, isSame<PERSON>ear, isThisWeek, isThisYear, isToday, isYesterday } from 'date-fns'
import { Session } from 'next-auth'
import { ADMIN_ROLE, SUPER_ADMIN } from './constants'

export const normalizeDate = (date: Date | null) =>
    date ? new Date(date.setHours(12, 0, 0, 0)).toISOString().split('T')[0] : ''

// utils/downloadBlob.ts
export const downloadBlob = (blob: Blob, filename: string) => {
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = filename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
}

export const checkIsAdmin = (user: Session['user'] | undefined) =>
    [ADMIN_ROLE, SUPER_ADMIN].includes(user?.role_name ?? '')

export const checkRole = (user: Session['user'] | undefined, role: string) => user?.role_name === role

export const formatDateTime = (dateTime: string, formatting = 'MMM dd, yyyy, hh:mm a') => {
    const date = new Date(dateTime)
    return format(date, formatting)
}

export const formatEventTimeRange = (
    startDateTime: Date | string,
    endDateTime: Date | string,
    showOnlyDates = false,
): string => {
    const startDate = typeof startDateTime === 'string' ? new Date(startDateTime) : startDateTime
    const endDate = typeof endDateTime === 'string' ? new Date(endDateTime) : endDateTime

    const getFriendlyDay = (date: Date): string => {
        if (isToday(date)) return 'Today'
        if (isYesterday(date)) return 'Yesterday'
        if (isThisWeek(date)) return format(date, 'EEE') // e.g., "Mon", "Tue"
        return ''
    }

    const formatDayDate = (date: Date): string => {
        const friendlyDay = getFriendlyDay(date)
        if (friendlyDay && friendlyDay !== 'Today' && friendlyDay !== 'Yesterday') {
            return `${friendlyDay}, ${format(date, 'MMM d')}`
        }
        return friendlyDay || format(date, 'MMM d')
    }

    const buildSameDayRange = () => {
        const dayDisplay = formatDayDate(startDate)
        if (showOnlyDates) return dayDisplay
        return `${dayDisplay} @${format(startDate, 'h:mm')} - ${format(endDate, 'h:mm a')}`
    }

    const buildSameMonthRange = () => {
        const startDayDisplay = formatDayDate(startDate)
        const endDayDisplay = formatDayDate(endDate)
        const endDateDisplay =
            endDayDisplay === 'Today' || endDayDisplay === 'Yesterday' || endDayDisplay.includes(',')
                ? endDayDisplay
                : format(endDate, 'd')
        const yearSuffix = isThisYear(endDate) ? '' : `, ${format(endDate, 'yyyy')}`

        if (showOnlyDates) {
            return `${startDayDisplay} - ${endDateDisplay}${yearSuffix}`
        }
        return `${startDayDisplay} @${format(startDate, 'h:mm a')} - ${endDateDisplay}${yearSuffix} @${format(endDate, 'h:mm a')}`
    }

    const buildDifferentMonthRange = () => {
        const startDayDisplay = formatDayDate(startDate)
        const endDayDisplay = formatDayDate(endDate)
        const yearSuffix = isThisYear(endDate) ? '' : `, ${format(endDate, 'yyyy')}`

        if (showOnlyDates) {
            return `${startDayDisplay} - ${endDayDisplay}${yearSuffix}`
        }
        return `${startDayDisplay} @${format(startDate, 'h:mm a')} - ${endDayDisplay}${yearSuffix} @${format(endDate, 'h:mm a')}`
    }

    if (isSameDay(startDate, endDate)) {
        return buildSameDayRange()
    }

    if (isSameMonth(startDate, endDate) && isSameYear(startDate, endDate)) {
        return buildSameMonthRange()
    }

    return buildDifferentMonthRange()
}

export function getReportView(view?: string): 'all' | 'favorites' | 'saved-later' {
    const allowedViews = ['all', 'favorites', 'saved-later'] as const
    return allowedViews.includes(view as any) ? (view as (typeof allowedViews)[number]) : 'all'
}

export const createPBILink = (reportId: string, groupId: string): string => {
    const baseUrl = 'https://app.powerbi.com'
    if (!reportId) {
        throw new Error('Missing reportId in the input.')
    }
    const encodedReportId = encodeURIComponent(reportId)
    const encodedGroupId = groupId ? encodeURIComponent(groupId) : ''
    return `${baseUrl}/groups/${encodedGroupId}/reports/${encodedReportId}`
}
