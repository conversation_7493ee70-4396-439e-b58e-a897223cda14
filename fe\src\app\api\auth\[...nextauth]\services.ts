'use server'

import { postRequest } from '@/utils/axiosInstance'
import { BACKEND_URL } from '@/utils/constants'

export type UpsertUserParams = {
    email: string
    ad_groups: string[]
    first_name: string
    last_name: string
    access_token: string | null
}

type RoleInfo = {
    role_id: number
    role: string
}

export type UpsertUserApiResponse = {
    data: {
        user_id: string
        email: string
        role_id: number
        role_name: string
        priority: number
        roles: RoleInfo[]
    }
    message: string
}

export async function upsertUser({ access_token, ...userDetails }: UpsertUserParams): Promise<UpsertUserApiResponse> {
    try {
        if (!access_token) {
            throw new Error('Missing access token for authorization')
        }
        const response = await postRequest<UpsertUserApiResponse>(`${BACKEND_URL}/user/upsert`, userDetails, {
            headers: {
                Authorization: `Bearer ${access_token}`,
            },
        })
        const { data, status, statusText } = response
        if (status !== 200) {
            throw new Error(`Failed to upsert user: ${statusText || 'Unknown error'}`)
        }
        if (!data) {
            throw new Error('No data received from upsert user API')
        }
        return data
    } catch (error: unknown) {
        const message = error instanceof Error ? error.message : 'Unknown error occurred in upsertUser'
        console.error('Upsert User Error:', message)
        throw new Error(`Upsert User Error: ${message}`)
    }
}
