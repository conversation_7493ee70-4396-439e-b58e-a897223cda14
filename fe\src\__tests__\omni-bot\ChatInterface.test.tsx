import '@testing-library/jest-dom'
import { fireEvent, render, screen, waitFor } from '@testing-library/react'
import ChatInterface from '../../app/omni-bot/components/ChatInterface'

// Mock services and chart components
jest.mock('../../app/omni-bot/services', () => ({
    fetchTopicContextService: jest.fn(async () => ({
        networkData: {
            title: 'Network Data',
            context: 'Network context',
            insights: ['Insight 1', 'Insight 2'],
            explorationQuestions: [{ question: 'What is audit accuracy?', answer: '93%' }],
        },
    })),
    getTopicIdToKeyMap: jest.fn(() => ({ '1': 'networkData' })),
    getTopicKeywords: jest.fn(() => ({ networkData: ['network'] })),
}))
jest.mock('../../app/omni-bot/components/charts/NetworkDataComboChart', () => () => <div>Network Chart</div>)
jest.mock('../../app/omni-bot/components/charts/DCSpecificTrendsChart', () => () => <div>DC Chart</div>)
jest.mock('../../app/omni-bot/components/charts/EmployeePerformanceChart', () => () => <div>Employee Chart</div>)

describe('ChatInterface', () => {
    // Silence act warnings in test output
    beforeAll(() => {
        jest.spyOn(console, 'error').mockImplementation((msg, ...args) => {
            if (typeof msg === 'string' && msg.includes('not wrapped in act')) {
                return
            }
            // @ts-ignore
            return globalThis.console.error.original ? globalThis.console.error.original(msg, ...args) : undefined
        })
        // Save original for restoration
        // @ts-ignore
        globalThis.console.error.original ??= console.error
        // Polyfill for scrollIntoView in test environment
        if (!window.HTMLElement.prototype.scrollIntoView) {
            window.HTMLElement.prototype.scrollIntoView = function () {}
        }
    })

    afterAll(() => {
        // @ts-ignore
        if (globalThis.console.error.original) {
            // @ts-ignore
            console.error = globalThis.console.error.original
        }
    })

    it('renders initial bot message', async () => {
        render(<ChatInterface chatTitle="Test Chat" onClose={() => {}} />)
        // There are two elements with "Omnibot": the label and the message text.
        // Assert both are present.
        const omnibotLabels = await screen.findAllByText(/Omnibot/)
        expect(omnibotLabels.length).toBeGreaterThan(0)
        expect(screen.getByText("Hello! I'm Omnibot, your AI assistant.")).toBeInTheDocument()
    })

    it('sends a user message and shows fallback response', async () => {
        render(<ChatInterface chatTitle="Test Chat" onClose={() => {}} />)
        const input = screen.getByPlaceholderText('Ask Omnibot...')
        fireEvent.change(input, { target: { value: 'hello' } })
        fireEvent.keyDown(input, { key: 'Enter', code: 'Enter' })
        expect(await screen.findByText('hello')).toBeInTheDocument()
        await waitFor(() => {
            expect(screen.getByText(/I understand you're asking about/i)).toBeInTheDocument()
        })
    })

    it('shows chart when chart button is clicked', async () => {
        // Simulate topic context in sessionStorage
        sessionStorage.setItem('topicId', '1')
        render(<ChatInterface chatTitle="Test Chat" onClose={() => {}} />)
        await waitFor(() => {
            expect(screen.getByText(/Network Data/)).toBeInTheDocument()
        })
        // Click chart icon button
        const chartButton = screen
            .getAllByRole('button')
            .find((btn) => btn.querySelector('svg[data-testid="BarChartIcon"]'))
        expect(chartButton).toBeTruthy()
        fireEvent.click(chartButton!)
        expect(await screen.findByText(/Network Chart/)).toBeInTheDocument()
    })

    it('calls onClose when back arrow is clicked', () => {
        const onClose = jest.fn()
        render(<ChatInterface chatTitle="Test Chat" onClose={onClose} />)
        const backButton = screen
            .getAllByRole('button')
            .find((btn) => btn.querySelector('svg[data-testid="ArrowBackIcon"]'))
        expect(backButton).toBeTruthy()
        fireEvent.click(backButton!)
        expect(onClose).toHaveBeenCalled()
    })
})
