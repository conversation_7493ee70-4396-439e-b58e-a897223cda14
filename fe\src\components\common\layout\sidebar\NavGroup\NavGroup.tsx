import PropTypes from 'prop-types'
// mui imports
import { Divider, ListSubheader, styled, Theme } from '@mui/material'

interface ItemType {
    item: {
        subheader: string
        [key: string]: any
    }
    isCollapsed: boolean
}

const NavGroup = ({ item, isCollapsed }: ItemType) => {
    // Define the ListSubheader styled component correctly with Theme typing
    const ListSubheaderStyle = styled(ListSubheader)(({ theme }: { theme: Theme }) => ({
        ...theme.typography.overline,
        fontWeight: '700',
        textTransform: 'none',
        marginTop: theme.spacing(1),
        marginBottom: theme.spacing(0),
        color: theme.palette.text.primary,
        lineHeight: '26px',
        padding: '3px 12px',
    }))

    return (
        <ListSubheaderStyle>
            {isCollapsed ? '' : item.subheader}
            <Divider />
        </ListSubheaderStyle>
    )
}

NavGroup.propTypes = {
    item: PropTypes.object.isRequired,
    isCollapsed: PropTypes.bool.isRequired,
}

export default NavGroup
