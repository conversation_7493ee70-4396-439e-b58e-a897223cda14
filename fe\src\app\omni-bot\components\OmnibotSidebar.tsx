'use client'
import {
    Delete as DeleteIcon,
    Edit as EditIcon,
    Logout as LogoutIcon,
    MoreVert as MoreVertIcon,
    Settings as SettingsIcon,
} from '@mui/icons-material'
import {
    Box,
    Button,
    Dialog,
    DialogActions,
    DialogContent,
    DialogTitle,
    Divider,
    IconButton,
    List,
    ListItemButton,
    ListItemText,
    Menu,
    MenuItem,
    styled,
    TextField,
    Typography,
} from '@mui/material'
import React, { useState } from 'react'
import { Chat } from '../types'

const SidebarContainer = styled(Box)(({ theme }) => ({
    width: '250px',
    height: 'calc(100vh - 84px)',
    position: 'fixed',
    left: 0,
    top: 84,
    backgroundColor: '#ffffff',
    display: 'flex',
    flexDirection: 'column',
    padding: theme.spacing(2),
    boxShadow: '0px 7px 30px 0px rgba(90, 114, 123, 0.11)',
    borderRight: '1px solid #e0e0e0',
    transition: 'all 0.3s ease-in-out',
    overflowY: 'auto',
    '&::-webkit-scrollbar': {
        width: '7px',
    },
    '&::-webkit-scrollbar-thumb': {
        backgroundColor: '#eff2f7',
        borderRadius: '15px',
    },
    [theme.breakpoints.down('md')]: {
        width: '100%',
        height: 'calc(100vh - 84px)',
        zIndex: 1200,
        transform: 'translateX(-100%)',
        transition: 'transform 0.3s ease-in-out',
    },
}))

const MobileSidebarContainer = styled(Box)(({ theme }) => ({
    width: '100%',
    height: 'calc(100vh - 84px)',
    position: 'fixed',
    left: 0,
    top: 84,
    backgroundColor: '#ffffff',
    display: 'flex',
    flexDirection: 'column',
    padding: theme.spacing(2),
    boxShadow: '0px 7px 30px 0px rgba(90, 114, 123, 0.11)',
    borderRight: '1px solid #e0e0e0',
    overflowY: 'auto',
    zIndex: 1200,
    '&::-webkit-scrollbar': {
        width: '7px',
    },
    '&::-webkit-scrollbar-thumb': {
        backgroundColor: '#eff2f7',
        borderRadius: '15px',
    },
}))

interface OmnibotSidebarProps {
    isMobile: boolean
    isOpen: boolean
    onClose: () => void
    onChatSelect: (chatTitle: string) => void
    onNewChat: () => void
    onExplore: () => void
    showExploreButton?: boolean // Optional prop to control visibility of Explore button
    onRenameChat?: (chatId: string, newTitle: string) => void
    onDeleteChat?: (chatId: string) => void
    chats?: Chat[] // Now using Chat type
}

// Helper to group chats by "Today" and "Yesterday" (simple split for demo)
function groupChats(chats: Chat[] = []) {
    // For demo, first 3 are "Today", rest are "Yesterday"
    return [
        { title: 'Today', chats: chats.slice(0, 3) },
        { title: 'Yesterday', chats: chats.slice(3) },
    ]
}

const OmnibotSidebar: React.FC<OmnibotSidebarProps> = ({
    isMobile,
    isOpen,
    onClose,
    onChatSelect,
    onNewChat,
    onExplore,
    showExploreButton = true, // Default to showing the button
    onRenameChat = () => {}, // Default empty function
    onDeleteChat = () => {}, // Default empty function
    chats = [],
}) => {
    const Container = isMobile ? MobileSidebarContainer : SidebarContainer
    const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null)
    const [selectedChat, setSelectedChat] = useState<Chat | null>(null)
    const [isRenameDialogOpen, setIsRenameDialogOpen] = useState(false)
    const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
    const [newChatTitle, setNewChatTitle] = useState('')

    // Determine transform value based on mobile and open state
    let transformValue = 'none'
    if (isMobile) {
        transformValue = isOpen ? 'translateX(0)' : 'translateX(-100%)'
    }

    // Handle opening the menu
    const handleMenuOpen = (event: React.MouseEvent<HTMLButtonElement>, chat: Chat) => {
        event.stopPropagation()
        setAnchorEl(event.currentTarget)
        setSelectedChat(chat)
    }

    // Handle closing the menu
    const handleMenuClose = () => {
        setAnchorEl(null)
    }

    // Handle opening the rename dialog
    const handleRenameClick = () => {
        if (selectedChat) {
            setNewChatTitle(selectedChat.title)
            setIsRenameDialogOpen(true)
            handleMenuClose()
        }
    }

    // Handle opening the delete dialog
    const handleDeleteClick = () => {
        setIsDeleteDialogOpen(true)
        handleMenuClose()
    }

    // Handle renaming a chat
    const handleRenameConfirm = () => {
        if (selectedChat && newChatTitle.trim()) {
            // Call the parent handler
            onRenameChat(selectedChat.id, newChatTitle.trim())

            // Close the dialog
            setIsRenameDialogOpen(false)
            setSelectedChat(null)
        }
    }

    // Handle deleting a chat
    const handleDeleteConfirm = () => {
        if (selectedChat) {
            // Call the parent handler
            onDeleteChat(selectedChat.id)

            // Close the dialog
            setIsDeleteDialogOpen(false)
            setSelectedChat(null)
        }
    }

    const groupedChatData = Array.isArray(chats) && chats.length > 0 ? groupChats(chats) : []

    return (
        <Container
            className="omnibot-sidebar"
            sx={{
                transform: transformValue,
            }}
        >
            {/* Header Section - Removed */}

            {/* Explore Button - Only shown when showExploreButton is true */}
            {showExploreButton && (
                <Button
                    variant="contained"
                    fullWidth
                    onClick={onExplore}
                    sx={{
                        mb: 2,
                        backgroundColor: '#ffeb3b',
                        color: 'rgba(0,0,0,0.8)',
                        py: 1.2,
                        boxShadow: '0 4px 8px rgba(255, 235, 59, 0.3)',
                        transition: 'all 0.2s ease',
                        '&:hover': {
                            backgroundColor: '#fdd835',
                            transform: 'translateY(-2px)',
                            boxShadow: '0 6px 12px rgba(255, 235, 59, 0.4)',
                        },
                        borderRadius: 2,
                        fontWeight: 'bold',
                        position: 'relative',
                        zIndex: 10,
                    }}
                >
                    EXPLORE AI DNG
                </Button>
            )}

            {/* Chat Sections */}
            {groupedChatData.length === 0 ? (
                <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center', mt: 4 }}>
                    No chats available.
                </Typography>
            ) : (
                groupedChatData.map((section) => (
                    <Box key={section.title} sx={{ mb: 3 }}>
                        <Typography variant="subtitle1" sx={{ mb: 1, fontWeight: 'bold' }}>
                            {section.title}
                        </Typography>
                        <List disablePadding>
                            {section.chats.map((chat) => (
                                <ListItemButton
                                    key={chat.id}
                                    onClick={() => onChatSelect(chat.title)}
                                    sx={{
                                        borderRadius: 1.5,
                                        mb: 0.8,
                                        py: 1,
                                        transition: 'all 0.2s ease',
                                        border: '1px solid transparent',
                                        display: 'flex',
                                        justifyContent: 'space-between',
                                        '&:hover': {
                                            backgroundColor: 'rgba(255, 193, 7, 0.08)',
                                            transform: 'translateX(3px)',
                                            borderLeft: '3px solid #FFC107',
                                        },
                                        '&:hover .chat-actions': {
                                            opacity: 1,
                                        },
                                    }}
                                >
                                    <ListItemText
                                        primary={
                                            <Typography
                                                noWrap
                                                sx={{
                                                    textOverflow: 'ellipsis',
                                                    fontSize: '0.9rem',
                                                    fontWeight: 500,
                                                }}
                                            >
                                                {chat.title}
                                            </Typography>
                                        }
                                    />
                                    <IconButton
                                        size="small"
                                        className="chat-actions"
                                        onClick={(e) => handleMenuOpen(e, chat)}
                                        sx={{
                                            opacity: 0,
                                            transition: 'opacity 0.2s ease',
                                            color: 'rgba(0, 0, 0, 0.54)',
                                            '&:hover': {
                                                backgroundColor: 'rgba(0, 0, 0, 0.04)',
                                            },
                                        }}
                                    >
                                        <MoreVertIcon fontSize="small" />
                                    </IconButton>
                                </ListItemButton>
                            ))}
                        </List>
                    </Box>
                ))
            )}

            {/* Chat Actions Menu */}
            <Menu
                anchorEl={anchorEl}
                open={Boolean(anchorEl)}
                onClose={handleMenuClose}
                anchorOrigin={{
                    vertical: 'bottom',
                    horizontal: 'right',
                }}
                transformOrigin={{
                    vertical: 'top',
                    horizontal: 'right',
                }}
            >
                <MenuItem onClick={handleRenameClick}>
                    <EditIcon fontSize="small" sx={{ mr: 1 }} />
                    Rename
                </MenuItem>
                <MenuItem onClick={handleDeleteClick} sx={{ color: '#f44336' }}>
                    <DeleteIcon fontSize="small" sx={{ mr: 1 }} />
                    Delete
                </MenuItem>
            </Menu>

            {/* Rename Dialog */}
            <Dialog open={isRenameDialogOpen} onClose={() => setIsRenameDialogOpen(false)} maxWidth="xs" fullWidth>
                <DialogTitle>Rename Chat</DialogTitle>
                <DialogContent>
                    <TextField
                        autoFocus
                        margin="dense"
                        label="Chat Name"
                        type="text"
                        fullWidth
                        value={newChatTitle}
                        onChange={(e) => setNewChatTitle(e.target.value)}
                        variant="outlined"
                    />
                </DialogContent>
                <DialogActions>
                    <Button onClick={() => setIsRenameDialogOpen(false)} color="inherit">
                        Cancel
                    </Button>
                    <Button
                        onClick={handleRenameConfirm}
                        variant="contained"
                        sx={{
                            backgroundColor: '#FFC107 !important',
                            color: 'black',
                            '&:hover': {
                                backgroundColor: '#FFB000 !important',
                            },
                        }}
                    >
                        Save
                    </Button>
                </DialogActions>
            </Dialog>

            {/* Delete Confirmation Dialog */}
            <Dialog open={isDeleteDialogOpen} onClose={() => setIsDeleteDialogOpen(false)} maxWidth="xs">
                <DialogTitle>Delete Chat</DialogTitle>
                <DialogContent>
                    <Typography>Are you sure you want to delete this chat? This action cannot be undone.</Typography>
                </DialogContent>
                <DialogActions>
                    <Button onClick={() => setIsDeleteDialogOpen(false)} color="inherit">
                        Cancel
                    </Button>
                    <Button onClick={handleDeleteConfirm} color="error" variant="contained">
                        Delete
                    </Button>
                </DialogActions>
            </Dialog>

            {/* Flexible space */}
            <Box sx={{ flexGrow: 1 }}></Box>

            {/* Bottom Links */}
            <Box sx={{ mt: 2 }}>
                <Divider sx={{ mb: 2, opacity: 0.6 }} />
                <List disablePadding>
                    <ListItemButton
                        sx={{
                            borderRadius: 2,
                            mb: 1,
                            transition: 'all 0.2s ease',
                            '&:hover': {
                                backgroundColor: 'rgba(0, 0, 0, 0.04)',
                                transform: 'translateX(3px)',
                            },
                        }}
                    >
                        <SettingsIcon sx={{ mr: 1.5, color: '#616161' }} />
                        <ListItemText
                            primary={<Typography sx={{ fontWeight: 500, fontSize: '0.9rem' }}>Settings</Typography>}
                        />
                    </ListItemButton>
                    <ListItemButton
                        sx={{
                            borderRadius: 2,
                            transition: 'all 0.2s ease',
                            '&:hover': {
                                backgroundColor: 'rgba(244, 67, 54, 0.08)',
                                transform: 'translateX(3px)',
                            },
                        }}
                    >
                        <LogoutIcon sx={{ mr: 1.5, color: '#f44336' }} />
                        <ListItemText
                            primary={
                                <Typography sx={{ color: '#f44336', fontWeight: 500, fontSize: '0.9rem' }}>
                                    Sign out
                                </Typography>
                            }
                        />
                    </ListItemButton>
                </List>
            </Box>
        </Container>
    )
}

export default OmnibotSidebar
