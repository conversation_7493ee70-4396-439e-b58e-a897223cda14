import React from 'react'

type Props = {
    children: React.ReactNode
}

type State = {
    hasError: boolean
    error: Error | null
}

export class ErrorBoundary extends React.Component<Props, State> {
    constructor(props: Props) {
        super(props)
        this.state = { hasError: false, error: null }
    }

    static getDerivedStateFromError(error: Error) {
        return { hasError: true, error }
    }

    render() {
        if (this.state.hasError && this.state.error) {
            return <div data-testid="error-boundary">{this.state.error.message}</div>
        }
        return this.props.children
    }
}
