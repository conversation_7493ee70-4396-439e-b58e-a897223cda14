package userHandler

import (
	"dng-module/config"
	"dng-module/internal/app/userService"
	"dng-module/internal/model"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v4"
)

// GetAllDomainsHandler godoc
// @Summary Get all domains
// @Description Retrieve all available domains
// @Tags users
// @Accept json
// @Produce json
// @Success 200 {array} DomainModel "Successfully retrieved domains"
// @Failure 500 {object} map[string]string "Internal server error"
// @Router /api/domains [post]
func GetAllDomainsHandler(c *gin.Context) {
	// Extract user claims from context
	claimsRaw, exists := c.Get("claims")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Missing authentication claims"})
		return
	}

	claims, ok := claimsRaw.(jwt.MapClaims)
	if !ok {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid claims structure"})
		return
	}

	// Extract UPN (email) from claims
	upnVal, ok := claims["upn"]
	if !ok {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "UPN not found in token"})
		return
	}

	userEmail, ok := upnVal.(string)
	if !ok {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "UPN in token is not a valid string"})
		return
	}

	// Fetch domains based on user's role access
	domains, err := userService.GetAllDomains(config.DB, userEmail)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"status":  "error",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"status":  "success",
		"message": "Domains fetched successfully",
		"data":    domains,
	})
}

// PostUserUpsertHandler godoc
// @Summary Upsert user
// @Description Insert or update a user record
// @Tags users
// @Accept json
// @Produce json
// @Param request body model.UserReq true "User upsert request"
// @Success 200 {object} map[string]interface{} "User record inserted successfully"
// @Failure 400 {object} map[string]string "Invalid request body"
// @Failure 500 {object} map[string]string "Internal server error"
// @Router /api/user/upsert [post]
func PostUserUpsertHandler(c *gin.Context) {
	var req model.UserReq

	// Bind and validate request body
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request body",
			"message": err.Error(),
		})
		return
	}
	user, err := userService.UserUpsert(config.DB, req.UserEmail, req.AdGroups, req.FirstName, req.LastName)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"status": "error", "message": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "User record inserted successfully.", "data": user})
}
