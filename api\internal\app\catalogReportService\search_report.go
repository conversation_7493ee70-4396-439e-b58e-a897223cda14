package catalogReportService

import (
	"dng-module/internal/model"
	"dng-module/internal/utils"
	"fmt"
	"strings"

	"gorm.io/gorm"
)

func GetSearchOptions(db *gorm.DB, req model.GetSearchOptionsRequest) ([]model.ReportSearchOption, error) {
	var user model.User
	err := db.Select("email").Where("id = ?", req.UserID).First(&user).Error
	if err != nil {
		user.Email = fmt.Sprintf("UserID:%d", req.UserID)
	}

	utils.Logger.Infof("Report search initiated by user: %s, query: '%s'", user.Email, req.Query)

	// Step 1: Get accessible domain IDs
	var domainIDs []int
	query := db.Table("user_domain_access AS uda").
		Select("uda.domain_id").
		Joins("JOIN roles r ON r.id = uda.role_id").
		Where("uda.user_id = ? AND r.can_view = ?", req.UserID, true)

	if len(req.RoleName) > 0 {
		query = query.Where("r.role_name IN (?)", req.RoleName)
	}

	err = query.Pluck("uda.domain_id", &domainIDs).Error
	if err != nil {
		utils.Logger.Errorf("Access domain fetch failed for user %s: %v", user.Email, err)
		return nil, fmt.Errorf("failed to fetch accessible domains: %w", err)
	}

	if len(domainIDs) == 0 {
		return []model.ReportSearchOption{}, nil
	}

	// Step 2: Search reports
	searchPattern := "%" + strings.ToLower(req.Query) + "%"
	var reports []model.Report
	err = db.Where("domain_id IN ?", domainIDs).
		Where("LOWER(name) ILIKE ? OR LOWER(category) ILIKE ? OR LOWER(source_system) ILIKE ?",
			searchPattern, searchPattern, searchPattern).
		Preload("Domain").
		Find(&reports).Error
	if err != nil {
		utils.Logger.Errorf("Report fetch failed for user %s: %v", user.Email, err)
		return nil, fmt.Errorf("failed to fetch reports: %w", err)
	}

	// Step 3: Build response
	options := make([]model.ReportSearchOption, len(reports))
	for i, rep := range reports {
		options[i] = model.ReportSearchOption{
			ID:           rep.ID,
			Name:         rep.Name,
			Category:     rep.Category,
			DomainName:   rep.Domain.Name,
			SourceSystem: rep.SourceSystem,
		}
	}

	utils.Logger.Infof("Search complete for user %s: %d results for query '%s'",
		user.Email, len(options), req.Query)

	return options, nil
}
