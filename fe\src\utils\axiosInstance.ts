// axiosInstance.js
import axios, { AxiosRequestConfig, AxiosResponse } from 'axios'
import { getSession } from 'next-auth/react'
import { handleHttpError } from './errorHandler'

// Custom event name for API status changes
export const API_STATUS_EVENT = 'api-status-change'

const axiosInstance = axios.create({
    baseURL: process.env.NEXTAUTH_URL,
    headers: {
        'Content-Type': 'application/json',
    },
})

let cachedToken: string | null = null
let lastFetched: number | null = null
const CACHE_TTL = 5 * 60 * 1000 // 5 minutes

axiosInstance.interceptors.request.use(
    async (config) => {
        const now = Date.now()
        if (!cachedToken || !lastFetched || now - lastFetched > CACHE_TTL) {
            const session = await getSession()
            cachedToken = session?.user?.access_token ?? null
            lastFetched = now
        }
        if (cachedToken) {
            config.headers.Authorization = `Bearer ${cachedToken}`
        }
        return config
    },
    (error) => Promise.reject(new Error(error)),
)

const findError = (error: any) => {
    const apiStatus = error?.response?.status

    if (!apiStatus) {
        return 'Network error or no response received.'
    }

    if (error?.response?.data?.message) {
        return error.response.data?.message
    }
    if (error?.response?.data?.details) {
        return error.response.data?.details
    }

    switch (apiStatus) {
        case 400:
            return 'Bad request.'
        case 401:
            return 'Unauthenticated user.'
        case 403:
            return "You don't have permission to access the request."
        case 404:
            return 'API url not found.'
        case 503:
            return 'Service Unavailable.'
        default:
            return 'Something went wrong.'
    }
}
// Add a response interceptor for successful responses
axiosInstance.interceptors.response.use(
    (response: AxiosResponse) => response,
    async (error) => {
        const status = error?.response?.status
        const message = error?.response?.data?.message ?? findError(error)
        const data = error?.response?.data ?? {}

        // Helper to dispatch API status event
        function dispatchApiStatusEvent(status: number | undefined) {
            if (typeof window !== 'undefined') {
                const event = new CustomEvent(API_STATUS_EVENT, {
                    detail: {
                        isAvailable: false,
                        timestamp: new Date(),
                        status,
                    },
                })
                window.dispatchEvent(event)
            }
        }

        // Helper to handle error pages
        function handleErrorPage(status: number | undefined, skipErrorHandling: boolean | undefined) {
            if (typeof window !== 'undefined' && !skipErrorHandling) {
                handleHttpError(status ?? 0)
            }
        }

        // Dispatch API status event for server errors
        if (status === 500 || status === 503 || !status) {
            dispatchApiStatusEvent(status)
        }

        // If 401, try to refresh session and retry ONCE
        if (status === 401 && !error.config?._retry) {
            error.config._retry = true
            try {
                await fetch('/api/auth/session?update', { method: 'GET' })
                const session = await getSession()
                cachedToken = session?.user?.access_token ?? null
                lastFetched = Date.now()
                if (cachedToken) {
                    error.config.headers = error.config.headers ?? {}
                    error.config.headers.Authorization = `Bearer ${cachedToken}`
                }
                return axiosInstance(error.config)
            } catch (refreshErr) {
                // If refresh fails, fall through to error
                // eslint-disable-next-line no-console
                console.log('Error refreshing session:', refreshErr)
                handleErrorPage(401, error.config?.skipErrorHandling)
            }
        } else if ([403, 404, 500].includes(status)) {
            handleErrorPage(status, error.config?.skipErrorHandling)
        }

        const err = new Error(message) as Error & {
            status?: number
            config?: any
            custom?: boolean
            data?: any
        }

        err.status = status
        err.config = error?.config
        err.custom = true
        err.data = data

        return Promise.reject(err)
    },
)

// Generic function for making GET requests
export const getRequest = async <T>(
    url: string,
    config?: AxiosRequestConfig,
): Promise<{ data: T; status: number; statusText: string; message?: string }> => {
    const response = await axiosInstance.get<T & { message?: string }>(url, config)

    return {
        data: response.data,
        status: response.status,
        statusText: response.statusText,
        message: (response.data as any)?.message, // safely pull message if present
    }
}

// Generic function for making POST requests (returns data, status, and statusText)
export const postRequest = async <T>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig,
): Promise<{ data: T; status: number; statusText: string; message?: string }> => {
    const response = await axiosInstance.post<T & { message?: string }>(url, data, config)
    return {
        data: response.data,
        status: response.status,
        statusText: response.statusText,
        message: (response.data as any)?.message, // safely pull message if present
    }
}

// Generic function for making PUT requests (returns data, status, and statusText)
export const putRequest = async <T>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig,
): Promise<{ data: T; status: number; statusText: string; message?: string }> => {
    const response = await axiosInstance.put<T & { message?: string }>(url, data, config)
    return {
        data: response.data,
        status: response.status,
        statusText: response.statusText,
        message: (response.data as any)?.message, // safely pull message if present
    }
}

export const deleteRequest = async <T>(url: string, config?: AxiosRequestConfig): Promise<T> => {
    const response = await axiosInstance.delete<T>(url, config)
    return response.data
}

export default axiosInstance
