import { Box, CircularProgress, LinearProgress, Skeleton } from '@mui/material'

interface LoaderProps {
    type?: 'circular' | 'linear' | 'skeleton'
    size?: number
    color?: 'primary' | 'secondary' | 'inherit' | 'error' | 'success' | 'warning' | 'info'
    thickness?: number
    width?: number | string
    height?: number | string
    animation?: 'pulse' | 'wave' | false
}

export default function Loader({
    type = 'circular',
    size = 40,
    color = 'info',
    thickness = 4,
    width = '100%',
    height = 20,
    animation = 'wave',
}: Readonly<LoaderProps>): JSX.Element {
    if (type === 'linear') {
        return <LinearProgress color={color} style={{ width }} />
    }

    if (type === 'skeleton') {
        return <Skeleton variant="rectangular" width={width} height={height} animation={animation} />
    }

    // Default Circular Loader
    return (
        <Box sx={{ textAlign: 'center' }}>
            <CircularProgress size={size} color={color} thickness={thickness} />
        </Box>
    )
}
