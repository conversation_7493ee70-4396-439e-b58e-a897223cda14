'use client'
import { Box, Card, Typography } from '@mui/material'
import Grid from '@mui/material/Grid2'
// components
import PageContainer from '@/components/common/layout/container/PageContainer'
import { Logo } from '@/components/common/layout/shared/logo/logo'
//import loginBg from '../../../../public/images/backgrounds/bg-image.jpg'
import AuthLogin from './auth-login'

const Login = () => {
    return (
        <PageContainer title="Login" description="this is Login page">
            <Box
                sx={{
                    position: 'relative',
                    '&:before': {
                        content: '""',
                        background: `url('/images/backgrounds/bg-image.jpg') no-repeat center center`,
                        backgroundSize: 'cover', // Ensures the image scales responsively
                        backgroundPosition: 'center',
                        position: 'absolute',
                        height: '100%',
                        width: '100%',
                        zIndex: 0,
                    },
                }}
            >
                <Grid container alignItems="center" sx={{ height: '100vh', position: 'relative', zIndex: 1 }}>
                    <Grid size={{ xs: 12, md: 6, lg: 4 }} offset={{ xs: 0, lg: 2 }}>
                        <Typography variant="h1" fontSize={50} lineHeight={1} color="#fff" noWrap>
                            DNG Enterprise
                        </Typography>
                        <Typography fontSize={28} color="#fff" mt={2} gutterBottom>
                            Self-Service dashboards to
                        </Typography>
                        <Typography fontSize={28} fontWeight="bold" color="#fff">
                            optimize your results!
                        </Typography>
                    </Grid>
                    <Grid
                        size={{ xs: 12, md: 6, lg: 4, xl: 3 }}
                        display="flex"
                        justifyContent="center" // Centers card for smaller screens
                        alignItems="center"
                        sx={{ px: { xs: 2, sm: 4 } }} // Adds padding for smaller screens
                        offset={{ xs: 0, lg: 2 }}
                    >
                        <Card
                            elevation={4}
                            sx={{
                                p: 4,
                                zIndex: 2,
                                width: '100%',
                                maxWidth: '500px',
                                boxShadow: '0px 4px 20px rgba(0, 0, 0, 0.1)', // Subtle shadow for aesthetics
                            }}
                        >
                            <Box display="flex" alignItems="center" justifyContent="center" mb={5}>
                                <Logo />
                            </Box>
                            <AuthLogin />
                        </Card>
                    </Grid>
                </Grid>
            </Box>
        </PageContainer>
    )
}

export default Login
