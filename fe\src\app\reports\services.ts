import { deleteRequest, getRequest, postRequest } from '@/utils/axiosInstance'
import { BACKEND_URL } from '@/utils/constants'
import { Session } from 'next-auth'
import { getSession } from 'next-auth/react'
import { IFilterOptions } from './reportSearch/types'
import { ApiResponse, BulkUploadResponse, Domain, IReportSearchOption, Report, ReportMetaResponse } from './types'

export const getAllDomainsService = async () => {
    const { data } = await postRequest<{ data: Domain[] }>(`${BACKEND_URL}/domains`)
    return data
}

export const upsertReport = async (form: Omit<Report, 'id'> & { id?: string }) => {
    const response = await postRequest(`${BACKEND_URL}/reports/upsert`, form)
    return response.status
}

export const deleteReportsService = (user_id: string, report_ids: number[]) => {
    return deleteRequest(`${BACKEND_URL}/reports`, {
        data: {
            user_id,
            report_ids,
        },
        headers: {
            'Content-Type': 'application/json',
        },
    })
}

export const downloadPBIService = async (reportId: string) =>
    postRequest('api/reports/export-pbi', { reportId, format: 'PDF' })

export async function lookerPDFApi(dashboard_id: string): Promise<Blob> {
    const response = await postRequest<Blob>(
        `${BACKEND_URL}/reports/looker/render-pdf`,
        { dashboard_id },
        { responseType: 'blob' },
    )

    if (response.status !== 200) {
        throw new Error(`Failed to fetch PDF: ${response.statusText || 'Unknown error'}`)
    }

    return response.data
}

export const updatePreferenceService = async (
    report_id: string,
    is_preference: boolean,
    view: 'favorites' | 'saved-later',
    session: Session | null,
): Promise<number> => {
    const user_id = session?.user.user_id as string

    const payload = {
        user_id,
        report_id,
        view,
        ...(view === 'favorites' && { is_favorite: is_preference }),
        ...(view === 'saved-later' && { is_saved: is_preference }),
    }

    const { status } = await postRequest(`${BACKEND_URL}/reports/user-preferences`, payload)
    return status
}

export async function getSearchOptions(query: string, session: Session | null): Promise<IReportSearchOption[]> {
    try {
        const { data } = await postRequest<IReportSearchOption[]>(`${BACKEND_URL}/reports/search`, {
            user_id: session?.user?.user_id,
            role_name: session?.user?.role_name,
            query,
        })
        return data
    } catch (error) {
        console.error('Error fetching search options:', error)
        return []
    }
}

export async function bulkUploadService(formData: FormData): Promise<BulkUploadResponse> {
    const { data } = await postRequest<BulkUploadResponse>(`${BACKEND_URL}/reports/bulk-insert`, formData, {
        headers: { 'Content-Type': 'multipart/form-data' },
    })
    return data
}

export const pbiEmbedTokenService = async (reportId: string, groupId: string): Promise<string> => {
    const { data } = await getRequest<{
        data: string
    }>(`${BACKEND_URL}/reports/pbi/embed-token?report_id=${reportId}&group_id=${groupId}`)

    if (!data?.data) {
        throw new Error('Embed token not found in response')
    }

    return data.data
}

export const lookerEmbedUrlService = async (dashboard_id: string): Promise<string> => {
    const { data } = await getRequest<{
        data: { url: string }
    }>(`${BACKEND_URL}/reports/looker/looker-embed`, { params: { dashboard_id } })

    if (!data?.data?.url) {
        throw new Error('URL not found in response')
    }

    return data.data.url
}

export const getFilterOptionsService = async (): Promise<IFilterOptions> => {
    try {
        const { data } = await getRequest<IFilterOptions>(`${BACKEND_URL}/reports/filter-options`)
        return data
    } catch (error) {
        console.error('Error fetching filter options:', error)
        return {
            category: [],
            domains: [],
            source_system: [],
        }
    }
}

export async function bulkInsertErrorsService(filePath: string): Promise<Blob> {
    try {
        const response = await getRequest<Blob>(`${BACKEND_URL}/reports/bulk-insert-errors`, {
            params: { filePath },
            responseType: 'blob',
        })

        if (response.status !== 200) {
            return Promise.reject(new Error(`Failed to download file: ${response.statusText || 'Unknown error'}`))
        }

        return response.data
    } catch (error) {
        throw new Error(`Download Error: ${(error as Error).message}`)
    }
}

export async function bulkInsertDownloadService(): Promise<Blob> {
    try {
        const response = await getRequest<Blob>(`${BACKEND_URL}/reports/download-template`, {
            responseType: 'blob',
        })

        if (response.status !== 200) {
            return Promise.reject(new Error(`Failed to download template: ${response.statusText || 'Unknown error'}`))
        }

        return response.data
    } catch (error) {
        throw new Error(`Download Template Error: ${(error as Error).message}`)
    }
}

export interface GetReportsParams {
    page: number
    size: number
    view: string
    sort: string
    order: string
    search?: string
    filters?: Record<string, string>
    domain?: string
    source_system?: string
    category?: string
    start_date?: string
    end_date?: string
}

export const getReports = async (params: GetReportsParams): Promise<ApiResponse> => {
    const session = await getSession()
    const user = session?.user
    const query = new URLSearchParams()

    Object.entries({
        page: params.page.toString(),
        size: params.size.toString(),
        view: params.view,
        sort: params.sort,
        order: params.order,
        search: params.search,
        domain: params.domain,
        source_system: params.source_system,
        category: params.category,
        start_date: params.start_date,
        end_date: params.end_date,
    }).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '' && value !== '-') {
            query.set(key, value)
        }
    })

    // If you want to include filters also in query:
    if (params.filters) {
        Object.entries(params.filters).forEach(([key, value]) => {
            query.append(`filters[${key}]`, value) // nest filters in URL
        })
    }

    const url = `${BACKEND_URL}/reports?${query.toString()}`

    const { data } = await postRequest<ApiResponse>(url, {
        user_id: user?.user_id,
        role_name: user?.role_name,
    })

    return {
        data: data?.data || [],
        meta: data?.meta,
    }
}

export const getPBIReportMetaData = async (reportId: string, groupId: string): Promise<ReportMetaResponse> => {
    const data = await getRequest<ReportMetaResponse>(
        `${BACKEND_URL}/reports/pbi/metadata?report_id=${reportId}&group_id=${groupId}`,
    )

    if (!data?.data) {
        throw new Error('Embed token not found in response')
    }

    return data.data
}

export const getLookerReportMetaData = async (dashboardId: string): Promise<ReportMetaResponse> => {
    const data = await getRequest<ReportMetaResponse>(
        `${BACKEND_URL}/reports/looker/metadata?dashboard_id=${dashboardId}`,
    )

    if (!data?.data) {
        throw new Error('Embed token not found in response')
    }

    return data.data
}
