package userService

import (
	// "dng-module/internal/app/userService"
	"fmt"
	"regexp"
	"testing"

	// "dng-module/testing/utilsTest"
	"github.com/DATA-DOG/go-sqlmock"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

func setupMockDB(t *testing.T) (*gorm.DB, sqlmock.Sqlmock) {
	db, mock, err := sqlmock.New(sqlmock.MonitorPingsOption(true))
	if err != nil {
		t.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
	}

	// ✅ Mock the Ping call (required by GORM)
	mock.ExpectPing()

	dialector := postgres.New(postgres.Config{
		Conn:       db,
		DriverName: "postgres",
	})

	gormDB, err := gorm.Open(dialector, &gorm.Config{})
	if err != nil {
		t.Fatalf("failed to open gorm db: %v", err)
	}

	return gormDB, mock
}
func TestUserUpsert(t *testing.T) {
	validEmail := "<EMAIL>"
	validGroups := []string{"group1", "group2"}
	validFirstName := "John"
	validLastName := "Doe"

	tests := []struct {
		name        string
		email       string
		adGroups    []string
		firstName   string
		lastName    string
		mockClosure func(sqlmock.Sqlmock)
		want        UserRoleResponse
		wantErr     bool
	}{
		{
			name:      "user not found returns error",
			email:     validEmail,
			adGroups:  validGroups,
			firstName: validFirstName,
			lastName:  validLastName,
			mockClosure: func(mock sqlmock.Sqlmock) {
				// Expect transaction begin
				mock.ExpectBegin()

				// User lookup returns ErrRecordNotFound inside transaction
				mock.ExpectQuery(regexp.QuoteMeta(
					`SELECT * FROM "users" WHERE email = $1 AND ("users"."email" = $2 AND "users"."first_name" = $3 AND "users"."last_name" = $4) ORDER BY "users"."id" LIMIT $5`)).
					WithArgs(validEmail, validEmail, validFirstName, validLastName, 1).
					WillReturnError(gorm.ErrRecordNotFound)

				// Expect rollback because of error
				mock.ExpectRollback()
			},
			want:    UserRoleResponse{},
			wantErr: true,
		},
		{
			name:      "successful existing user update",
			email:     validEmail,
			adGroups:  validGroups,
			firstName: validFirstName,
			lastName:  validLastName,
			mockClosure: func(mock sqlmock.Sqlmock) {
				mock.ExpectBegin()

				userRows := sqlmock.NewRows([]string{"id", "email", "first_name", "last_name"}).
					AddRow(1, validEmail, validFirstName, validLastName)
				mock.ExpectQuery(regexp.QuoteMeta(
					`SELECT * FROM "users" WHERE email = $1 AND ("users"."email" = $2 AND "users"."first_name" = $3 AND "users"."last_name" = $4) ORDER BY "users"."id" LIMIT $5`)).
					WithArgs(validEmail, validEmail, validFirstName, validLastName, 1).
					WillReturnRows(userRows)

				roleMappingRows := sqlmock.NewRows([]string{"id", "pattern", "domain_pattern", "role_id"}).
					AddRow(1, "group1", "example", 1)
				mock.ExpectQuery(regexp.QuoteMeta(`SELECT * FROM "ad_group_role_mapping"`)).
					WillReturnRows(roleMappingRows)

				roleRows := sqlmock.NewRows([]string{"id", "role_name", "can_view", "can_edit", "is_super_admin"}).
					AddRow(1, "Admin", true, true, false)
				// Fix here: expect '=' not 'IN'
				mock.ExpectQuery(regexp.QuoteMeta(`SELECT * FROM "roles" WHERE "roles"."id" = $1`)).
					WithArgs(1).
					WillReturnRows(roleRows)

				domainRows := sqlmock.NewRows([]string{"id", "name"}).
					AddRow(1, "example.com").
					AddRow(2, "test.org")
				mock.ExpectQuery(regexp.QuoteMeta(`SELECT * FROM "domains"`)).
					WillReturnRows(domainRows)

				mock.ExpectExec(regexp.QuoteMeta(`DELETE FROM "user_domain_access" WHERE user_id = $1`)).
					WithArgs(1).
					WillReturnResult(sqlmock.NewResult(0, 0))

				mock.ExpectQuery(`INSERT INTO "user_domain_access"`).
					WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg()).
					WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))

				mock.ExpectCommit()

				accessRows := sqlmock.NewRows([]string{"domain_id", "domain_name", "role_id", "role_name", "can_view", "can_edit", "is_super_admin"}).
					AddRow(1, "example.com", 1, "Admin", true, true, false)
				mock.ExpectQuery(regexp.QuoteMeta(
					`SELECT uda.domain_id, d.name AS domain_name, r.id AS role_id, r.role_name AS role_name, r.can_view, r.can_edit, r.is_super_admin FROM user_domain_access AS uda JOIN domains d ON d.id = uda.domain_id JOIN roles r ON r.id = uda.role_id WHERE uda.user_id = $1`)).
					WithArgs(1).
					WillReturnRows(accessRows)
			},
			want: UserRoleResponse{
				UserID:          1,
				Email:           validEmail,
				PrimaryRoleName: "Admin",
				Roles: []DomainRole{
					{
						DomainID:   1,
						DomainName: "example.com",
						RoleID:     1,
						RoleName:   "Admin",
					},
				},
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			db, mock := setupMockDB(t)
			tt.mockClosure(mock)

			got, err := UserUpsert(db, tt.email, tt.adGroups, tt.firstName, tt.lastName)
			if (err != nil) != tt.wantErr {
				t.Errorf("UserUpsert() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if !tt.wantErr {
				if got.Email != tt.want.Email {
					t.Errorf("UserUpsert() email = %v, want %v", got.Email, tt.want.Email)
				}
				if got.PrimaryRoleName != tt.want.PrimaryRoleName {
					t.Errorf("UserUpsert() primary role = %v, want %v", got.PrimaryRoleName, tt.want.PrimaryRoleName)
				}
				if len(got.Roles) != len(tt.want.Roles) {
					t.Errorf("UserUpsert() roles count = %v, want %v", len(got.Roles), len(tt.want.Roles))
				}
			}

			if err := mock.ExpectationsWereMet(); err != nil {
				t.Errorf("there were unfulfilled expectations: %s", err)
			}
		})
	}
}

func TestUserUpsertAdditionalCases(t *testing.T) {
	validEmail := "<EMAIL>"
	validGroups := []string{"group1", "group2"}
	validFirstName := "John"
	validLastName := "Doe"

	tests := []struct {
		name        string
		email       string
		adGroups    []string
		firstName   string
		lastName    string
		mockClosure func(sqlmock.Sqlmock)
		want        UserRoleResponse
		wantErr     bool
	}{
		{
			name:      "transaction begin failure",
			email:     validEmail,
			adGroups:  validGroups,
			firstName: validFirstName,
			lastName:  validLastName,
			mockClosure: func(mock sqlmock.Sqlmock) {
				mock.ExpectBegin().WillReturnError(fmt.Errorf("transaction begin error"))
			},
			want:    UserRoleResponse{},
			wantErr: true,
		},
		{
			name:      "no matching AD groups",
			email:     validEmail,
			adGroups:  []string{"nonexistent-group"},
			firstName: validFirstName,
			lastName:  validLastName,
			mockClosure: func(mock sqlmock.Sqlmock) {
				mock.ExpectBegin()

				userRows := sqlmock.NewRows([]string{"id", "email", "first_name", "last_name"}).
					AddRow(1, validEmail, validFirstName, validLastName)
				mock.ExpectQuery(regexp.QuoteMeta(
					`SELECT * FROM "users" WHERE email = $1 AND ("users"."email" = $2 AND "users"."first_name" = $3 AND "users"."last_name" = $4) ORDER BY "users"."id" LIMIT $5`)).
					WithArgs(validEmail, validEmail, validFirstName, validLastName, 1).
					WillReturnRows(userRows)

				mock.ExpectQuery(regexp.QuoteMeta(`SELECT * FROM "ad_group_role_mapping"`)).
					WillReturnRows(sqlmock.NewRows([]string{"id", "pattern", "domain_pattern", "role_id"}))

				mock.ExpectRollback()
			},
			want:    UserRoleResponse{},
			wantErr: true,
		},
		{
			name:      "error while resetting previous access settings",
			email:     validEmail,
			adGroups:  validGroups,
			firstName: validFirstName,
			lastName:  validLastName,
			mockClosure: func(mock sqlmock.Sqlmock) {
				mock.ExpectBegin()

				userRows := sqlmock.NewRows([]string{"id", "email", "first_name", "last_name"}).
					AddRow(1, validEmail, validFirstName, validLastName)
				mock.ExpectQuery(regexp.QuoteMeta(
					`SELECT * FROM "users" WHERE email = $1 AND ("users"."email" = $2 AND "users"."first_name" = $3 AND "users"."last_name" = $4) ORDER BY "users"."id" LIMIT $5`)).
					WithArgs(validEmail, validEmail, validFirstName, validLastName, 1).
					WillReturnRows(userRows)

				mock.ExpectQuery(regexp.QuoteMeta(`SELECT * FROM "ad_group_role_mapping"`)).
					WillReturnRows(
						sqlmock.NewRows([]string{"id", "pattern", "domain_pattern", "role_id"}).
							AddRow(1, "group1", "domain1", 2).
							AddRow(2, "group2", "domain2", 3),
					)

				mock.ExpectQuery(regexp.QuoteMeta(`SELECT * FROM "roles" WHERE "roles"."id" IN ($1,$2)`)).
					WithArgs(2, 3).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name", "can_view", "can_edit", "is_super_admin"}).
						AddRow(2, "role2", true, false, false).
						AddRow(3, "role3", true, true, false),
					)

				mock.ExpectQuery(regexp.QuoteMeta(`SELECT * FROM "domains"`)).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).
						AddRow(1, "example.com").
						AddRow(2, "test.org"))

				mock.ExpectExec(regexp.QuoteMeta(`DELETE FROM "user_domain_access" WHERE user_id = $1`)).
					WithArgs(1).
					WillReturnError(fmt.Errorf("delete error"))

				mock.ExpectRollback()
			},
			want:    UserRoleResponse{},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			db, mock := setupMockDB(t)
			tt.mockClosure(mock)

			got, err := UserUpsert(db, tt.email, tt.adGroups, tt.firstName, tt.lastName)
			fmt.Println(got)
			if (err != nil) != tt.wantErr {
				t.Errorf("UserUpsert() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if err := mock.ExpectationsWereMet(); err != nil {
				t.Errorf("there were unfulfilled expectations: %s", err)
			}
		})
	}
}
