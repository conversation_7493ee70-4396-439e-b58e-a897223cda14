'use client'
import React, { useState } from 'react'
import dynamic from 'next/dynamic'
import { Box, Typography, Paper, ToggleButtonGroup, ToggleButton } from '@mui/material'
import GenericChart from './GenericChart'
import dcTrendsData from './data/dcSpecificTrends.json'

type ChartType = 'accuracy' | 'zones'

interface DCTrendsData {
    accuracyOptions: any
    zoneOptions: any
    accuracySeries: any[]
    zoneSeries: any[]
}

const DCSpecificTrendsChart: React.FC = () => {
    const [chartType, setChartType] = useState<ChartType>('accuracy')

    // Use imported data with type assertion
    const { accuracyOptions, zoneOptions, accuracySeries, zoneSeries } = dcTrendsData as DCTrendsData

    const handleChartTypeChange = (event: React.MouseEvent<HTMLElement>, newType: ChartType | null) => {
        if (newType !== null) {
            setChartType(newType)
        }
    }

    return (
        <Paper
            elevation={0}
            sx={{
                p: 2,
                borderRadius: 3,
                border: '1px solid',
                borderColor: 'divider',
                backgroundColor: 'white',
                mb: 3,
            }}
        >
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Box>
                    <Typography variant="h6" gutterBottom sx={{ fontWeight: 'bold', color: '#424242' }}>
                        {chartType === 'accuracy'
                            ? 'DC Audit Accuracy Trends (6-Month)'
                            : 'Janesville DC Zone Performance'}
                    </Typography>
                    <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                        {chartType === 'accuracy'
                            ? 'Comparing audit accuracy trends across distribution centers'
                            : 'Breakdown of performance metrics by zone in Janesville DC'}
                    </Typography>
                </Box>
                <ToggleButtonGroup
                    value={chartType}
                    exclusive
                    onChange={handleChartTypeChange}
                    size="small"
                    sx={{ height: 36 }}
                >
                    <ToggleButton value="accuracy" sx={{ textTransform: 'none' }}>
                        DC Trends
                    </ToggleButton>
                    <ToggleButton value="zones" sx={{ textTransform: 'none' }}>
                        Zone Breakdown
                    </ToggleButton>
                </ToggleButtonGroup>
            </Box>
            <Box sx={{ height: 450 }}>
                <GenericChart
                    options={chartType === 'accuracy' ? accuracyOptions : zoneOptions}
                    series={chartType === 'accuracy' ? accuracySeries : zoneSeries}
                    type={chartType === 'accuracy' ? 'line' : 'bar'}
                    height="100%"
                    width="100%"
                />
            </Box>
            <Box sx={{ mt: 2, p: 1, borderTop: '1px solid #f0f0f0' }}>
                <Typography variant="body2" sx={{ color: '#000', fontSize: '13px', fontWeight: 'bold', mb: 1 }}>
                    Key Insights:
                </Typography>
                <Box component="ul" sx={{ m: 0, pl: 2, color: 'text.secondary', fontSize: '12px' }}>
                    {chartType === 'accuracy' ? (
                        <>
                            <li>Janesville DC shows steady improvement over 6 months (+1.8 percentage points)</li>
                            <li>Memphis DC has a concerning downward trend (-1.9 percentage points)</li>
                            <li>All DCs remain below the 97% target accuracy rate</li>
                        </>
                    ) : (
                        <>
                            <li>Zone D (Packing) has significantly lower accuracy (78.0%) than other zones</li>
                            <li>Zone C (Picking) has the highest productivity at 91.3 cartons per labor hour</li>
                            <li>Zone D&apos;s issues are primarily due to high staff turnover (43% annually)</li>
                        </>
                    )}
                </Box>
            </Box>
        </Paper>
    )
}

export default DCSpecificTrendsChart
