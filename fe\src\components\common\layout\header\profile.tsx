import { SUPER_ADMIN } from '@/utils/constants'
import { checkRole } from '@/utils/helper'
import { AddAlertSharp, ArrowDropDownCircleOutlined } from '@mui/icons-material'
import { Avatar, Box, Button, IconButton, ListItemIcon, ListItemText, Menu, MenuItem, Typography } from '@mui/material'
import { signOut, useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import React, { useState } from 'react'

const Profile = () => {
    const [anchorEl2, setAnchorEl2] = useState<null | HTMLElement>(null)
    const { data: session } = useSession()
    const user = session?.user
    const router = useRouter()

    const isSuperAdmin = checkRole(user, SUPER_ADMIN)

    const userRole = user?.role_name ?? 'Unknown'
    const imgUrl = user?.image ?? ''

    const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
        setAnchorEl2(event.currentTarget)
    }

    const handleMenuClose = () => {
        setAnchorEl2(null) // Close the menu
    }

    const handleLogout = async () => {
        await signOut({ callbackUrl: '/', redirect: true }).then(() => {
            console.log('Logged out.')
        })
    }

    return (
        <Box display="flex" alignItems="center" gap={1}>
            <IconButton
                size="small"
                color="inherit"
                //onClick={handleMenuOpen}
                aria-controls="profile-menu"
                aria-haspopup="true"
                sx={{
                    ...(Boolean(anchorEl2) && {
                        color: 'primary.main',
                    }),
                }}
            >
                <Avatar
                    src={imgUrl}
                    alt="profile image"
                    sx={{
                        width: 35,
                        height: 35,
                    }}
                />
            </IconButton>
            {/* Profile email */}
            <Box>
                <Typography variant="body2" sx={{ fontWeight: 'bold' }} color={'text.primary'}>
                    {user?.name}
                </Typography>
                <Typography variant="caption">{userRole}</Typography>
            </Box>

            {/* Arrow icon for dropdown */}
            <IconButton
                size="small"
                color="inherit"
                onClick={handleMenuOpen}
                aria-controls="profile-menu"
                aria-haspopup="true"
            >
                <ArrowDropDownCircleOutlined />
            </IconButton>

            {/* Profile Dropdown Menu */}
            <Menu
                id="profile-menu"
                anchorEl={anchorEl2}
                keepMounted
                open={Boolean(anchorEl2)}
                onClose={handleMenuClose} // This ensures the menu closes on outside clicks
                anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
                transformOrigin={{ horizontal: 'right', vertical: 'top' }}
                sx={{
                    '& .MuiMenu-paper': {
                        width: '200px',
                    },
                }}
            >
                {isSuperAdmin && (
                    <MenuItem onClick={() => router.push('alerts')} sx={{ cursor: 'pointer', bgColor: 'primary.main' }}>
                        <ListItemIcon>
                            <AddAlertSharp width={20} color="error" />
                        </ListItemIcon>
                        <ListItemText>Manage Alerts</ListItemText>
                    </MenuItem>
                )}
                <Box mt={1} py={1} px={2}>
                    <Button variant="contained" color="primary" onClick={handleLogout} fullWidth>
                        Logout
                    </Button>
                </Box>
            </Menu>
        </Box>
    )
}

export default Profile
