import {DefaultSession} from 'next-auth'

declare module 'next-auth' {
    /**
     * Returned by `useSession`, `getSession` and received as a prop on the `SessionProvider` React Context
     */
    interface Profile {
        roles?: string[] // Add the 'roles' property
    }

    interface User {
        name: string
        apiToken?: string
        accessTokenExpires?: string

        access_token?: string
        refresh_token?: string
        user_id: string;
        role_id: number;
        role_name: string;
        priority: number;
        roles: { role_id: string; role: string }[],
    }

    interface Session {
        user: User & DefaultSession['user']
    }
}
