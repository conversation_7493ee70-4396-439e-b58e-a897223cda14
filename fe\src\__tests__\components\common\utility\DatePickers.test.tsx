import { render, screen } from '@testing-library/react'
import { SingleDatePicker, DateRangePicker, SingleDateTimePicker } from '@/components/common/utility/DatePicker'
import '@testing-library/jest-dom'

describe('Date Picker Components', () => {
    test('renders SingleDatePicker with label and button input', () => {
        render(
            <SingleDatePicker
                value={null}
                onChange={() => {}}
                label="Single Date"
            />
        )
        expect(screen.getByText('Single Date')).toBeInTheDocument()
        const buttonInput = screen.getByRole('button', { name: /Single Date/i })
        expect(buttonInput).toBeInTheDocument()
    })

    test('renders SingleDateTimePicker with label and button input', () => {
        render(
            <SingleDateTimePicker
                value={null}
                onChange={() => {}}
                label="Date & Time"
            />
        )
        expect(screen.getByText('Date & Time')).toBeInTheDocument()
        const buttonInput = screen.getByRole('button', { name: /Date & Time/i })
        expect(buttonInput).toBeInTheDocument()
    })

    test('renders DateRangePicker with label and button input', () => {
        render(
            <DateRangePicker
                value={[null, null]}
                onChange={() => {}}
                label="Select a range"
            />
        )

        expect(screen.getByText('Select a range')).toBeInTheDocument()
        const buttonInput = screen.getByRole('button', { name: /Select a range/i })
        expect(buttonInput).toBeInTheDocument()
    })
})
