import { Topic } from '../../types';
import { dcSpecificTrendsCharts } from '../charts';

// DC-specific Trends Topic
export const dcSpecificTrendsTopic: Topic = {
  id: 2,
  title: 'DC-specific Trends',
  description: 'Detailed analysis of distribution center performance by zone and process',
  fullContent: `Dollar General's DC-specific trends dashboard provides a detailed view of performance metrics for individual distribution centers, broken down by zones and processes. This dashboard enables DC managers to identify specific areas for improvement and track progress over time.

  The data includes audit accuracy trends, labor productivity metrics, error rates by audit type, zone-specific performance indicators, audit frequency compliance, and process-specific metrics for trailer loading and unloading.

  By analyzing DC-specific trends, managers can implement targeted improvements, share best practices across zones, and optimize resource allocation to address the most critical issues.`,
  charts: dcSpecificTrendsCharts
};

// DC-specific Questions and Answers
export const DC_SPECIFIC_QA = [
  {
    keywords: ["janesville", "audit accuracy", "trend", "months"],
    answer: 'The audit accuracy trend for Janesville DC over the past 6 months shows steady improvement from 88.5% in January to 90.3% in June, with a peak of 90.5% in May. This represents a 1.8 percentage point improvement over the period. The improvement is attributed to the implementation of new training protocols and process standardization efforts. While this is positive progress, Janesville is still 6.7 percentage points below our target of 97%, though it is performing better than both Memphis and Oklahoma DCs.'
  },
  {
    keywords: ["compare", "cartons", "labor hour"],
    answer: 'In terms of cartons per labor hour, Janesville DC ranks 3rd in the network at 87.3 cartons/hour, which is 5.2% above the network average of 83.0. The top performer is Atlanta DC at 92.1 cartons/hour, followed by Phoenix at 89.5. Memphis ranks lowest at 76.8 cartons/hour. Janesville has improved its productivity by 3.7% over the past quarter through workflow optimization and targeted training programs.'
  },
  {
    keywords: ["audit type", "highest error", "rate"],
    answer: 'The audit type with the highest error rate in Janesville DC is the "Cycle Count" audit at 14.2% error rate, followed by "Putaway Verification" at 11.8%. "Trailer Load Verification" has the lowest error rate at 6.5%. The high error rate in cycle counts is primarily due to location discrepancies in Zone D, where the error rate reaches 22%. We\'ve initiated a focused improvement program for this zone, including additional training and process modifications.'
  },
  {
    keywords: ["performance", "breakdown", "zone"],
    answer: 'The performance breakdown by zone in Janesville DC shows significant variation:\n\n- Zone A (Receiving): 93.2% accuracy, 88.5 cartons/hour\n- Zone B (Storage): 91.7% accuracy, 85.2 cartons/hour\n- Zone C (Picking): 92.5% accuracy, 91.3 cartons/hour\n- Zone D (Packing): 78.0% accuracy, 84.1 cartons/hour\n- Zone E (Shipping): 94.1% accuracy, 87.4 cartons/hour\n\nZone D is clearly the underperforming area, with accuracy 14.1 percentage points below the next lowest zone. This is primarily due to high staff turnover (43% annually vs. 22% DC average) and inadequate training for new hires.'
  },
  {
    keywords: ["meeting", "audit frequency", "targets"],
    answer: 'Janesville DC is meeting audit frequency targets in 4 out of 5 zones. The compliance rates are:\n\n- Zone A: 97% compliance (target: 95%)\n- Zone B: 98% compliance (target: 95%)\n- Zone C: 96% compliance (target: 95%)\n- Zone D: 82% compliance (target: 95%)\n- Zone E: 99% compliance (target: 95%)\n\nZone D is significantly underperforming in audit frequency, primarily due to staffing shortages and prioritization of throughput over audit compliance during peak periods. We\'ve implemented a new audit scheduling system and added dedicated audit staff to address this issue.'
  },
  {
    keywords: ["processes", "causing", "trailer accuracy", "issues"],
    answer: 'The processes causing the most trailer accuracy issues at Janesville DC are:\n\n1. Load Sequencing (42% of errors): Items are being loaded out of sequence, causing verification issues.\n2. Manifest Reconciliation (27% of errors): Discrepancies between system records and physical counts.\n3. Label Verification (18% of errors): Incorrect or missing labels on cartons.\n4. Damage Documentation (13% of errors): Inadequate documentation of damaged items.\n\nWe\'ve implemented barcode scanning verification at each stage of the loading process and enhanced the manifest reconciliation protocol to address the top two issues, which account for 69% of all trailer accuracy problems.'
  }
];
