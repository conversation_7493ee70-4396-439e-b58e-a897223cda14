FROM harbor.dolgen.net/devops/kit/node/runtime:latest-20
WORKDIR /app

# Arguments Setup
ARG VER
ARG CI_PROJECT_NAME
ARG APP_NAME
ARG PORT_MAPPING
ARG DEPLOY_ENV

# Labels Setup
LABEL MAINTAINER=${CI_PROJECT_NAME}
LABEL dg.app=${CI_PROJECT_NAME}
LABEL dg.version=${VER}
LABEL dg.env=${DEPLOY_ENV}

# Update the OS packages
RUN apk update \
    && apk upgrade 
    
RUN echo Building ${CI_PROJECT_NAME} image for ${DEPLOY_ENV} environment...

COPY . .

RUN mv .env.${DEPLOY_ENV} .tmp.production \
    && rm -f .env* \
    && rm -f .env.* \
    && mv .tmp.production .env.production

RUN node -v \
    && ls -al /app \
    && echo $NODE_ENV


# This part should be used to install your application, which will create the node_modules directory and pull any dependencies
RUN npm install \
#    && npm explain static-eval \
    && npm run build \
# THIS REMOVES UNNECCESSARY DEPENDENT PACKAGES jsonpath and static-eval, and npm which has critical security vulnerabilities    
    && npm uninstall react-scripts -g \
    && npm uninstall npm -g

RUN chmod g+s /app \
  && chmod g+rx /app/* \
  && chown -R container-user:container-user /app

USER container-user
RUN whoami

EXPOSE ${PORT_MAPPING}
RUN echo ${PORT_MAPPING}
ENV PORT_MAPPING=${PORT_MAPPING}
ENV PATH $PATH:/app/node_modules/next/dist/bin

ENTRYPOINT next start -p $PORT_MAPPING
