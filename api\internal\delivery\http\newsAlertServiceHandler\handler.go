package newalertservicehandler

import (
	newsAlertService "dng-module/internal/app/newsAlertServices"
	"dng-module/internal/model"
	"dng-module/internal/utils"
	"strconv"
	"strings"

	"net/http"

	"github.com/gin-gonic/gin"
)

func CreateNewsAlertHandler(c *gin.Context) {
	var alert model.NewsAlert

	// Bind JSON body to alert struct
	if err := c.ShouldBindJSON(&alert); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid input", "details": err.Error()})
		return
	}

	// You could get the logged-in user's email from context here if available
	// alert.CreatedBy = getUserEmailFromContext(c)

	createdAlert, err := newsAlertService.CreateNewsAlert(alert)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create news alert", "details": err.<PERSON>rror()})
		return
	}

	c.<PERSON>(http.StatusCreated, createdAlert)
}

func DeleteNewsAlertsHandler(c *gin.Context) {
	idsParam := c.Query("ids") // e.g., "1,2,3"
	if idsParam == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Missing 'ids' query parameter"})
		return
	}

	idStrings := strings.Split(idsParam, ",")
	var ids []uint
	for _, idStr := range idStrings {
		id, err := strconv.Atoi(strings.TrimSpace(idStr))
		if err != nil || id <= 0 {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID in list: " + idStr})
			return
		}
		ids = append(ids, uint(id))
	}

	err := newsAlertService.DeleteNewsAlerts(ids)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete news alerts", "details": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "News alerts deleted successfully"})
}

func UpdateNewsAlertHandler(c *gin.Context) {
	var dto model.UpdateNewsAlertDTO
	if err := c.ShouldBindJSON(&dto); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request", "details": err.Error()})
		return
	}

	updatedAlert, err := newsAlertService.UpdateNewsAlert(dto)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update news alert", "details": err.Error()})
		return
	}

	c.JSON(http.StatusOK, updatedAlert)
}

func GetNewsAlertsHandler(c *gin.Context) {
	// Parse query parameters
	search := c.Query("search")
	sortBy := c.DefaultQuery("sortBy", "start_date_time")
	order := strings.ToLower(c.DefaultQuery("order", "desc"))
	status := c.QueryArray("status") // <-- this is new

	limitStr := c.DefaultQuery("limit", "15")
	skipStr := c.DefaultQuery("skip", "0")

	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit < 1 {
		limit = 15
	}

	skip, err := strconv.Atoi(skipStr)
	if err != nil || skip < 0 {
		skip = 0
	}

	// Call updated service function with status filter
	alerts, totalCount, err := newsAlertService.GetNewsAlerts(newsAlertService.GetNewsAlertsParams{
		Search: search,
		SortBy: sortBy,
		Order:  order,
		Limit:  limit,
		Skip:   skip,
		Status: status, // <-- pass it in
	})

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to fetch news alerts",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data":       alerts,
		"totalCount": totalCount,
	})
}

func GetVisibleNewsAlertsHandler(c *gin.Context) {
	alerts, err := newsAlertService.GetVisibleNewsAlerts()
	if err != nil {
		utils.Logger.Error("Error fetching visible news alerts: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to fetch visible news alerts",
			"details": err.Error(), // Send internal error details (optional, remove if you don't want to expose internals)
		})
		return
	}

	if len(alerts) == 0 {
		utils.Logger.Warn("No visible news alerts found")
		c.JSON(http.StatusOK, gin.H{
			"message": "No visible news alerts available currently",
		})
		return
	}

	utils.Logger.Infof("Successfully returning %d alerts", len(alerts))
	// Return the alerts under the 'data' key
	c.JSON(http.StatusOK, gin.H{
		"data": alerts,
	})
}
