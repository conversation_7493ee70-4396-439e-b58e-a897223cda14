definitions:
  model.CreateReportDto:
    properties:
      category:
        type: string
      created_at:
        description: UserEmail    string `json:"user_email" binding:"required"`
        type: string
      created_by:
        type: string
      description:
        type: string
      domain_id:
        type: integer
      group_id:
        type: string
      id:
        type: integer
      is_favorite:
        type: boolean
      is_saved:
        type: boolean
      name:
        type: string
      published_on:
        type: string
      report_id:
        type: string
      report_url:
        type: string
      source_system:
        type: string
      updated_at:
        type: string
    required:
    - category
    - created_by
    - domain_id
    - name
    - report_id
    type: object
  model.DashboardRequest:
    properties:
      dashboard_id:
        type: string
    required:
    - dashboard_id
    type: object
  model.EmbedTokenResponse:
    properties:
      token:
        type: string
    type: object
  model.ErrorResponse:
    properties:
      details:
        example: More specific error details
        type: string
      message:
        example: An error occurred
        type: string
      status:
        example: error
        type: string
    type: object
  model.FavoriteReportRequest:
    properties:
      is_favorite:
        type: boolean
      is_saved:
        type: boolean
      report_id:
        type: integer
      user_id:
        type: integer
      view:
        type: string
    required:
    - report_id
    - user_id
    type: object
  model.GetSearchOptionsRequest:
    properties:
      query:
        type: string
      role_name:
        description: e.g., ["ADMIN"]
        type: string
      user_id:
        type: integer
    required:
    - query
    - role_name
    - user_id
    type: object
  model.LookerDashboardMetadata:
    properties:
      id:
        type: string
      title:
        type: string
    type: object
  model.LookerEmbedResponse:
    properties:
      url:
        type: string
    type: object
  model.PowerBIReportMetaData:
    properties:
      datasetId:
        type: string
      embedUrl:
        type: string
      groupId:
        type: string
      id:
        type: string
      name:
        type: string
      webUrl:
        type: string
    type: object
  model.ReportResponse:
    properties:
      category:
        type: string
      created_at:
        type: string
      created_by:
        type: string
      description:
        type: string
      domain_id:
        type: integer
      domain_name:
        type: string
      group_id:
        description: Nullable Group ID
        type: string
      id:
        description: Converted from uint to string
        type: integer
      name:
        type: string
      report_id:
        type: string
      report_url:
        type: string
      source_system:
        type: string
      updated_at:
        type: string
    type: object
  model.ReportSearchOption:
    properties:
      category:
        type: string
      domain_name:
        type: string
      id:
        type: integer
      name:
        type: string
      source_system:
        type: string
    type: object
  model.ReportWithPreference:
    properties:
      category:
        type: string
      created_at:
        type: string
      created_by:
        type: string
      description:
        type: string
      domain_id:
        type: integer
      group_id:
        type: string
      id:
        type: integer
      is_favorite:
        description: Preference flags
        type: boolean
      is_saved:
        type: boolean
      name:
        type: string
      published_on:
        type: string
      report_id:
        type: string
      report_url:
        type: string
      source_system:
        type: string
      updated_at:
        type: string
    type: object
  model.UserReq:
    properties:
      ad_groups:
        items:
          type: string
        type: array
      email:
        description: Required field
        type: string
      first_name:
        type: string
      last_name:
        type: string
    required:
    - ad_groups
    - email
    type: object
  model.UserRequest:
    properties:
      role_name:
        type: string
      user_id:
        description: Required field
        type: integer
    required:
    - user_id
    type: object
  userHandler.DomainModel:
    properties:
      id:
        type: integer
      name:
        type: string
    type: object
info:
  contact: {}
paths:
  /api/domains:
    post:
      consumes:
      - application/json
      description: Retrieve all available domains
      produces:
      - application/json
      responses:
        "200":
          description: Successfully retrieved domains
          schema:
            items:
              $ref: '#/definitions/userHandler.DomainModel'
            type: array
        "500":
          description: Internal server error
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Get all domains
      tags:
      - users
  /api/user/upsert:
    post:
      consumes:
      - application/json
      description: Insert or update a user record
      parameters:
      - description: User upsert request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/model.UserReq'
      produces:
      - application/json
      responses:
        "200":
          description: User record inserted successfully
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Invalid request body
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Upsert user
      tags:
      - users
  /dng/api/reports:
    post:
      consumes:
      - application/json
      description: Fetch catalog reports based on query parameters and user context
        with pagination support
      parameters:
      - default: 1
        description: Page number
        in: query
        name: page
        type: integer
      - default: 10
        description: Items per page
        in: query
        name: limit
        type: integer
      - description: Search term
        in: query
        name: search
        type: string
      - default: created_at
        description: Sort field
        in: query
        name: sort_by
        type: string
      - default: desc
        description: Sort order
        enum:
        - asc
        - desc
        in: query
        name: sort_order
        type: string
      - description: User context for report retrieval
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/model.UserRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Successfully retrieved reports
          schema:
            $ref: '#/definitions/model.ReportResponse'
        "400":
          description: Invalid query parameters
          schema:
            $ref: '#/definitions/model.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/model.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/model.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Retrieve catalog reports
      tags:
      - reports
  /dng/api/reports/bulk-insert:
    post:
      consumes:
      - multipart/form-data
      description: Upload an Excel file to create multiple reports with validation
      parameters:
      - description: Excel file to upload (must be .xlsx or .xls)
        in: formData
        name: file
        required: true
        type: file
      - description: User who created the reports
        in: formData
        name: created_by
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: All records successfully processed
          schema:
            $ref: '#/definitions/model.ReportResponse'
        "206":
          description: Partial success - some records processed
          schema:
            $ref: '#/definitions/model.ReportResponse'
        "304":
          description: No records modified - all unchanged
          schema:
            $ref: '#/definitions/model.ReportResponse'
        "400":
          description: Invalid file or processing error
          schema:
            $ref: '#/definitions/model.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/model.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/model.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Bulk create reports from Excel
      tags:
      - reports
  /dng/api/reports/bulk-insert-errors:
    get:
      description: Download an error Excel file containing validation errors from
        bulk upload
      parameters:
      - description: Temporary file path of the error file
        in: query
        name: filePath
        required: true
        type: string
      produces:
      - application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
      responses:
        "200":
          description: Excel file with error details
          headers:
            Content-Disposition:
              description: attachment; filename=bulk_upload_errors.xlsx
              type: string
            Content-Type:
              description: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
              type: string
          schema:
            type: file
        "400":
          description: Invalid file path
          schema:
            $ref: '#/definitions/model.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/model.ErrorResponse'
        "404":
          description: File not found
          schema:
            $ref: '#/definitions/model.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Download error file
      tags:
      - reports
  /dng/api/reports/download-template:
    get:
      description: Provides an Excel template with proper formatting for bulk report
        upload
      produces:
      - application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
      responses:
        "200":
          description: Excel template downloaded successfully
          headers:
            Content-Disposition:
              description: attachment; filename=report_template.xlsx
              type: string
            Content-Type:
              description: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
              type: string
          schema:
            type: file
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/model.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/model.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Download Excel Template
      tags:
      - reports
  /dng/api/reports/looker/looker-embed:
    get:
      description: Get a secure embed URL for a specific Looker dashboard with SSO
      parameters:
      - description: Looker Dashboard ID
        in: query
        name: dashboard_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Successfully retrieved Looker embed URL
          schema:
            $ref: '#/definitions/model.LookerEmbedResponse'
        "400":
          description: Missing dashboard ID
          schema:
            $ref: '#/definitions/model.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/model.ErrorResponse'
        "500":
          description: Failed to retrieve embed URL
          schema:
            $ref: '#/definitions/model.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Retrieve Looker Embed URL
      tags:
      - looker
  /dng/api/reports/looker/metadata:
    get:
      description: Retrieve metadata for a Looker dashboard including fields and filters
      parameters:
      - description: Looker Dashboard ID
        in: query
        name: dashboard_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Successfully retrieved metadata
          schema:
            $ref: '#/definitions/model.LookerDashboardMetadata'
        "400":
          description: Missing dashboard ID
          schema:
            $ref: '#/definitions/model.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/model.ErrorResponse'
        "422":
          description: Invalid dashboard ID
          schema:
            $ref: '#/definitions/model.ErrorResponse'
        "500":
          description: Failed to fetch metadata
          schema:
            $ref: '#/definitions/model.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Get Looker metadata
      tags:
      - looker
  /dng/api/reports/looker/render-pdf:
    post:
      consumes:
      - application/json
      description: Create and download a PDF of a Looker dashboard with specified
        filters
      parameters:
      - description: Dashboard PDF Generation Request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/model.DashboardRequest'
      produces:
      - application/pdf
      responses:
        "200":
          description: PDF file of the dashboard
          headers:
            Content-Disposition:
              description: attachment; filename=dashboard_{id}.pdf
              type: string
            Content-Type:
              description: application/pdf
              type: string
          schema:
            type: file
        "400":
          description: Invalid request
          schema:
            $ref: '#/definitions/model.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/model.ErrorResponse'
        "500":
          description: Failed to generate PDF
          schema:
            $ref: '#/definitions/model.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Generate Looker Dashboard PDF
      tags:
      - looker
  /dng/api/reports/pbi/embed-token:
    get:
      description: Get an embed token for a specific Power BI report with access validation
      parameters:
      - description: Power BI Report ID
        in: query
        name: report_id
        required: true
        type: string
      - description: Power BI Group ID (use 'me' for personal workspace)
        in: query
        name: group_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Successfully retrieved embed token
          schema:
            $ref: '#/definitions/model.EmbedTokenResponse'
        "400":
          description: Missing required parameters
          schema:
            $ref: '#/definitions/model.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/model.ErrorResponse'
        "500":
          description: Failed to retrieve embed token
          schema:
            $ref: '#/definitions/model.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Retrieve Power BI Embed Token
      tags:
      - powerbi
  /dng/api/reports/pbi/metadata:
    get:
      description: Retrieve metadata for a Power BI report including datasets and
        parameters
      parameters:
      - description: Power BI Report ID
        in: query
        name: report_id
        required: true
        type: string
      - description: Power BI Group ID (use 'me' for personal workspace)
        in: query
        name: group_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Successfully retrieved metadata
          schema:
            $ref: '#/definitions/model.PowerBIReportMetaData'
        "400":
          description: Missing required parameters
          schema:
            $ref: '#/definitions/model.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/model.ErrorResponse'
        "422":
          description: Invalid report ID or group ID
          schema:
            $ref: '#/definitions/model.ErrorResponse'
        "500":
          description: Failed to fetch metadata
          schema:
            $ref: '#/definitions/model.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Get Power BI metadata
      tags:
      - powerbi
  /dng/api/reports/search:
    post:
      consumes:
      - application/json
      description: Get available search options and filters based on user role and
        permissions
      parameters:
      - description: Search options request including user context
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/model.GetSearchOptionsRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Successfully retrieved search options
          schema:
            $ref: '#/definitions/model.ReportSearchOption'
        "400":
          description: Invalid request
          schema:
            $ref: '#/definitions/model.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/model.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/model.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Retrieve search options
      tags:
      - reports
  /dng/api/reports/upsert-report:
    post:
      consumes:
      - application/json
      description: Create a new report or update an existing one
      parameters:
      - description: Report data to create or update
        in: body
        name: report
        required: true
        schema:
          $ref: '#/definitions/model.CreateReportDto'
      produces:
      - application/json
      responses:
        "201":
          description: Successfully created report
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Invalid input
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Create or update a report
      tags:
      - reports
  /dng/api/reports/user-preferences:
    post:
      consumes:
      - application/json
      description: Update the favorite/saved/view status of a report for a user
      parameters:
      - description: Favorite report update request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/model.FavoriteReportRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Successfully updated preference
          schema:
            $ref: '#/definitions/model.ReportWithPreference'
        "400":
          description: Invalid request
          schema:
            $ref: '#/definitions/model.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/model.ErrorResponse'
        "404":
          description: Preference not found
          schema:
            $ref: '#/definitions/model.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/model.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Update report favorite status
      tags:
      - user-preferences
swagger: "2.0"
