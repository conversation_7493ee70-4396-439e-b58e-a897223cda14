package app

import (
	"context"
	"dng-module/config"
	deliveryHTTP "dng-module/internal/delivery/http"
	"errors"
	"fmt"
	"log/slog"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	dapr "github.com/dapr/go-sdk/client"
	"github.com/gin-gonic/gin"
)

type App struct {
	router *gin.Engine
	log    *slog.Logger
	cfg    *config.Config // Changed to pointer to avoid copying
	client dapr.Client
}

// Add this method to your App struct in app.go
func (a *App) Close() error {
	if a.client != nil {
		a.client.Close()
		a.log.Info("Dapr client closed")
	}
	return nil
}

// GetClient returns the Dapr client instance
func (a *App) GetClient() dapr.Client {
	return a.client
}

// NewApp creates a new application instance with proper initialization
func NewApp(ctx context.Context, env string) (*App, error) {
	// Initialize logger first
	logger := slog.New(slog.NewJSONHandler(os.Stdout, nil))
	slog.SetDefault(logger)

	// Load configuration
	cfg, err := config.LoadConfigByEnv(env)
	if err != nil {
		return nil, fmt.Errorf("failed to load config: %w", err)
	}

	app := &App{
		router: gin.New(),
		cfg:    &cfg, // Store as pointer
		log:    logger,
	}

	// Initialize Dapr client (with retries only in production)
	if env != "development" {
		if err := app.initDaprClient(ctx); err != nil {
			return nil, fmt.Errorf("failed to initialize Dapr client: %w", err)
		}
	} else {
		logger.Info("Running in development mode - Dapr client disabled")
	}

	// Setup secrets
	if err := app.setupSecrets(); err != nil {
		return nil, fmt.Errorf("failed to setup secrets: %w", err)
	}

	// Connect to database
	if err := config.ConnectDatabase(app.cfg.DB); err != nil {
		return nil, fmt.Errorf("failed to connect database: %w", err)
	}

	// Setup middleware
	app.router.Use(gin.Recovery())
	app.router.Use(app.loggingMiddleware())

	// Setup routes from the routes package
	deliveryHTTP.SetupRoutes(app.router, *app.cfg) // Add this line

	return app, nil
}

func (app *App) GetSecret(key string) (string, error) {
	if val, exists := app.cfg.Dapr.SecretKeys[key]; exists {
		return val, nil
	}
	return "", fmt.Errorf("secret key %s not found", key)
}

// initDaprClient initializes the Dapr client with retry logic
func (a *App) initDaprClient(ctx context.Context) error {
	var err error
	maxRetries := 3
	retryDelay := 2 * time.Second

	for i := 0; i < maxRetries; i++ {
		a.client, err = dapr.NewClient()
		if err == nil {
			a.log.Info("Dapr client initialized successfully")
			return nil
		}

		a.log.Error("Failed to create Dapr client, retrying...",
			"attempt", i+1,
			"error", err,
			"retry_in", retryDelay)

		if i < maxRetries-1 {
			select {
			case <-ctx.Done():
				return ctx.Err()
			case <-time.After(retryDelay):
				continue
			}
		}
	}

	return fmt.Errorf("failed to create Dapr client after %d attempts: %w", maxRetries, err)
}

func (a *App) setupSecrets() error {
	if a.client == nil {
		a.log.Warn("No Dapr client - using configuration secrets as-is")
		return nil
	}

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	secret, err := a.client.GetSecret(ctx, a.cfg.Dapr.SecretStore, a.cfg.Dapr.Secret, nil)
	if err != nil {
		return fmt.Errorf("failed to fetch secrets: %w", err)
	}

	if secret != nil {
		for key := range a.cfg.Dapr.SecretKeys {
			if val, exists := secret[key]; exists {
				a.cfg.Dapr.SecretKeys[key] = val
				a.log.Info("Fetched and applied secret", "key", key)

				// Apply secrets to configuration
				switch key {
				case "db_password":
					a.cfg.DB.Password = val
					// Add other secret mappings as needed
				}
			}
		}
	}

	return nil
}

func (a *App) loggingMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()
		path := c.Request.URL.Path

		c.Next()

		latency := time.Since(start)
		a.log.Info("Request completed",
			"status", c.Writer.Status(),
			"method", c.Request.Method,
			"path", path,
			"ip", c.ClientIP(),
			"latency", latency,
		)
	}
}

func (a *App) Run(ctx context.Context) error {
	server := &http.Server{
		Addr:    fmt.Sprintf("%s:%s", a.cfg.App.Host, a.cfg.App.Port),
		Handler: a.router,
	}

	// Graceful shutdown
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)

	// Server error channel
	serverErr := make(chan error, 1)
	go func() {
		a.log.Info("Starting server",
			"host", a.cfg.App.Host,
			"port", a.cfg.App.Port,
		)
		if err := server.ListenAndServe(); err != nil && !errors.Is(err, http.ErrServerClosed) {
			serverErr <- err
		}
	}()

	select {
	case err := <-serverErr:
		return fmt.Errorf("server error: %w", err)
	case <-quit:
		a.log.Info("Shutting down server gracefully...")

		shutdownCtx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
		defer cancel()

		if err := server.Shutdown(shutdownCtx); err != nil {
			return fmt.Errorf("server shutdown error: %w", err)
		}

		// Close Dapr client if it exists
		if a.client != nil {
			a.client.Close() // Simply call Close() without checking return value
			a.log.Info("Dapr client closed")
		}

		a.log.Info("Server stopped")
		return nil
	case <-ctx.Done():
		return ctx.Err()
	}
}
