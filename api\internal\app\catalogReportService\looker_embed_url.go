package catalogReportService

import (
	"bytes"
	"dng-module/config"
	"dng-module/internal/model"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
)

const contentTypeJSON = "application/json"

type LookerAPIError struct {
	StatusCode int
	Message    string
}

func (e *LookerAPIError) Error() string {
	return e.Message
}

func GetLookerEmbedURL(dashboardID string, cfg config.Config) (string, error) {
	// func GetAccessToken(cfg config.Config) (string, error) {
	// tenantID := cfg.Dapr.SecretKeys["azure_tenant_id"]
	// clientID := cfg.Dapr.SecretKeys["azure_client_id"]
	// clientSecret := cfg.Dapr.SecretKeys["azure_client_secret"]
	// Step 1: Login to Looker API

	baseURL := cfg.Dapr.SecretKeys["LOOKERSDK_BASE_URL"]
	clientID := cfg.Dapr.SecretKeys["LOOKER_CLIENT_ID"]
	clientSecret := cfg.Dapr.SecretKeys["LOOKER_CLIENT_SECRET"]

	loginURL := fmt.Sprintf("%s/api/4.0/login?client_id=%s&client_secret=%s", baseURL, clientID, clientSecret)

	loginResp, err := http.Post(loginURL, contentTypeJSON, nil)
	if err != nil {
		return "", fmt.Errorf("failed to login to Looker: %w", err)
	}
	defer loginResp.Body.Close()

	var loginData model.LookerLoginResponse

	if err := json.NewDecoder(loginResp.Body).Decode(&loginData); err != nil {
		return "", fmt.Errorf("failed to decode login response: %w", err)
	}

	// Step 2: Request embed URL
	requestBody, _ := json.Marshal(map[string]interface{}{
		"target_url":         fmt.Sprintf("%s/embed/dashboards/%s", baseURL, dashboardID),
		"session_length":     300,
		"force_logout_login": false,
	})

	req, _ := http.NewRequest("POST",
		fmt.Sprintf("%s/api/4.0/embed/token_url/me", baseURL),
		bytes.NewBuffer(requestBody),
	)
	req.Header.Set("Content-Type", contentTypeJSON)
	req.Header.Set("Authorization", "Bearer "+loginData.AccessToken)

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return "", fmt.Errorf("failed to get embed URL: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("failed to get embed URL: received status %d", resp.StatusCode)
	}

	var embedData model.LookerEmbedResponse

	if err := json.NewDecoder(resp.Body).Decode(&embedData); err != nil {
		return "", fmt.Errorf("failed to decode embed response: %w", err)
	}

	return embedData.Url, nil
}

func GetLookerDashMetadata(dashboardID string, cfg config.Config) (*model.LookerDashboardMetadata, error) {
	// Step 1: Login to Looker API to get access token
	baseURL := cfg.Dapr.SecretKeys["LOOKERSDK_BASE_URL"]
	clientID := cfg.Dapr.SecretKeys["LOOKER_CLIENT_ID"]
	clientSecret := cfg.Dapr.SecretKeys["LOOKER_CLIENT_SECRET"]

	loginURL := fmt.Sprintf("%s/api/4.0/login?client_id=%s&client_secret=%s", baseURL, clientID, clientSecret)

	loginResp, err := http.Post(loginURL, contentTypeJSON, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to login to Looker: %w", err)
	}
	defer loginResp.Body.Close()

	var loginData struct {
		AccessToken string `json:"access_token"`
	}
	if err := json.NewDecoder(loginResp.Body).Decode(&loginData); err != nil {
		return nil, fmt.Errorf("failed to decode login response: %w", err)
	}

	// Step 2: Get dashboard metadata
	metaURL := fmt.Sprintf("%s/api/4.0/dashboards/%s", baseURL, dashboardID)

	req, err := http.NewRequest("GET", metaURL, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create metadata request: %w", err)
	}
	req.Header.Set("Authorization", "token "+loginData.AccessToken)

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to get dashboard metadata: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, &LookerAPIError{
			StatusCode: resp.StatusCode,
			Message:    strings.TrimSpace(string(body)),
		}
	}

	var dashboardMeta model.LookerDashboardMetadata

	if err := json.NewDecoder(resp.Body).Decode(&dashboardMeta); err != nil {
		return nil, fmt.Errorf("failed to decode dashboard metadata: %w", err)
	}

	return &dashboardMeta, nil
}
