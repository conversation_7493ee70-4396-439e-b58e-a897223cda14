package userService

import (
	// "dng-module/internal/app/userService"
	"dng-module/internal/model"
	"dng-module/testing/utilsTest"
	"reflect"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"gorm.io/gorm"
)

func TestGetAllDomains(t *testing.T) {
	db, mock := utilsTest.SetupMockDB(t)

	tests := []struct {
		name      string
		userEmail string
		mockSetup func()
		want      []model.Domain
		wantErr   bool
	}{
		{
			name:      "user not found",
			userEmail: "<EMAIL>",
			mockSetup: func() {
				mock.ExpectQuery(`SELECT \* FROM "users" WHERE email = \$1 ORDER BY "users"\."id" LIMIT \$2`).
					WithArgs("<EMAIL>", 1). // because LIMIT $2 is used by GORM
					WillReturnError(gorm.ErrRecordNotFound)

			},
			want:    nil,
			wantErr: true,
		},
		{
			name:      "user with no domain access",
			userEmail: "<EMAIL>",
			mockSetup: func() {
				mock.ExpectQuery(`SELECT \* FROM "users" WHERE email = \$1 ORDER BY "users"\."id" LIMIT \$2`).
					WithArgs("<EMAIL>", 1).
					WillReturnRows(sqlmock.NewRows([]string{"id", "email", "first_name", "last_name"}).
						AddRow(1, "<EMAIL>", "John", "Doe"))

				mock.ExpectQuery(`SELECT user_domain_access.domain_id, role.is_super_admin FROM "user_domain_access" JOIN roles ON user_domain_access.role_id = role.id WHERE user_domain_access.user_id = \$1`).
					WithArgs(1).
					WillReturnRows(sqlmock.NewRows([]string{"domain_id", "is_super_admin"}))
			},
			want:    []model.Domain{},
			wantErr: false,
		},
		{
			name:      "user is super admin",
			userEmail: "<EMAIL>",
			mockSetup: func() {
				mock.ExpectQuery(`SELECT \* FROM "users" WHERE email = \$1 ORDER BY "users"\."id" LIMIT \$2`).
					WithArgs("<EMAIL>", 1).
					WillReturnRows(sqlmock.NewRows([]string{"id", "email", "first_name", "last_name"}).
						AddRow(2, "<EMAIL>", "Admin", "User"))

				mock.ExpectQuery(`SELECT user_domain_access.domain_id, role.is_super_admin FROM "user_domain_access" JOIN roles ON user_domain_access.role_id = role.id WHERE user_domain_access.user_id = \$1`).
					WithArgs(2).
					WillReturnRows(sqlmock.NewRows([]string{"domain_id", "is_super_admin"}).
						AddRow(1, true))

				mock.ExpectQuery(`SELECT \* FROM "domains"`).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).
						AddRow(1, "Domain A").
						AddRow(2, "Domain B"))
			},
			want: []model.Domain{
				{ID: 1, Name: "Domain A"},
				{ID: 2, Name: "Domain B"},
			},
			wantErr: false,
		},
		{
			name:      "user with regular domain access",
			userEmail: "<EMAIL>",
			mockSetup: func() {
				mock.ExpectQuery(`SELECT \* FROM "users" WHERE email = \$1 ORDER BY "users"\."id" LIMIT \$2`).
					WithArgs("<EMAIL>", 1).
					WillReturnRows(sqlmock.NewRows([]string{"id", "email", "first_name", "last_name"}).
						AddRow(3, "<EMAIL>", "Viewer", "User"))

				mock.ExpectQuery(`SELECT user_domain_access.domain_id, role.is_super_admin FROM "user_domain_access" JOIN roles ON user_domain_access.role_id = role.id WHERE user_domain_access.user_id = \$1`).
					WithArgs(3).
					WillReturnRows(sqlmock.NewRows([]string{"domain_id", "is_super_admin"}).
						AddRow(10, false).
						AddRow(20, false))

				mock.ExpectQuery(`SELECT \* FROM "domains" WHERE id IN \(\$1,\$2\)`).
					WithArgs(10, 20).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).
						AddRow(10, "Domain X").
						AddRow(20, "Domain Y"))
			},
			want: []model.Domain{
				{ID: 10, Name: "Domain X"},
				{ID: 20, Name: "Domain Y"},
			},
			wantErr: false,
		},
		{
			name:      "super admin - db error fetching domains",
			userEmail: "<EMAIL>",
			mockSetup: func() {
				mock.ExpectQuery(`SELECT \* FROM "users" WHERE email = \$1 ORDER BY "users"\."id" LIMIT \$2`).
					WithArgs("<EMAIL>", 1).
					WillReturnRows(sqlmock.NewRows([]string{"id", "email", "first_name", "last_name"}).
						AddRow(4, "<EMAIL>", "Admin", "Two"))

				mock.ExpectQuery(`SELECT user_domain_access.domain_id, role.is_super_admin FROM "user_domain_access" JOIN roles ON user_domain_access.role_id = role.id WHERE user_domain_access.user_id = \$1`).
					WithArgs(4).
					WillReturnRows(sqlmock.NewRows([]string{"domain_id", "is_super_admin"}).
						AddRow(1, true))

				mock.ExpectQuery(`SELECT \* FROM "domains"`).
					WillReturnError(gorm.ErrInvalidTransaction)
			},
			want:    nil,
			wantErr: true,
		},
		{
			name:      "regular user - db error fetching assigned domains",
			userEmail: "<EMAIL>",
			mockSetup: func() {
				mock.ExpectQuery(`SELECT \* FROM "users" WHERE email = \$1 ORDER BY "users"\."id" LIMIT \$2`).
					WithArgs("<EMAIL>", 1).
					WillReturnRows(sqlmock.NewRows([]string{"id", "email", "first_name", "last_name"}).
						AddRow(5, "<EMAIL>", "User", "Two"))

				mock.ExpectQuery(`SELECT user_domain_access.domain_id, role.is_super_admin FROM "user_domain_access" JOIN roles ON user_domain_access.role_id = role.id WHERE user_domain_access.user_id = \$1`).
					WithArgs(5).
					WillReturnRows(sqlmock.NewRows([]string{"domain_id", "is_super_admin"}).
						AddRow(5, false))

				mock.ExpectQuery(`SELECT \* FROM "domains" WHERE id IN \(\$1\)`).
					WithArgs(5).
					WillReturnError(gorm.ErrInvalidField)
			},
			want:    nil,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockSetup != nil {
				tt.mockSetup()
			}
			got, err := GetAllDomains(db, tt.userEmail)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetAllDomains() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetAllDomains() = %v, want %v", got, tt.want)
			}
			// ensure all expectations were met
			if err := mock.ExpectationsWereMet(); err != nil {
				t.Errorf("there were unfulfilled expectations: %s", err)
			}
		})
	}
}
