'use client'
import React from 'react'
import dynamic from 'next/dynamic'
import { Box, Typography, Paper } from '@mui/material'

// Dynamically import ApexCharts to avoid SSR issues
const Chart = dynamic(() => import('react-apexcharts'), { ssr: false })

const NetworkDataComboChart: React.FC = () => {
    // Distribution Centers data
    const distributionCenters = ['Memphis', 'Oklahoma', 'Janesville', 'Atlanta', 'Phoenix', 'Chicago', 'Network Avg'];

    // Chart options
    const options = {
        chart: {
            id: 'dc-performance-metrics',
            type: 'line',
            stacked: false,
            toolbar: {
                show: false
            },
            fontFamily: 'Arial, sans-serif',
            animations: {
                enabled: true,
                easing: 'easeinout',
                speed: 800
            }
        },
        colors: ['#FFC107', '#000000', '#4D4D4D', '#8D8D8D'], // DG colors: yellow, black, dark gray, light gray
        stroke: {
            width: [0, 3, 3, 3],
            curve: 'smooth',
            dashArray: [0, 0, 5, 0]
        },
        plotOptions: {
            bar: {
                columnWidth: '50%',
                borderRadius: 5,
                dataLabels: {
                    position: 'top'
                }
            }
        },
        dataLabels: {
            enabled: true,
            enabledOnSeries: [0],
            formatter: function(val: number) {
                return val + '%';
            },
            style: {
                fontSize: '10px',
                colors: ['#333']
            },
            offsetY: -20
        },
        markers: {
            size: 5,
            hover: {
                size: 7
            }
        },
        xaxis: {
            categories: distributionCenters,
            labels: {
                style: {
                    colors: '#616161',
                    fontSize: '12px',
                    fontFamily: 'Arial, sans-serif',
                }
            },
            title: {
                text: 'Distribution Centers',
                style: {
                    color: '#616161',
                    fontSize: '12px',
                    fontFamily: 'Arial, sans-serif',
                }
            }
        },
        yaxis: [
            {
                title: {
                    text: 'Audit Accuracy (%)',
                    style: {
                        color: '#FFC107',
                        fontSize: '12px',
                        fontFamily: 'Arial, sans-serif',
                        fontWeight: 'bold'
                    }
                },
                labels: {
                    formatter: function(val: number) {
                        return val.toFixed(1) + '%';
                    },
                    style: {
                        colors: '#FFC107',
                        fontSize: '12px',
                        fontFamily: 'Arial, sans-serif',
                    }
                },
                min: 70,
                max: 100
            },
            {
                opposite: true,
                title: {
                    text: 'Capacity Utilization (%)',
                    style: {
                        color: '#000000',
                        fontSize: '12px',
                        fontFamily: 'Arial, sans-serif',
                        fontWeight: 'bold'
                    }
                },
                labels: {
                    formatter: function(val: number) {
                        return val.toFixed(1) + '%';
                    },
                    style: {
                        colors: '#000000',
                        fontSize: '12px',
                        fontFamily: 'Arial, sans-serif',
                    }
                },
                min: 60,
                max: 100
            },
            {
                show: false,
                min: 85,
                max: 100
            },
            {
                opposite: true,
                show: false,
                min: 0,
                max: 10
            }
        ],
        tooltip: {
            shared: true,
            intersect: false,
            theme: 'light',
            y: {
                formatter: function(val: number, { seriesIndex }: { seriesIndex: number }) {
                    if (seriesIndex === 0 || seriesIndex === 1 || seriesIndex === 2) {
                        return val.toFixed(1) + '%';
                    }
                    return val.toFixed(1);
                }
            }
        },
        legend: {
            position: 'top',
            horizontalAlign: 'left',
            fontSize: '11px',
            fontFamily: 'Arial, sans-serif',
            offsetY: 5,
            offsetX: 10,
            labels: {
                colors: '#616161'
            },
            markers: {
                width: 12,
                height: 12,
                radius: 2
            },
            itemMargin: {
                horizontal: 15,
                vertical: 5
            }
        },
        grid: {
            borderColor: '#f1f1f1',
            row: {
                colors: ['transparent', 'transparent'],
                opacity: 0.5
            }
        },
        annotations: {
            yaxis: [
                {
                    y: 97,
                    yAxisIndex: 0,
                    borderColor: '#FFC107',
                    borderWidth: 2,
                    label: {
                        borderColor: '#FFC107',
                        style: {
                            color: '#000',
                            background: '#FFC107',
                            fontSize: '10px',
                            fontFamily: 'Arial, sans-serif',
                            fontWeight: 'bold'
                        },
                        text: 'Target: 97%'
                    }
                }
            ]
        }
    };

    // Chart series data
    const series = [
        {
            name: 'Audit Accuracy',
            type: 'column',
            data: [85.3, 86.7, 90.3, 94.5, 95.2, 93.8, 93.5]
        },
        {
            name: 'Capacity Utilization',
            type: 'line',
            data: [92.1, 89.5, 78.3, 82.7, 75.4, 81.2, 83.2]
        },
        {
            name: 'In-Stock Percentage',
            type: 'line',
            data: [91.8, 92.5, 94.7, 95.3, 96.1, 93.9, 94.2],
            dashArray: 5
        },
        {
            name: 'Financial Impact ($K)',
            type: 'line',
            data: [3.5, 3.1, 2.0, 0.8, 0.5, 1.0, 1.8]
        }
    ];

    return (
        <Paper
            elevation={0}
            sx={{
                p: 2,
                borderRadius: 3,
                border: '1px solid',
                borderColor: 'divider',
                backgroundColor: 'white',
                mb: 3
            }}
        >
            <Typography variant="h6" gutterBottom sx={{ fontWeight: 'bold', color: '#424242' }}>
                Distribution Center Performance Metrics
            </Typography>
            <Typography variant="body2" sx={{ color: 'text.secondary', mb: 2 }}>
                Comprehensive view of audit accuracy, capacity utilization, and in-stock percentage across DCs
            </Typography>
            <Box sx={{ height: 450 }}>
                <Chart
                    options={options as any}
                    series={series}
                    type="line"
                    height="100%"
                    width="100%"
                />
            </Box>
            <Box sx={{ mt: 2, p: 1, borderTop: '1px solid #f0f0f0' }}>
                <Typography variant="body2" sx={{ color: '#000', fontSize: '13px', fontWeight: 'bold', mb: 1 }}>
                    Key Insights:
                </Typography>
                <Box component="ul" sx={{ m: 0, pl: 2, color: 'text.secondary', fontSize: '12px' }}>
                    <li>Memphis and Oklahoma DCs require immediate attention with audit accuracy below 87%.</li>
                    <li>Strong negative correlation (-0.78) between capacity utilization and audit accuracy.</li>
                    <li>A 2% improvement in audit accuracy would result in $1.2M annual savings.</li>
                </Box>
            </Box>
        </Paper>
    )
}

export default NetworkDataComboChart
