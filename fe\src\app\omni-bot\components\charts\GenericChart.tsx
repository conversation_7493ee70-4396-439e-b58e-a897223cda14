'use client'
import dynamic from 'next/dynamic'
import React from 'react'

const Chart = dynamic(() => import('react-apexcharts'), { ssr: false })

interface GenericChartProps {
    options: any
    series: any
    type: 'line' | 'bar' | 'radar' | 'area' | 'pie' | 'donut' | 'scatter' | 'heatmap'
    height?: number | string
    width?: number | string
}

const GenericChart: React.FC<GenericChartProps> = ({ options, series, type, height = 300, width = '100%' }) => (
    <Chart options={options} series={series} type={type} height={height} width={width} />
)

export default GenericChart
