import { Box, List } from '@mui/material'
import { usePathname, useSearchParams } from 'next/navigation'
import { aiAnalyticsItems, generalSidebarItems, ReportsItems } from './MenuItems'
import NavGroup from './NavGroup/NavGroup'
import NavItem from './NavItem'

interface SidebarItemsProps {
    toggleMobileSidebar?: () => void
    isCollapsed: boolean
}

const SidebarItems = ({ toggleMobileSidebar, isCollapsed }: SidebarItemsProps) => {
    let pathDirect = usePathname()
    const isOmniBotPage = pathDirect.includes('/ai-analytics')
    const isReportPage = pathDirect.includes('/reports')

    let menuItems = generalSidebarItems

    if (isOmniBotPage) {
        menuItems = aiAnalyticsItems
    }
    if (isReportPage) {
        menuItems = ReportsItems
    }

    const searchParams = useSearchParams()

    const viewParam = searchParams.get('view')
    const IS_FAVORITE_VIEW = viewParam?.includes('favorite')
    const IS_SAVED_LATER_VIEW = viewParam?.includes('saved-later')

    if (IS_FAVORITE_VIEW) {
        pathDirect = '/reports?view=favorites'
    } else if (IS_SAVED_LATER_VIEW) {
        pathDirect = '/reports?view=saved-later'
    }

    return (
        <Box sx={{ px: 1, mt: 1 }}>
            <List sx={{ pt: 0 }} className="sidebarNav" component="div">
                {menuItems.map((item) => {
                    if ('navlabel' in item && item.navlabel) {
                        return <NavGroup item={item} key={item.subheader} isCollapsed={isCollapsed} />
                    } else {
                        return (
                            <NavItem
                                item={item}
                                pathDirect={pathDirect}
                                isCollapsed={isCollapsed}
                                onClick={() => toggleMobileSidebar}
                                key={item?.id}
                            />
                        )
                    }
                })}
            </List>
        </Box>
    )
}

export default SidebarItems
