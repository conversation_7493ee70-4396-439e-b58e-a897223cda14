'use client'

import { bulkInsertErrorsService, bulkUploadService } from '@/app/reports/services'
import { BulkUploadResponse } from '@/app/reports/types'
import { downloadBlob } from '@/utils/helper'
import { snackbar } from '@/utils/toast'
import { ArrowCircleDownOutlined, CloudUpload } from '@mui/icons-material'
import { Alert, Button, CircularProgress, IconButton, Stack, styled, Tooltip, Typography } from '@mui/material'
import { useSession } from 'next-auth/react'
import { useState } from 'react'

type BulkUploadReportsProps = {
    uploadResponse: BulkUploadResponse | null
    setUploadResponse: (response: BulkUploadResponse | null) => void
}

export default function BulkUploadReports({ uploadResponse, setUploadResponse }: Readonly<BulkUploadReportsProps>) {
    const [uploading, setUploading] = useState(false)
    const [selectedFile, setSelectedFile] = useState<string | null>(null)
    const { data: session } = useSession()
    const userEmail = session?.user.email

    const processExcelFile = async (file: File) => {
        setUploading(true)
        setSelectedFile(file.name)
        const formData = new FormData()
        formData.append('file', file)
        formData.append('created_by', userEmail ?? '')

        try {
            const response = await bulkUploadService(formData)
            // Only set the response if it has expected shape
            if (response?.data) {
                setUploadResponse(response)
                if (response.data.error_count === 0) {
                    snackbar.success(response.message)
                } else {
                    snackbar.error('File upload failed! Please check the error file for details.')
                }
            } else {
                snackbar.error('Unexpected response from the server.')
            }
        } catch (error) {
            const err = error as Error & { data?: any; status?: number; message?: string }
            if (err?.status === 304) {
                setUploadResponse(null)
                snackbar.info('No Change in file') // "No changes detected"
            } else if (err?.data) {
                setUploadResponse(err.data)
                snackbar.error(err.data?.message ?? 'File upload failed!')
            } else {
                snackbar.error(err.message || 'Unknown error occurred')
            }
        } finally {
            setUploading(false)
        }
    }

    const downloadErrors = async () => {
        if (uploadResponse?.data?.errors && uploadResponse.data?.error_file_path) {
            try {
                const blob = await bulkInsertErrorsService(uploadResponse.data.error_file_path)
                downloadBlob(blob, 'upload-errors.xlsx')
            } catch (error) {
                snackbar.error('File download failed!')
                console.log('Error downloading file: ', error)
            }
        }
    }

    return (
        <>
            <Stack direction="row" spacing={2} alignItems="center">
                <Button
                    component="label"
                    variant="contained"
                    fullWidth
                    startIcon={uploading ? <CircularProgress size={20} color="secondary" /> : <CloudUpload />}
                >
                    {uploading ? 'Processing upload...' : 'Upload Excel File'}
                    <VisuallyHiddenInput
                        type="file"
                        accept=".xlsx,.xls"
                        onChange={(e) => {
                            const file = e.target.files?.[0]
                            if (file) {
                                processExcelFile(file)
                                e.target.value = '' // Reset input value to allow re-uploading the same file
                            }
                        }}
                    />
                </Button>
            </Stack>
            {uploadResponse && (
                <Alert
                    sx={{ p: 1, mt: -1 }}
                    severity={uploadResponse.data.error_count > 0 ? 'error' : 'success'}
                    icon={false}
                >
                    <Typography variant={'subtitle1'}>File Name: {selectedFile ?? 'No file selected'}</Typography>
                    <Typography variant={'subtitle1'}>
                        Total Processed: {uploadResponse.data.total_processed ?? 0}
                    </Typography>
                    <Typography variant={'subtitle1'}>
                        Success Count: {uploadResponse.data.success_count ?? 0}
                    </Typography>
                    <Typography variant={'subtitle1'}>
                        Error Count: {uploadResponse.data.error_count ?? 0}
                        {uploadResponse.data.error_count > 0 && (
                            <Tooltip title={'Download upload file errors'} arrow>
                                <IconButton onClick={downloadErrors} color="error" size="small">
                                    <ArrowCircleDownOutlined />
                                </IconButton>
                            </Tooltip>
                        )}
                    </Typography>
                </Alert>
            )}
        </>
    )
}

// Styled component for the file input
const VisuallyHiddenInput = styled('input')({
    clip: 'rect(0 0 0 0)',
    clipPath: 'inset(50%)',
    height: 1,
    overflow: 'hidden',
    position: 'absolute',
    bottom: 0,
    left: 0,
    whiteSpace: 'nowrap',
    width: 1,
})
