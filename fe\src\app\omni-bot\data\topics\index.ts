// Import types
import { Topic } from '../../types';

// Import topic data from consolidated files
import { networkDataTopic, NETWORK_DATA_QA, AUDIT_QA } from './topic1-networkData';
import { dcSpecificTrendsTopic, DC_SPECIFIC_QA } from './topic2-dcSpecificTrends';
import { employeePerformanceTopic, EMPLOYEE_PERFORMANCE_QA } from './topic3-employeePerformance';
import { orderToteErrorsTopic, ORDER_TOTE_ERRORS_QA } from './topic4-orderToteErrors';
import { TOPIC_CONTEXT, TOPIC_KEYWORDS, GENERIC_RESPONSES, CHATS } from './common';

// Export all topic data
export {
  networkDataTopic,
  dcSpecificTrendsTopic,
  employeePerformanceTopic,
  orderToteErrorsTopic,
  NETWORK_DATA_QA,
  AUDIT_QA,
  DC_SPECIFIC_QA,
  EMPLOYEE_PERFORMANCE_QA,
  ORDER_TOTE_ERRORS_QA,
  TOPIC_CONTEXT,
  TOPIC_KEYWORDS,
  GENERIC_RESPONSES,
  CHATS
};

// Combine topics into a single array
export const TOPICS: Topic[] = [
  networkDataTopic,
  dcSpecificTrendsTopic,
  employeePerformanceTopic,
  orderToteErrorsTopic
];

// Helper Functions
export const getTopicIdToKeyMap = () => {
  // Create a mapping of topic IDs to context keys
  const topicMap: Record<string, string> = {};

  // For each topic in TOPICS, find a matching key in TOPIC_CONTEXT
  TOPICS.forEach(topic => {
    // Try to find a matching context key based on title similarity
    const contextKey = Object.keys(TOPIC_CONTEXT).find(key => {
      const context = TOPIC_CONTEXT[key];
      // Check if the context title is similar to the topic title
      return context.title.toLowerCase().includes(topic.title.toLowerCase().split(' ')[0]) ||
             topic.title.toLowerCase().includes(context.title.toLowerCase().split(' ')[0]);
    });

    if (contextKey) {
      topicMap[topic.id.toString()] = contextKey;
    }
  });

  return topicMap;
};
