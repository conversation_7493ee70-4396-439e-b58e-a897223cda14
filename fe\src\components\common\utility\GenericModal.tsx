import CloseIcon from '@mui/icons-material/Close'
import { Breakpoint, Dialog, DialogActions, DialogContent, DialogTitle, IconButton, Typography } from '@mui/material'
import { ReactNode } from 'react'

interface ModalProps {
    open: boolean
    onClose: () => void
    title?: ReactNode
    children?: ReactNode
    actions?: ReactNode
    width?: false | Breakpoint
    showCloseButton?: boolean
}

export default function GenericModal(props: Readonly<ModalProps>) {
    const { open, onClose, title, children, actions, width = 'md', showCloseButton = true } = props

    return (
        <Dialog open={open} onClose={onClose} maxWidth={width} fullWidth>
            {title && (
                <DialogTitle>
                    {typeof title === 'string' ? (
                        <div>
                            <Typography variant="h6">{title}</Typography>
                        </div>
                    ) : (
                        title
                    )}
                    {showCloseButton && (
                        <IconButton
                            aria-label="close"
                            onClick={onClose}
                            sx={{ position: 'absolute', right: 8, top: 8 }}
                        >
                            <CloseIcon />
                        </IconButton>
                    )}
                </DialogTitle>
            )}
            {children && <DialogContent>{children}</DialogContent>}
            {actions && <DialogActions>{actions}</DialogActions>}
        </Dialog>
    )
}
