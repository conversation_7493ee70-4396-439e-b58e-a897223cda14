apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: {{CI_PROJECT_NAME}}
  namespace: {{KUBE_NAMESPACE}}
  labels: 
    cattle.io/creator: {{GITLAB_USER_LOGIN}}
  annotations:
    #The below parameter disables client re-tries to the pod.
    nginx.ingress.kubernetes.io/proxy-next-upstream-tries: "0"
spec:
  ingressClassName: "nginx"
  rules:
  - host: {{KUBE_APP_HOST}}
    http:
      paths:
      - path: /{{INGRESS_BASE_PATH}}
        pathType: Prefix
        backend:
          service:
            name: {{KUBE_SERVICE_NAME}}
            port:
              number: {{PORT_MAPPING}}
  # - host: {{KUBE_APP_CNAME}}
  #   http:
  #     paths:
  #     - path: /{{INGRESS_BASE_PATH}}
  #       pathType: Prefix
  #       backend:
  #         service:
  #           name: {{KUBE_SERVICE_NAME}}
  #           port:
  #             number: {{PORT_MAPPING}}
  # - host: {{KUBE_DOLGEN_APP_CNAME}}
  #   http:
  #     paths:
  #     - path: /{{INGRESS_BASE_PATH}}
  #       pathType: Prefix
  #       backend:
  #         service:
  #           name: {{KUBE_SERVICE_NAME}}
  #           port:
  #             number: {{PORT_MAPPING}}

# USED ONLY FOR GKE DEPLOYED APPLICATIONS
# apiVersion: gateway.networking.k8s.io/v1beta1
# kind: HTTPRoute
# metadata:
#   name: {{CI_PROJECT_NAME}}
#   namespace: {{KUBE_NAMESPACE}}
#   labels: 
#     cattle.io/creator: {{GITLAB_USER_LOGIN}}
#     app.kubernetes.io/name: {{CI_PROJECT_NAME}}
#     app.kubernetes.io/instance: {{CI_PROJECT_NAME}}
#     app.kubernetes.io/version: "{{VER}}"
#     app.kubernetes.io/component: {{APP_TIER}}
#     app.kubernetes.io/part-of: {{DOMAIN_SOLUTION}}
#     app.kubernetes.io/managed-by: {{DOMAIN}}
#     dg.domain: {{DOMAIN}}
#     dg.environment: {{CI_ENVIRONMENT_SLUG}}
#     dg.app: {{CI_PROJECT_NAME}}
#     dg.version: "{{VER}}"
#     dg.app-tech-stack: {{APP_TECH_STACK}}
#   annotations:
# spec:
#   parentRefs:
#   - kind: Gateway
#     name: "gateway-{{DOMAIN}}-internal"
#     namespace: "gateway-{{DOMAIN}}-internal"
#   hostnames:
#   - {{KUBE_APP_HOST}}
#   rules:
#   - matches:
#     - path:
#         value: /{{INGRESS_BASE_PATH}}
#     backendRefs:
#     - name: {{KUBE_SERVICE_NAME}}
#       port: {{PORT_MAPPING}} 
#---
# apiVersion: networking.gke.io/v1
# kind: HealthCheckPolicy
# metadata:
#   name: "{{CI_PROJECT_NAME}}-healthcheck"
#   namespace: {{KUBE_NAMESPACE}}
# spec:
#   default:
#     config:
#       httpHealthCheck:
#         host: {{KUBE_APP_CNAME}}
#         port: {{PORT_MAPPING}}
#         portSpecification: USE_FIXED_PORT
#         requestPath: "/{{INGRESS_BASE_PATH}}/health/live"
#       type: HTTP
#   targetRef:
#     group: ''
#     kind: Service
#     name: {{KUBE_SERVICE_NAME}}
#     namespace: {{KUBE_NAMESPACE}}
