import DataCatalog from '@/app/data-catalog/DataCatalog'
import DataCatalogPage from '@/app/data-catalog/page'
import { fireEvent, render, screen } from '@testing-library/react'

jest.mock('@/components/common/layout/GenericLayout', () => ({
    __esModule: true,
    default: ({ children }: { children: React.ReactNode }) => <div data-testid="generic-layout">{children}</div>,
}))

describe('DataCatalogPage', () => {
    it('should render the DataCatalogPage component with mocked GenericLayout', () => {
        render(<DataCatalogPage />)

        // Check if the GenericLayout is mocked and rendered
        expect(screen.getByTestId('generic-layout')).toBeInTheDocument()

        // Check if the PageTitle is rendered
        expect(screen.getByText('Data Catalog')).toBeInTheDocument()
    })

    it('should render the DataCatalog component', () => {
        render(<DataCatalog />)

        // Check if the header is rendered
        expect(screen.getByText('Why Choose Our Data Catalog?')).toBeInTheDocument()
        expect(
            screen.getByText(
                'Empower your organization with a modern data catalog that brings clarity and efficiency to your data management.',
            ),
        ).toBeInTheDocument()

        // Check if all cards are rendered
        expect(screen.getByText('Smart Search')).toBeInTheDocument()
        expect(screen.getByText('Data Lineage')).toBeInTheDocument()
        expect(screen.getByText('Governance')).toBeInTheDocument()
        expect(screen.getByText('Data Assets')).toBeInTheDocument()
    })

    it('should highlight the selected card and display its details', () => {
        render(<DataCatalog />)

        // Click on the "Data Lineage" card
        const dataLineageCard = screen.getByText('Data Lineage')
        fireEvent.click(dataLineageCard)

        // Check if the card is highlighted
        expect(dataLineageCard.closest('div.MuiPaper-root')).toHaveStyle('border: 1px solid #2196f3')

        // Check if the details section updates
        expect(screen.getByText('Experience Data Lineage')).toBeInTheDocument()
        expect(screen.getByText('End-to-End Traceability')).toBeInTheDocument()
        expect(
            screen.getByText('Track data from source systems through transformations to consumption.'),
        ).toBeInTheDocument()
    })

    it('should display the link to Collibra', () => {
        render(<DataCatalog />)

        // Check if the "Learn More About Collibra" link is rendered
        const link = screen.getByText('Learn More About Collibra')
        expect(link).toBeInTheDocument()
        expect(link.closest('a')).toHaveAttribute('href', 'https://dollargeneral-dev.collibra.com/apps/')
    })
})
