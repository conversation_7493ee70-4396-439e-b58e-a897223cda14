package catalogReportService

import (
	// "dng-module/internal/app/catalogReportService"
	"fmt"
	"net/http"
	"os"
	"testing"

	"github.com/jarcoal/httpmock"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestGetAccessToken_Success(t *testing.T) {
	os.Setenv("AZURE_TENANT_ID", "test-tenant")
	os.Setenv("AZURE_CLIENT_ID", "test-client-id")
	os.Setenv("AZURE_CLIENT_SECRET", "test-secret")

	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	authURL := "https://login.microsoftonline.com/test-tenant/oauth2/v2.0/token"

	httpmock.RegisterResponder("POST", authURL, func(req *http.Request) (*http.Response, error) {
		resp := httpmock.NewStringResponse(200, `{"access_token": "mock-access-token"}`)
		return resp, nil
	})

	token, err := GetAccessToken()
	assert.NoError(t, err)
	assert.Equal(t, "mock-access-token", token)
}

func TestGetAccessToken_MissingEnvVars(t *testing.T) {
	os.Unsetenv("AZURE_TENANT_ID")
	os.Unsetenv("AZURE_CLIENT_ID")
	os.Unsetenv("AZURE_CLIENT_SECRET")

	token, err := GetAccessToken()
	assert.Error(t, err)
	assert.Empty(t, token)
	assert.EqualError(t, err, "missing Azure AD environment variables")
}

func TestGetAccessToken_FailureFromServer(t *testing.T) {
	os.Setenv("AZURE_TENANT_ID", "test-tenant")
	os.Setenv("AZURE_CLIENT_ID", "test-client-id")
	os.Setenv("AZURE_CLIENT_SECRET", "test-secret")

	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	authURL := "https://login.microsoftonline.com/test-tenant/oauth2/v2.0/token"
	httpmock.RegisterResponder("POST", authURL,
		httpmock.NewStringResponder(401, "unauthorized"))

	_, err := GetAccessToken()
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "failed to get access token")
}

func TestGetEmbedToken_Success(t *testing.T) {
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	groupID := "test-group"
	reportID := "test-report"
	accessToken := "mock-access-token"

	embedURL := "https://api.powerbi.com/v1.0/myorg/groups/test-group/reports/test-report/GenerateToken"
	httpmock.RegisterResponder("POST", embedURL, func(req *http.Request) (*http.Response, error) {
		resp := httpmock.NewStringResponse(200, `{"token": "mock-embed-token"}`)
		return resp, nil
	})

	token, err := GetEmbedToken(groupID, reportID, accessToken)
	assert.NoError(t, err)
	assert.Equal(t, "mock-embed-token", token)
}

func TestGetEmbedToken_EmptyResponseToken(t *testing.T) {
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	groupID := "test-group"
	reportID := "test-report"
	accessToken := "mock-access-token"

	embedURL := "https://api.powerbi.com/v1.0/myorg/groups/test-group/reports/test-report/GenerateToken"
	httpmock.RegisterResponder("POST", embedURL,
		httpmock.NewStringResponder(200, `{"token": ""}`))

	token, err := GetEmbedToken(groupID, reportID, accessToken)
	assert.Error(t, err)
	assert.Empty(t, token)
	assert.EqualError(t, err, "empty embed token received")
}

func TestGetAccessToken_InvalidJSONResponse(t *testing.T) {
	os.Setenv("AZURE_TENANT_ID", "test-tenant")
	os.Setenv("AZURE_CLIENT_ID", "test-client-id")
	os.Setenv("AZURE_CLIENT_SECRET", "test-secret")

	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	authURL := "https://login.microsoftonline.com/test-tenant/oauth2/v2.0/token"
	httpmock.RegisterResponder("POST", authURL,
		httpmock.NewStringResponder(200, "invalid-json"))

	_, err := GetAccessToken()
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "invalid character")
}
func TestGetEmbedToken_InvalidJSONResponse(t *testing.T) {
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	groupID := "test-group"
	reportID := "test-report"
	accessToken := "mock-access-token"

	embedURL := "https://api.powerbi.com/v1.0/myorg/groups/test-group/reports/test-report/GenerateToken"
	httpmock.RegisterResponder("POST", embedURL,
		httpmock.NewStringResponder(200, "invalid-json"))

	_, err := GetEmbedToken(groupID, reportID, accessToken)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "invalid character")
}
func TestGetEmbedToken_ServerError(t *testing.T) {
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	groupID := "test-group"
	reportID := "test-report"
	accessToken := "mock-access-token"

	embedURL := "https://api.powerbi.com/v1.0/myorg/groups/test-group/reports/test-report/GenerateToken"
	httpmock.RegisterResponder("POST", embedURL,
		httpmock.NewStringResponder(500, "Internal Server Error"))

	_, err := GetEmbedToken(groupID, reportID, accessToken)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "failed to get embed token")
}

func TestGetPowerBIMetaData_Success(t *testing.T) {
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	groupID := "test-group"
	reportID := "test-report"
	accessToken := "mock-access-token"

	apiURL := fmt.Sprintf("https://api.powerbi.com/v1.0/myorg/groups/%s/reports/%s", groupID, reportID)
	httpmock.RegisterResponder("GET", apiURL, func(req *http.Request) (*http.Response, error) {
		resp := httpmock.NewStringResponse(200, `{
            "id": "report-id",
            "name": "Test Report",
            "webUrl": "https://powerbi.com/report",
            "embedUrl": "https://powerbi.com/embed",
            "datasetId": "dataset-id"
        }`)
		return resp, nil
	})

	metadata, err := GetPowerBIMetaData(groupID, reportID, accessToken)
	assert.NoError(t, err)
	assert.Equal(t, "report-id", metadata.ID)
	assert.Equal(t, "Test Report", metadata.Name)
	assert.Equal(t, "https://powerbi.com/report", metadata.WebURL)
	assert.Equal(t, "https://powerbi.com/embed", metadata.EmbedURL)
	assert.Equal(t, "dataset-id", metadata.DatasetID)
}

func TestGetPowerBIMetaData_APIError(t *testing.T) {
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	groupID := "test-group"
	reportID := "test-report"
	accessToken := "mock-access-token"

	apiURL := fmt.Sprintf("https://api.powerbi.com/v1.0/myorg/groups/%s/reports/%s", groupID, reportID)
	httpmock.RegisterResponder("GET", apiURL,
		httpmock.NewStringResponder(500, "Internal Server Error"))

	_, err := GetPowerBIMetaData(groupID, reportID, accessToken)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "Power BI API returned status 500")
}

func TestGetPowerBIMetaData_InvalidJSONResponse(t *testing.T) {
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	groupID := "test-group"
	reportID := "test-report"
	accessToken := "mock-access-token"

	apiURL := fmt.Sprintf("https://api.powerbi.com/v1.0/myorg/groups/%s/reports/%s", groupID, reportID)
	httpmock.RegisterResponder("GET", apiURL,
		httpmock.NewStringResponder(200, "invalid-json"))

	_, err := GetPowerBIMetaData(groupID, reportID, accessToken)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "invalid character")
}

func TestGetDatasetIDForMyWorkspaceReport(t *testing.T) {
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	reportID := "mock-report"
	token := "mock-token"
	url := fmt.Sprintf("https://api.powerbi.com/v1.0/myorg/reports/%s", reportID)

	t.Run("Returns error on non-200 status", func(t *testing.T) {
		httpmock.RegisterResponder("GET", url,
			httpmock.NewStringResponder(404, "not found"))

		id, err := GetDatasetIDForMyWorkspaceReport(reportID, token)
		require.Error(t, err)
		require.Contains(t, err.Error(), "failed to fetch report details")
		require.Equal(t, "", id)
	})

	t.Run("Returns error on missing dataset ID", func(t *testing.T) {
		httpmock.RegisterResponder("GET", url,
			httpmock.NewJsonResponderOrPanic(200, map[string]string{}))

		id, err := GetDatasetIDForMyWorkspaceReport(reportID, token)
		require.Error(t, err)
		require.Contains(t, err.Error(), "dataset ID not found")
		require.Equal(t, "", id)
	})
}
