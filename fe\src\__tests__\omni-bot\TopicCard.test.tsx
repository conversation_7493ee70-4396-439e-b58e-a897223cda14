import '@testing-library/jest-dom'
import { fireEvent, render, screen } from '@testing-library/react'
import TopicCard from '../../app/omni-bot/components/TopicCard'

describe('TopicCard', () => {
    it('renders title and description', () => {
        render(<TopicCard title="Test Topic" description="Test Desc" onReadClick={() => {}} />)
        expect(screen.getByText('Test Topic')).toBeInTheDocument()
        expect(screen.getByText('Test Desc')).toBeInTheDocument()
    })

    it('calls onReadClick when button is clicked', () => {
        const onReadClick = jest.fn()
        render(<TopicCard title="Test Topic" description="Test Desc" onReadClick={onReadClick} />)
        fireEvent.click(screen.getByRole('button', { name: /Read Now/i }))
        expect(onReadClick).toHaveBeenCalled()
    })
})
