import { styled } from '@mui/material'
import Image from 'next/image'
import Link from 'next/link'

const LinkStyled = styled(Link)(() => ({
    height: '40px',
    width: '180px',
    overflow: 'hidden',
    display: 'block',
}))

export const Logo = () => {
    return (
        <LinkStyled href="/">
            <Image src="/images/logos/DG-full-logo.svg" alt="logo" height={40} width={180} priority />
        </LinkStyled>
    )
}

export const LogoSmall = () => {
    return (
        <LinkStyled href="/">
            <Image src="/images/logos/DG-short.svg" alt="logo" height={40} width={40} priority />
        </LinkStyled>
    )
}
