'use client'

import { Box, Button, Typography } from '@mui/material'
import { useRouter } from 'next/navigation'
import React from 'react'

interface ErrorProps {
    error: Error
    reset: () => void
}

const ErrorComponent: React.FC<ErrorProps> = ({ error, reset }) => {
    const router = useRouter()

    return (
        <Box sx={{ padding: 4, textAlign: 'center', color: 'error.main' }}>
            <Typography variant="h4" gutterBottom>
                Something went wrong!
            </Typography>
            <Typography variant="body1" gutterBottom>
                {error.message}
            </Typography>
            <Button variant="contained" color="error" onClick={reset} sx={{ marginTop: 2, marginRight: 2 }}>
                Retry
            </Button>
            <Button variant="outlined" color="primary" onClick={() => router.push('/')} sx={{ marginTop: 2 }}>
                Go Home
            </Button>
        </Box>
    )
}

export default ErrorComponent
