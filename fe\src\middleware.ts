import { withAuth } from 'next-auth/middleware'
import { NextRequest } from 'next/server'
import { SUPER_ADMIN } from './utils/constants'

const authMiddleware = withAuth({
    callbacks: {
        authorized: ({ token, req }) => {
            const url = req.nextUrl.pathname

            // Check if the user is authorized for the '/alerts' route
            if (url.startsWith('/alerts')) {
                const user = token as unknown as any
                return user?.details.role_name === SUPER_ADMIN
            }

            // Default authorization check
            return !!token
        },
    },
    secret: process.env.NEXTAUTH_SECRET,
    pages: {
        signIn: '/authentication/login',
    },
})

export default function middleware(req: NextRequest) {
    return (authMiddleware as any)(req)
}

export const config = {
    //matcher: ['/', '/users', "/dashboard"]
    matcher: [
        /*
         * Match all request paths except for the ones starting with:
         * - api (API routes)
         * - _next/static (static files)
         * - _next/image (image optimization files)
         * - favicon.ico (favicon file)
         */
        '/((?!api|_next/static|_next/image|favicon.ico|images).*)',
    ],
}
