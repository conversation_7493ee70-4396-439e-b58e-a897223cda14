import { Chat as ChatType, Topic as TopicType, TopicContext as TopicContextType } from './types';
import {
  TOPICS,
  CHATS,
  TOPIC_CONTEXT,
  TOPIC_KEYWORDS,
  getTopicIdToKeyMap as getIdToKeyMap
} from './data/omnibotData';

// Service Functions - These act as the API layer that would normally fetch from a backend
export const fetchTopicsService = async (): Promise<TopicType[]> => {
  return TOPICS;
};

export const fetchChatsService = async (): Promise<ChatType[]> => {
  return CHATS;
};

export const createChatService = async (data: { title: string }): Promise<ChatType> => {
  const newChat = { id: `c${Date.now()}`, title: data.title };
  // In a real app, this would call an API to create a new chat
  // For now, we'll just return the new chat
  return newChat;
};

export const fetchTopicContextService = async (): Promise<TopicContextType> => {
  return TOPIC_CONTEXT;
};

export const fetchTopicChartsService = async (topicId: number): Promise<any[]> => {
  const topic = TOPICS.find(t => t.id === topicId);
  return topic?.charts || [];
};

// Helper function to generate topic ID to context key mapping
export const getTopicIdToKeyMap = (): Record<string, string> => {
  return getIdToKeyMap();
};

// Helper function to get keywords for topic detection
export const getTopicKeywords = (): Record<string, string[]> => {
  return TOPIC_KEYWORDS;
};

