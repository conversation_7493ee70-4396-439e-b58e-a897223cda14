'use client'

import { Button, Typography } from '@mui/material'

type ErrorProps = {
    error: Error & { digest?: string }
    reset: () => void
}

export default function HandleError({ error, reset }: Readonly<ErrorProps>) {
    return (
        <Typography variant="h4" textAlign="center" py={3}>
            Something went wrong loading reports page!
            <br />
            <br />
            <Button onClick={reset} variant="contained" color="info">
                Try Again
            </Button>
        </Typography>
    )
}
