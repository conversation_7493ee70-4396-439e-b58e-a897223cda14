package catalogReportService

import (
	"dng-module/config"
	// "dng-module/internal/app/catalogReportService"
	"dng-module/testing/utilsTest"
	"fmt"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
)

func TestDeleteReports_Success(t *testing.T) {
	db, mock := utilsTest.SetupMockDB(t)
	defer utilsTest.CloseMockDB(db)

	userID := 10
	reportIDs := []int{100, 200}

	// 1. Mock query to get user email (before transaction)
	mock.ExpectQuery(`SELECT "email" FROM "users" WHERE id = \$1 ORDER BY "users"\."id" LIMIT \$2`).
		WithArgs(userID, 1).
		WillReturnRows(sqlmock.NewRows([]string{"email"}).AddRow("<EMAIL>"))

	// 2. Mock query to fetch user_domain_access with roles join (before transaction)
	mock.ExpectQuery(`SELECT user_domain_access\.domain_id, r\.can_edit, r\.is_super_admin FROM "user_domain_access" JOIN roles r ON user_domain_access\.role_id = r\.id WHERE user_domain_access\.user_id = \$1`).
		WithArgs(userID).
		WillReturnRows(sqlmock.NewRows([]string{"domain_id", "can_edit", "is_super_admin"}).
			AddRow(1, true, false).
			AddRow(2, true, false))

	// 3. Mock query to fetch reports by IDs (before transaction)
	mock.ExpectQuery(`SELECT \* FROM "reports" WHERE id IN \(\$1,\$2\)`).
		WithArgs(reportIDs[0], reportIDs[1]).
		WillReturnRows(sqlmock.NewRows([]string{"id", "domain_id"}).
			AddRow(reportIDs[0], 1).
			AddRow(reportIDs[1], 2))

	// 4. Now expect transaction begin
	mock.ExpectBegin()

	// 5. Expect delete from user_report_preference for the reports (inside transaction)
	mock.ExpectExec(`DELETE FROM "user_report_preference" WHERE report_id IN \(\$1,\$2\)`).
		WithArgs(reportIDs[0], reportIDs[1]).
		WillReturnResult(sqlmock.NewResult(0, 2))

	// 6. Expect delete from reports (inside transaction)
	mock.ExpectExec(`DELETE FROM "reports" WHERE id IN \(\$1,\$2\)`).
		WithArgs(reportIDs[0], reportIDs[1]).
		WillReturnResult(sqlmock.NewResult(0, 2))

	// 7. Expect transaction commit
	mock.ExpectCommit()

	// Swap global DB to the mocked DB in config package (adjust according to your setup)
	config.DB = db

	err := DeleteReports(userID, reportIDs)
	assert.NoError(t, err)

	// Assert all expectations met
	assert.NoError(t, mock.ExpectationsWereMet())
}
func TestDeleteReports_PermissionDenied(t *testing.T) {
	db, mock := utilsTest.SetupMockDB(t)
	defer func() {
		sqlDB, _ := db.DB()
		sqlDB.Close()
	}()

	userID := 10
	reportIDs := []int{100}

	// 1. Mock query to get user email with 2 args: userID, limit (1)
	mock.ExpectQuery(`SELECT "email" FROM "users" WHERE id = \$1 ORDER BY "users"\."id" LIMIT \$2`).
		WithArgs(userID, 1).
		WillReturnRows(sqlmock.NewRows([]string{"email"}).AddRow("<EMAIL>"))

	// 2. Mock query to fetch user_domain_access with roles join - no edit permission
	mock.ExpectQuery(`SELECT user_domain_access\.domain_id, r\.can_edit, r\.is_super_admin FROM "user_domain_access" JOIN roles r ON user_domain_access.role_id = r.id WHERE user_domain_access.user_id = \$1`).
		WithArgs(userID).
		WillReturnRows(sqlmock.NewRows([]string{"domain_id", "can_edit", "is_super_admin"}).
			AddRow(1, false, false))

	// 3. Mock query to fetch reports by IDs
	mock.ExpectQuery(`SELECT \* FROM "reports" WHERE id IN \(\$1\)`).
		WithArgs(reportIDs[0]).
		WillReturnRows(sqlmock.NewRows([]string{"id", "domain_id"}).
			AddRow(reportIDs[0], 2)) // domain 2 which user cannot edit

	config.DB = db

	err := DeleteReports(userID, reportIDs)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "permission denied")

	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestDeleteReports_NoReportIDs(t *testing.T) {
	db, _ := utilsTest.SetupMockDB(t)
	defer utilsTest.CloseMockDB(db)

	config.DB = db

	err := DeleteReports(1, []int{})
	assert.Error(t, err)
	assert.Equal(t, "no report IDs provided", err.Error())
}

func TestDeleteReports_UserEmailFetchError(t *testing.T) {
	db, mock := utilsTest.SetupMockDB(t)
	defer utilsTest.CloseMockDB(db)

	userID := 10
	reportIDs := []int{100}

	// Simulate error fetching user email
	mock.ExpectQuery(`SELECT "email" FROM "users" WHERE id = \$1 ORDER BY "users"\."id" LIMIT \$2`).
		WithArgs(userID, 1).
		WillReturnError(fmt.Errorf("DB error"))

	// Mock domain access and reports as usual (happy path)
	mock.ExpectQuery(`SELECT user_domain_access\.domain_id, r\.can_edit, r\.is_super_admin FROM "user_domain_access" JOIN roles r ON user_domain_access\.role_id = r\.id WHERE user_domain_access\.user_id = \$1`).
		WithArgs(userID).
		WillReturnRows(sqlmock.NewRows([]string{"domain_id", "can_edit", "is_super_admin"}).
			AddRow(1, true, false))

	mock.ExpectQuery(`SELECT \* FROM "reports" WHERE id IN \(\$1\)`).
		WithArgs(reportIDs[0]).
		WillReturnRows(sqlmock.NewRows([]string{"id", "domain_id"}).
			AddRow(reportIDs[0], 1))

	mock.ExpectBegin()
	mock.ExpectExec(`DELETE FROM "user_report_preference" WHERE report_id IN \(\$1\)`).
		WithArgs(reportIDs[0]).
		WillReturnResult(sqlmock.NewResult(0, 1))
	mock.ExpectExec(`DELETE FROM "reports" WHERE id IN \(\$1\)`).
		WithArgs(reportIDs[0]).
		WillReturnResult(sqlmock.NewResult(0, 1))
	mock.ExpectCommit()

	config.DB = db
	err := DeleteReports(userID, reportIDs)
	assert.NoError(t, err)
}

func TestDeleteReports_UserDomainAccessError(t *testing.T) {
	db, mock := utilsTest.SetupMockDB(t)
	defer utilsTest.CloseMockDB(db)

	userID := 10
	reportIDs := []int{100}

	mock.ExpectQuery(`SELECT "email" FROM "users" WHERE id = \$1 ORDER BY "users"\."id" LIMIT \$2`).
		WithArgs(userID, 1).
		WillReturnRows(sqlmock.NewRows([]string{"email"}).AddRow("<EMAIL>"))

	// Simulate error on fetching domain access
	mock.ExpectQuery(`SELECT user_domain_access\.domain_id, r\.can_edit, r\.is_super_admin FROM "user_domain_access" JOIN roles r ON user_domain_access\.role_id = r\.id WHERE user_domain_access\.user_id = \$1`).
		WithArgs(userID).
		WillReturnError(fmt.Errorf("DB error"))

	config.DB = db
	err := DeleteReports(userID, reportIDs)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "DB error")
}

func TestDeleteReports_NoReportsFound(t *testing.T) {
	db, mock := utilsTest.SetupMockDB(t)
	defer utilsTest.CloseMockDB(db)

	userID := 10
	reportIDs := []int{100}

	mock.ExpectQuery(`SELECT "email" FROM "users" WHERE id = \$1 ORDER BY "users"\."id" LIMIT \$2`).
		WithArgs(userID, 1).
		WillReturnRows(sqlmock.NewRows([]string{"email"}).AddRow("<EMAIL>"))

	mock.ExpectQuery(`SELECT user_domain_access\.domain_id, r\.can_edit, r\.is_super_admin FROM "user_domain_access" JOIN roles r ON user_domain_access\.role_id = r\.id WHERE user_domain_access\.user_id = \$1`).
		WithArgs(userID).
		WillReturnRows(sqlmock.NewRows([]string{"domain_id", "can_edit", "is_super_admin"}).
			AddRow(1, true, false))

	// Simulate no reports found
	mock.ExpectQuery(`SELECT \* FROM "reports" WHERE id IN \(\$1\)`).
		WithArgs(reportIDs[0]).
		WillReturnRows(sqlmock.NewRows([]string{"id", "domain_id"})) // empty result

	config.DB = db
	err := DeleteReports(userID, reportIDs)
	assert.Error(t, err)
	assert.Equal(t, "no matching reports found", err.Error())
}

func TestDeleteReports_ReportsFetchError(t *testing.T) {
	db, mock := utilsTest.SetupMockDB(t)
	defer utilsTest.CloseMockDB(db)

	userID := 10
	reportIDs := []int{100}

	mock.ExpectQuery(`SELECT "email" FROM "users" WHERE id = \$1 ORDER BY "users"\."id" LIMIT \$2`).
		WithArgs(userID, 1).
		WillReturnRows(sqlmock.NewRows([]string{"email"}).AddRow("<EMAIL>"))

	mock.ExpectQuery(`SELECT user_domain_access\.domain_id, r\.can_edit, r\.is_super_admin FROM "user_domain_access" JOIN roles r ON user_domain_access\.role_id = r\.id WHERE user_domain_access\.user_id = \$1`).
		WithArgs(userID).
		WillReturnRows(sqlmock.NewRows([]string{"domain_id", "can_edit", "is_super_admin"}).
			AddRow(1, true, false))

	// Simulate error fetching reports
	mock.ExpectQuery(`SELECT \* FROM "reports" WHERE id IN \(\$1\)`).
		WithArgs(reportIDs[0]).
		WillReturnError(fmt.Errorf("DB error"))

	config.DB = db
	err := DeleteReports(userID, reportIDs)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "DB error")
}

func TestDeleteReports_TransactionBeginError(t *testing.T) {
	db, mock := utilsTest.SetupMockDB(t)
	defer utilsTest.CloseMockDB(db)

	userID := 10
	reportIDs := []int{100}

	mock.ExpectQuery(`SELECT "email" FROM "users" WHERE id = \$1 ORDER BY "users"\."id" LIMIT \$2`).
		WithArgs(userID, 1).
		WillReturnRows(sqlmock.NewRows([]string{"email"}).AddRow("<EMAIL>"))

	mock.ExpectQuery(`SELECT user_domain_access\.domain_id, r\.can_edit, r\.is_super_admin FROM "user_domain_access" JOIN roles r ON user_domain_access\.role_id = r\.id WHERE user_domain_access\.user_id = \$1`).
		WithArgs(userID).
		WillReturnRows(sqlmock.NewRows([]string{"domain_id", "can_edit", "is_super_admin"}).
			AddRow(1, true, false))

	mock.ExpectQuery(`SELECT \* FROM "reports" WHERE id IN \(\$1\)`).
		WithArgs(reportIDs[0]).
		WillReturnRows(sqlmock.NewRows([]string{"id", "domain_id"}).
			AddRow(reportIDs[0], 1))

	// Simulate transaction begin error
	mock.ExpectBegin().WillReturnError(fmt.Errorf("tx begin error"))

	config.DB = db
	err := DeleteReports(userID, reportIDs)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "tx begin error")
}

func TestDeleteReports_DeletePreferencesError(t *testing.T) {
	db, mock := utilsTest.SetupMockDB(t)
	defer utilsTest.CloseMockDB(db)

	userID := 10
	reportIDs := []int{100}

	mock.ExpectQuery(`SELECT "email" FROM "users" WHERE id = \$1 ORDER BY "users"\."id" LIMIT \$2`).
		WithArgs(userID, 1).
		WillReturnRows(sqlmock.NewRows([]string{"email"}).AddRow("<EMAIL>"))

	mock.ExpectQuery(`SELECT user_domain_access\.domain_id, r\.can_edit, r\.is_super_admin FROM "user_domain_access" JOIN roles r ON user_domain_access\.role_id = r\.id WHERE user_domain_access\.user_id = \$1`).
		WithArgs(userID).
		WillReturnRows(sqlmock.NewRows([]string{"domain_id", "can_edit", "is_super_admin"}).
			AddRow(1, true, false))

	mock.ExpectQuery(`SELECT \* FROM "reports" WHERE id IN \(\$1\)`).
		WithArgs(reportIDs[0]).
		WillReturnRows(sqlmock.NewRows([]string{"id", "domain_id"}).
			AddRow(reportIDs[0], 1))

	mock.ExpectBegin()

	// Simulate error deleting from user_report_preference
	mock.ExpectExec(`DELETE FROM "user_report_preference" WHERE report_id IN \(\$1\)`).
		WithArgs(reportIDs[0]).
		WillReturnError(fmt.Errorf("delete preferences error"))

	mock.ExpectRollback()

	config.DB = db
	err := DeleteReports(userID, reportIDs)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "delete preferences error")
}

func TestDeleteReports_DeleteReportsError(t *testing.T) {
	db, mock := utilsTest.SetupMockDB(t)
	defer utilsTest.CloseMockDB(db)

	userID := 10
	reportIDs := []int{100}

	mock.ExpectQuery(`SELECT "email" FROM "users" WHERE id = \$1 ORDER BY "users"\."id" LIMIT \$2`).
		WithArgs(userID, 1).
		WillReturnRows(sqlmock.NewRows([]string{"email"}).AddRow("<EMAIL>"))

	mock.ExpectQuery(`SELECT user_domain_access\.domain_id, r\.can_edit, r\.is_super_admin FROM "user_domain_access" JOIN roles r ON user_domain_access\.role_id = r\.id WHERE user_domain_access\.user_id = \$1`).
		WithArgs(userID).
		WillReturnRows(sqlmock.NewRows([]string{"domain_id", "can_edit", "is_super_admin"}).
			AddRow(1, true, false))

	mock.ExpectQuery(`SELECT \* FROM "reports" WHERE id IN \(\$1\)`).
		WithArgs(reportIDs[0]).
		WillReturnRows(sqlmock.NewRows([]string{"id", "domain_id"}).
			AddRow(reportIDs[0], 1))

	mock.ExpectBegin()

	mock.ExpectExec(`DELETE FROM "user_report_preference" WHERE report_id IN \(\$1\)`).
		WithArgs(reportIDs[0]).
		WillReturnResult(sqlmock.NewResult(0, 1))

	// Simulate error deleting from reports
	mock.ExpectExec(`DELETE FROM "reports" WHERE id IN \(\$1\)`).
		WithArgs(reportIDs[0]).
		WillReturnError(fmt.Errorf("delete reports error"))

	mock.ExpectRollback()

	config.DB = db
	err := DeleteReports(userID, reportIDs)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "delete reports error")
}

func TestDeleteReports_CommitError(t *testing.T) {
	db, mock := utilsTest.SetupMockDB(t)
	defer utilsTest.CloseMockDB(db)

	userID := 10
	reportIDs := []int{100}

	mock.ExpectQuery(`SELECT "email" FROM "users" WHERE id = \$1 ORDER BY "users"\."id" LIMIT \$2`).
		WithArgs(userID, 1).
		WillReturnRows(sqlmock.NewRows([]string{"email"}).AddRow("<EMAIL>"))

	mock.ExpectQuery(`SELECT user_domain_access\.domain_id, r\.can_edit, r\.is_super_admin FROM "user_domain_access" JOIN roles r ON user_domain_access\.role_id = r\.id WHERE user_domain_access\.user_id = \$1`).
		WithArgs(userID).
		WillReturnRows(sqlmock.NewRows([]string{"domain_id", "can_edit", "is_super_admin"}).
			AddRow(1, true, false))

	mock.ExpectQuery(`SELECT \* FROM "reports" WHERE id IN \(\$1\)`).
		WithArgs(reportIDs[0]).
		WillReturnRows(sqlmock.NewRows([]string{"id", "domain_id"}).
			AddRow(reportIDs[0], 1))

	mock.ExpectBegin()

	mock.ExpectExec(`DELETE FROM "user_report_preference" WHERE report_id IN \(\$1\)`).
		WithArgs(reportIDs[0]).
		WillReturnResult(sqlmock.NewResult(0, 1))

	mock.ExpectExec(`DELETE FROM "reports" WHERE id IN \(\$1\)`).
		WithArgs(reportIDs[0]).
		WillReturnResult(sqlmock.NewResult(0, 1))

	// Simulate commit error
	mock.ExpectCommit().WillReturnError(fmt.Errorf("commit error"))

	config.DB = db
	err := DeleteReports(userID, reportIDs)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "commit error")
}
