// app/api/tableau-views/route.ts
import {NextRequest, NextResponse} from 'next/server'

const API_VERSION = process.env.TABLEAU_API_VERSION!

export async function POST(req: NextRequest) {
    try {
        const {access_token, siteId} = await req.json()
        const response = await fetch(
            //`https://prod-useast-b.online.tableau.com/api/${API_VERSION}/sites/${siteId}/views/${viewId}?includeUsageStatistics=true`,
            `https://prod-useast-b.online.tableau.com/api/${API_VERSION}/sites/${siteId}/views?filter=viewUrlName:eq:CriteoEOCview`,
            {
                method: 'GET',
                headers: {
                    'X-Tableau-Auth': access_token,
                    Accept: 'application/json',
                },
            },
        )

        const data = await response.json()

        if (!response.ok) {
            console.error('Error fetching view data:', data)
            return NextResponse.json({error: 'Failed to get view data', details: data}, {status: 500})
        }

        return NextResponse.json({view: data})
    } catch (err) {
        console.error('Request error:', err)
        return NextResponse.json({error: 'Unexpected error', details: err}, {status: 500})
    }
}
