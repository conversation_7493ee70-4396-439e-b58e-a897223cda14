// __tests__/Page.test.tsx

import Page from '@/app/authentication/login/page'
import * as authActions from '@/components/actions/authActions'
import { render, screen, waitFor } from '@testing-library/react'
import * as nextNavigation from 'next/navigation'

// Mock the redirect function
jest.mock('next/navigation', () => ({
    ...jest.requireActual('next/navigation'),
    redirect: jest.fn(),
    useSearchParams: jest.fn(),
}))

// Mock the isAuthenticated function
jest.mock('@/components/actions/authActions', () => ({
    isAuthenticated: jest.fn(),
}))

beforeEach(() => {
    // Always return a searchParams object with a get method
    ;(require('next/navigation').useSearchParams as jest.Mock).mockReturnValue({
        get: () => null,
    })
})

describe('Auth Page', () => {
    afterEach(() => {
        jest.clearAllMocks()
    })

    it('redirects to home if authenticated', async () => {
        ;(authActions.isAuthenticated as jest.Mock).mockResolvedValue(true)

        // Call the server component manually since it's async
        await Page()

        expect(nextNavigation.redirect).toHaveBeenCalledWith('/')
    })

    it('renders login page if not authenticated', async () => {
        ;(authActions.isAuthenticated as jest.Mock).mockResolvedValue(false)

        const PageComponent = await Page()

        render(PageComponent)

        // Wait for the heading to appear
        await waitFor(() => {
            expect(screen.getByText(/DNG Enterprise/i)).toBeInTheDocument()
        })
    })
})
