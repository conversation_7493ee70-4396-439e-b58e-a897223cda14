'use client'
import { ArrowBackIos, ArrowForwardIos } from '@mui/icons-material'
import { Box, Button, IconButton, Paper, Typography, useMediaQuery, useTheme } from '@mui/material'
import React, { useState } from 'react'
import { Topic } from '../types'

interface TopicsCarouselProps {
    topics: Topic[]
    onReadClick: (topicId: number) => void
    onTopicClick: (topicId: number) => void
}

const TopicsCarousel: React.FC<TopicsCarouselProps> = ({ topics, onReadClick, onTopicClick }) => {
    // Use MUI breakpoints for itemsPerPage
    const theme = useTheme()
    const isXl = useMediaQuery(theme.breakpoints.up('xl'))
    const isLg = useMediaQuery(theme.breakpoints.up('lg'))
    const isMd = useMediaQuery(theme.breakpoints.up('md'))
    const isSm = useMediaQuery(theme.breakpoints.up('sm'))

    let itemsPerPage = 1
    if (isXl) itemsPerPage = 5
    else if (isLg) itemsPerPage = 4
    else if (isMd) itemsPerPage = 3
    else if (isSm) itemsPerPage = 2

    const [currentPage, setCurrentPage] = useState(0)

    // Defensive: topics must be array and have at least one element
    if (!Array.isArray(topics) || topics.length === 0) {
        return (
            <Box sx={{ width: '100%', textAlign: 'center', py: 4 }}>
                <Typography variant="body1" color="text.secondary">
                    No topics available.
                </Typography>
            </Box>
        )
    }

    const totalPages = Math.ceil((topics.length - 1) / itemsPerPage) // Excluding the featured topic

    const handlePrevPage = () => {
        setCurrentPage((prev) => (prev > 0 ? prev - 1 : totalPages - 1))
    }

    const handleNextPage = () => {
        setCurrentPage((prev) => (prev < totalPages - 1 ? prev + 1 : 0))
    }

    // Get current page topics (excluding the featured topic)
    const getCurrentPageTopics = () => {
        const start = currentPage * itemsPerPage
        return topics.length > 1 ? topics.slice(1).slice(start, start + itemsPerPage) : []
    }

    const currentTopics = getCurrentPageTopics()

    // Function to get background image for a topic
    const getTopicCardBackground = (index: number) => {
        const actualIndex = index + currentPage * itemsPerPage
        if (actualIndex < 3) {
            const extension = actualIndex === 0 ? '.png' : '.jpg'
            return `url(/images/omni-bot/topic-${actualIndex + 1}${extension})`
        } else {
            // Use a darker gradient for better text contrast
            return 'linear-gradient(135deg, #E65100 0%, #FF9800 100%)'
        }
    }

    // Defensive: topics[0] may not exist
    const featuredTopic = topics[0]
    if (!featuredTopic) {
        return null
    }

    return (
        <Box sx={{ width: '100%', position: 'relative' }}>
            {/* Featured Topic */}
            <Box
                sx={{
                    width: '100%',
                    height: { xs: '200px', sm: '250px', md: '300px' },
                    padding: { xs: '24px', md: '32px' },
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'flex-end',
                    alignItems: 'flex-start',
                    gap: '4px',
                    bgcolor: '#f5f5f5',
                    borderRadius: 4,
                    boxShadow: '0 10px 30px rgba(0,0,0,0.1)',
                    backgroundImage: 'url(/images/omni-bot/chart.png)',
                    backgroundSize: 'cover',
                    backgroundPosition: 'center',
                    position: 'relative',
                    flexShrink: 0,
                    transition: 'all 0.3s ease',
                    cursor: 'pointer',
                    mb: 4,
                    '&::before': {
                        content: '""',
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        width: '100%',
                        height: '100%',
                        background: 'linear-gradient(to top, rgba(0,0,0,0.9), rgba(0,0,0,0.6) 50%, rgba(0,0,0,0.4))',
                        borderRadius: '16px',
                        zIndex: 0,
                    },
                }}
                onClick={() => onTopicClick(featuredTopic.id)}
            >
                <Box
                    sx={{
                        position: 'relative',
                        zIndex: 1,
                        color: 'white',
                        display: 'flex',
                        flexDirection: 'column',
                        height: '100%',
                        padding: { xs: '16px', sm: '24px', md: '30px' },
                        width: '100%',
                    }}
                >
                    <Box sx={{ maxWidth: '80%' }}>
                        <Typography
                            variant="caption"
                            sx={{
                                opacity: 1,
                                bgcolor: 'rgba(255, 193, 7, 0.8)',
                                color: 'black',
                                px: 1.5,
                                py: 0.5,
                                borderRadius: '12px',
                                fontWeight: 'bold',
                                display: 'inline-block',
                                mb: 1,
                                boxShadow: '0 2px 4px rgba(0,0,0,0.2)',
                            }}
                        >
                            FEATURED TOPIC
                        </Typography>
                        <Typography
                            variant="h5"
                            fontWeight="bold"
                            sx={{
                                mb: 1,
                                textShadow: '0 2px 4px rgba(0,0,0,0.7)',
                                color: 'white',
                                letterSpacing: '0.5px',
                            }}
                        >
                            {featuredTopic.title}
                        </Typography>
                        <Typography
                            variant="body1"
                            sx={{
                                mb: 2,
                                opacity: 1,
                                display: '-webkit-box',
                                WebkitLineClamp: 3,
                                WebkitBoxOrient: 'vertical',
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                                textShadow: '0 2px 4px rgba(0,0,0,0.7)',
                                color: 'white',
                                fontWeight: 'medium',
                            }}
                        >
                            {featuredTopic.description}
                        </Typography>
                    </Box>
                    <Box
                        sx={{
                            mt: 'auto',
                            display: 'flex',
                            alignItems: 'center',
                            width: '100%',
                            justifyContent: 'flex-start',
                        }}
                    >
                        <Button
                            variant="contained"
                            disableElevation={false}
                            onClick={(e) => {
                                e.stopPropagation()
                                onReadClick(featuredTopic.id)
                            }}
                            sx={{
                                backgroundColor: '#FFC107 !important',
                                color: 'black',
                                borderRadius: '16px',
                                px: 3,
                                py: 1,
                                height: '44px',
                                minWidth: '180px',
                                width: { xs: '100%', sm: '60%', md: '40%' },
                                fontWeight: 'bold',
                                border: '2px solid #FFC107',
                                boxShadow: '0 4px 12px rgba(255, 193, 7, 0.4)',
                                '&:hover': {
                                    backgroundColor: '#FFB000 !important',
                                    transform: 'translateY(-2px)',
                                    boxShadow: '0 6px 15px rgba(255, 193, 7, 0.5)',
                                },
                                transition: 'all 0.2s ease',
                            }}
                        >
                            Read More
                        </Button>
                    </Box>
                </Box>
            </Box>

            {/* Topic Carousel Section */}
            <Box sx={{ mb: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                    All Topics
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Typography variant="body2" color="text.secondary">
                        Page {totalPages === 0 ? 1 : currentPage + 1} of {totalPages === 0 ? 1 : totalPages}
                    </Typography>
                    <IconButton
                        onClick={handlePrevPage}
                        size="small"
                        sx={{
                            bgcolor: 'rgba(0,0,0,0.05)',
                            '&:hover': { bgcolor: 'rgba(0,0,0,0.1)' },
                        }}
                        disabled={totalPages <= 1}
                    >
                        <ArrowBackIos fontSize="small" sx={{ fontSize: 14 }} />
                    </IconButton>
                    <IconButton
                        onClick={handleNextPage}
                        size="small"
                        sx={{
                            bgcolor: 'rgba(0,0,0,0.05)',
                            '&:hover': { bgcolor: 'rgba(0,0,0,0.1)' },
                        }}
                        disabled={totalPages <= 1}
                    >
                        <ArrowForwardIos fontSize="small" sx={{ fontSize: 14 }} />
                    </IconButton>
                </Box>
            </Box>

            {/* Topic Cards */}
            <Box
                sx={{
                    display: 'grid',
                    gridTemplateColumns: {
                        xs: '1fr',
                        sm: 'repeat(2, 1fr)',
                        md: 'repeat(3, 1fr)',
                        lg: 'repeat(4, 1fr)',
                        xl: 'repeat(5, 1fr)',
                    },
                    gap: 3,
                    width: '100%',
                    mb: 4,
                }}
            >
                {currentTopics && currentTopics.length > 0 ? (
                    currentTopics.map((topic, index) => (
                        <Paper
                            key={topic.id}
                            elevation={0}
                            sx={{
                                display: 'flex',
                                flexDirection: 'column',
                                padding: '16px',
                                gap: '12px',
                                borderRadius: '24px',
                                background: '#FFF',
                                boxShadow: '0 5px 15px rgba(0,0,0,0.05)',
                                minHeight: '250px',
                                height: '100%',
                                transition: 'all 0.3s ease',
                                cursor: 'pointer',
                                '&:hover': {
                                    transform: 'translateY(-5px)',
                                    boxShadow: '0 8px 25px rgba(0,0,0,0.1)',
                                },
                            }}
                            onClick={() => onTopicClick(topic.id)}
                        >
                            <Box
                                sx={{
                                    width: '100%',
                                    height: '80px',
                                    borderRadius: '16px',
                                    backgroundImage: getTopicCardBackground(index),
                                    backgroundSize: 'cover',
                                    backgroundPosition: 'center',
                                    flexShrink: 0,
                                    boxShadow: '0 4px 8px rgba(0,0,0,0.1)',
                                    position: 'relative',
                                    '&::before': {
                                        content: '""',
                                        position: 'absolute',
                                        top: 0,
                                        left: 0,
                                        width: '100%',
                                        height: '100%',
                                        background: 'linear-gradient(to top, rgba(0,0,0,0.7), rgba(0,0,0,0.4))',
                                        borderRadius: '16px',
                                    },
                                }}
                            />
                            <Box
                                sx={{
                                    flexGrow: 1,
                                    display: 'flex',
                                    flexDirection: 'column',
                                    height: '100%',
                                    py: 1,
                                }}
                            >
                                <Box>
                                    <Typography
                                        variant="subtitle1"
                                        fontWeight="bold"
                                        gutterBottom
                                        sx={{
                                            display: '-webkit-box',
                                            WebkitLineClamp: 1,
                                            WebkitBoxOrient: 'vertical',
                                            overflow: 'hidden',
                                            textOverflow: 'ellipsis',
                                        }}
                                    >
                                        {topic.title}
                                    </Typography>
                                    <Typography
                                        variant="body2"
                                        color="text.secondary"
                                        sx={{
                                            display: '-webkit-box',
                                            WebkitLineClamp: 3,
                                            WebkitBoxOrient: 'vertical',
                                            overflow: 'hidden',
                                            textOverflow: 'ellipsis',
                                            mb: 2,
                                        }}
                                    >
                                        {topic.description}
                                    </Typography>
                                </Box>
                                <Box
                                    sx={{
                                        mt: 'auto',
                                        display: 'flex',
                                        justifyContent: 'center',
                                        pt: 1,
                                        borderTop: '1px solid rgba(0,0,0,0.05)',
                                    }}
                                >
                                    <Button
                                        variant="contained"
                                        size="small"
                                        fullWidth
                                        disableElevation={false}
                                        onClick={(e) => {
                                            e.stopPropagation()
                                            onReadClick(topic.id)
                                        }}
                                        sx={{
                                            backgroundColor: '#FFC107 !important',
                                            color: 'black',
                                            fontWeight: 'bold',
                                            padding: '6px 12px',
                                            minHeight: '36px',
                                            maxWidth: '90%',
                                            borderRadius: '16px',
                                            border: '2px solid #FFC107',
                                            boxShadow: '0 2px 6px rgba(255, 193, 7, 0.4)',
                                            '&:hover': {
                                                backgroundColor: '#FFB000 !important',
                                                transform: 'translateY(-2px)',
                                                boxShadow: '0 4px 8px rgba(255, 193, 7, 0.5)',
                                            },
                                        }}
                                    >
                                        Read More
                                    </Button>
                                </Box>
                            </Box>
                        </Paper>
                    ))
                ) : (
                    <Typography
                        variant="body2"
                        color="text.secondary"
                        sx={{ gridColumn: '1/-1', textAlign: 'center', py: 4 }}
                    >
                        No additional topics to display.
                    </Typography>
                )}
            </Box>
        </Box>
    )
}

export default TopicsCarousel
