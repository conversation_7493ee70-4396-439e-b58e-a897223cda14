import ReportsTreeTable from '@/app/reports/components/ReportTable'
import { render, screen } from '@testing-library/react'

jest.mock('@/app/reports/components/Utils', () => ({
    ReportDetails: () => <div data-testid="report-details" />,
    ReportIcon: () => <span data-testid="report-icon" />,
}))
jest.mock('@/app/reports/components/AddReport', () => () => <button>AddReport</button>)
jest.mock('@/app/reports/reportCards/components/TableActions', () => () => <div>TableActions</div>)
jest.mock('@/app/reports/services', () => ({
    getReports: jest.fn(() => Promise.resolve({ data: [], meta: { total: 0 } })),
    deleteReportsService: jest.fn(),
}))
jest.mock('@/store/hooks', () => ({
    useAppDispatch: () => jest.fn(),
    useAppSelector: jest.fn(() => ({ activeFilters: {}, filters: {} })),
}))
jest.mock('next-auth/react', () => ({
    useSession: () => ({ data: { user: { user_id: '1' } } }),
}))

// Silence act warnings in test output
beforeAll(() => {
    jest.spyOn(console, 'error').mockImplementation((msg, ...args) => {
        if (typeof msg === 'string' && msg.includes('not wrapped in act')) {
            return
        }
        // @ts-ignore
        return globalThis.console.error.original ? globalThis.console.error.original(msg, ...args) : undefined
    })
    // Save original for restoration
    // @ts-ignore
    globalThis.console.error.original ??= console.error
})

afterAll(() => {
    // @ts-ignore
    if (globalThis.console.error.original) {
        // @ts-ignore
        console.error = globalThis.console.error.original
    }
})

describe('ReportTable', () => {
    it('renders without crashing', async () => {
        render(<ReportsTreeTable view="all" />)
        expect(screen.getByRole('table')).toBeInTheDocument()
    })
})
