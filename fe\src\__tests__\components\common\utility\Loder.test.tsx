import { render, screen } from '@testing-library/react'
import Loader from '@/components/common/utility/Loader'
import '@testing-library/jest-dom'

describe('Loader Component', () => {
    it('renders circular loader by default', () => {
        render(<Loader />)
        expect(screen.getByRole('progressbar')).toBeInTheDocument()
    })

    it('renders linear loader', () => {
        render(<Loader type="linear" />)
        expect(screen.getByRole('progressbar')).toBeInTheDocument()
    })

    it('renders skeleton loader', () => {
        const { container } = render(<Loader type="skeleton" />)
        const skeleton = container.querySelector('.MuiSkeleton-root')
        expect(skeleton).toBeInTheDocument()
    })
})
