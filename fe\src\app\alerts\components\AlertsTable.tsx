'use client'

import DeleteConfirmDialog from '@/app/reports/reportCards/components/DeleteConfirmDialog'
import RemoteDataTable from '@/components/common/table/RemoteDataTable'
import { formatEventTimeRange } from '@/utils/helper'
import { snackbar } from '@/utils/toast'
import { Add, DeleteForever, Edit } from '@mui/icons-material'
import { Box, Button, Chip, Grid2 as Grid, IconButton, Switch, Tooltip, Typography } from '@mui/material'
import { capitalize } from 'lodash'
import {
    MRT_ColumnDef,
    MRT_ColumnFiltersState,
    MRT_SortingState,
    MRT_TableInstance,
    MRT_GlobalFilterTextField as MRTGlobalFilterTextField,
} from 'material-react-table'
import { useCallback, useMemo, useRef, useState } from 'react'
import { deleteAlertsService, editAlertsService, getAlertsService } from '../services'
import { IAlert } from '../types'
import AlertsModal, { TYPE_ICONS } from './AlertsModal'

const fetchNewsAlerts = async (
    pageIndex: number,
    pageSize: number,
    globalFilter: string,
    columnFilters: MRT_ColumnFiltersState,
    sorting: MRT_SortingState,
) => {
    const skip = pageIndex * pageSize
    const searchParams = new URLSearchParams({
        limit: String(pageSize),
        skip: String(skip),
    })

    if (globalFilter) {
        searchParams.append('search', globalFilter)
    }

    columnFilters.forEach((filter) => {
        searchParams.append(filter.id, filter.value as string)
    })

    if (sorting.length > 0) {
        const sortField = sorting[0].id
        const sortDirection = sorting[0].desc ? 'desc' : 'asc'
        searchParams.append('sortBy', sortField)
        searchParams.append('order', sortDirection)
    }

    const response = await getAlertsService(searchParams.toString())

    return {
        data: response.data,
        rowCount: response.totalCount,
    }
}

export default function AlertsTable() {
    const [open, setOpen] = useState(false)
    const [rowData, setRowData] = useState<IAlert | undefined>(undefined)
    const [forceRefresh, setForceRefresh] = useState(false)
    const [openDialog, setOpenDialog] = useState(false)
    const tableRef = useRef<MRT_TableInstance<IAlert> | null>(null)
    const [rowSelection, setRowSelection] = useState<Record<string, boolean>>({})

    const handleClose = () => {
        setOpen(false)
        setRowData(undefined)
    }

    const handleOpen = useCallback(() => setOpen(true), [setOpen])

    const handleEdit = useCallback(
        (row: IAlert) => {
            setRowData(row)
            handleOpen()
        },
        [setRowData, handleOpen],
    )

    const handleSave = async () => {
        try {
            setForceRefresh((prev) => !prev)
        } catch (error) {
            snackbar.error('Failed to save alert')
            console.log('Error saving alert:', error)
        }
    }

    const handleDeleteSelected = async () => {
        try {
            const ids = Object.keys(rowSelection)
            await deleteAlertsService(ids)
            snackbar.success('Deleted successfully')
            setOpenDialog(false)
            clearRowSelection()
            setForceRefresh((prev) => !prev)
        } catch (error) {
            snackbar.error('Failed to delete alerts')
            setOpenDialog(false)
            console.log('Error deleting alerts:', error)
        }
    }

    const clearRowSelection = () => {
        setRowSelection({})
        tableRef.current?.resetRowSelection()
    }

    // -------------------------
    // 🧹 Clean Cell Renderers
    // -------------------------

    const renderTypeCell = useCallback(
        (value: string) => (
            <Typography variant="body2" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                {TYPE_ICONS[value]}
                {capitalize(value)}
            </Typography>
        ),
        [],
    )

    const renderDescriptionCell = useCallback(
        (value: string) => (
            <Tooltip title={value}>
                <Box
                    sx={{
                        whiteSpace: 'nowrap',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        maxWidth: 200,
                    }}
                >
                    {value}
                </Box>
            </Tooltip>
        ),
        [],
    )

    const renderStatusCell = useCallback((status: string) => {
        let color: 'success' | 'warning' | 'error' = 'error'

        if (status === 'active') {
            color = 'success'
        } else if (status === 'scheduled') {
            color = 'warning'
        }

        return <Chip label={capitalize(status)} color={color} size="small" variant="filled" />
    }, [])

    const renderEventDatesCell = useCallback((startDate: string, endDate: Date) => {
        const eventDates = formatEventTimeRange(startDate, endDate)
        return (
            <Tooltip title={eventDates}>
                <Typography variant="body2">{formatEventTimeRange(startDate, endDate, true)}</Typography>
            </Tooltip>
        )
    }, [])

    const renderVisibilityCell = useCallback(
        (isVisible: boolean, row: IAlert) => {
            const handleToggleVisibility = async () => {
                try {
                    const updatedAlert = { ...row, is_visible: !isVisible }
                    await editAlertsService(updatedAlert)
                    snackbar.success('Visibility updated successfully')
                    setForceRefresh((prev) => !prev)
                } catch (error) {
                    snackbar.error('Failed to update visibility')
                    console.log('Error updating visibility:', error)
                }
            }

            return (
                <Tooltip title={isVisible ? 'Visible' : 'Hidden'}>
                    <Switch checked={isVisible} onChange={handleToggleVisibility} color="info" size="small" />
                </Tooltip>
            )
        },
        [setForceRefresh],
    )

    const renderActionsCell = useCallback(
        (row: IAlert) => (
            <Box sx={{ display: 'flex', gap: '0.5rem' }}>
                <IconButton size="small" onClick={() => handleEdit(row)}>
                    <Edit fontSize="small" />
                </IconButton>
            </Box>
        ),
        [handleEdit],
    )

    // -------------------------
    // Columns
    // -------------------------

    const columns = useMemo<MRT_ColumnDef<IAlert>[]>(
        () => [
            { accessorKey: 'title', header: 'Title', maxSize: 70 },
            {
                accessorKey: 'type',
                header: 'Type',
                maxSize: 70,
                Cell: ({ cell }) => renderTypeCell(cell.getValue<string>()),
            },
            {
                accessorKey: 'description',
                header: 'Description',
                Cell: ({ cell }) => renderDescriptionCell(cell.getValue<string>()),
            },
            {
                accessorKey: 'status',
                header: 'Status',
                maxSize: 70,
                Cell: ({ cell }) => renderStatusCell(cell.getValue<string>()),
            },
            {
                accessorKey: 'start_date_time',
                header: 'Event Dates',
                maxSize: 70,
                Cell: ({ cell, row }) =>
                    renderEventDatesCell(cell.getValue<string>(), row.original.end_date_time as Date),
            },
            {
                accessorKey: 'is_visible',
                header: 'Visibility',
                enableSorting: false,
                maxSize: 70,
                Cell: ({ cell, row }) => renderVisibilityCell(cell.getValue<boolean>(), row.original),
            },
            {
                id: 'actions',
                header: 'Actions',
                enableSorting: false,
                enableColumnFilter: false,
                maxSize: 70,
                Cell: ({ row }) => renderActionsCell(row.original),
            },
        ],
        [
            renderTypeCell,
            renderDescriptionCell,
            renderStatusCell,
            renderEventDatesCell,
            renderVisibilityCell,
            renderActionsCell,
        ],
    )

    const renderTableActions = (addNewHandler: () => void, table: MRT_TableInstance<IAlert>) => {
        tableRef.current = table
        const selectedIds = Object.keys(rowSelection)
        return (
            <Box sx={{ display: 'flex', gap: '0.5rem' }}>
                {!!selectedIds.length && (
                    <Box display="flex" alignItems="center">
                        <Typography variant="body2" sx={{ alignSelf: 'center' }}>
                            {selectedIds.length} rows selected
                            <Button
                                variant="text"
                                color="info"
                                onClick={clearRowSelection}
                                size="small"
                                sx={{ fontSize: '10px' }}
                            >
                                Clear Selection
                            </Button>
                        </Typography>
                    </Box>
                )}
                <Button color="success" onClick={addNewHandler} size="small" variant="contained" startIcon={<Add />}>
                    Alerts
                </Button>
                <Button
                    color="error"
                    size="small"
                    disabled={selectedIds.length === 0}
                    onClick={() => setOpenDialog(true)}
                    variant="contained"
                    startIcon={<DeleteForever />}
                >
                    Delete
                </Button>
            </Box>
        )
    }

    return (
        <>
            <Grid container spacing={2} mt={2}>
                <RemoteDataTable
                    title="News Alerts"
                    columns={columns}
                    fetchData={fetchNewsAlerts}
                    enableRowSelection
                    renderActions={(_, table) => renderTableActions(handleOpen, table)}
                    getRowId={(originalRow) => ('id' in originalRow ? originalRow.id : String(originalRow.name))}
                    rowSelection={rowSelection}
                    onRowSelectionChange={setRowSelection}
                    renderTopToolbarFilters={(table) => (
                        <MRTGlobalFilterTextField
                            sx={{
                                '& .MuiOutlinedInput-root': { borderRadius: '20px' },
                                '& input': { padding: '6px' },
                            }}
                            table={table}
                        />
                    )}
                    forceRefresh={forceRefresh}
                />
            </Grid>

            {/* NewsAlertModal */}
            {open && <AlertsModal open={open} onClose={handleClose} rowData={rowData} onSaveSuccess={handleSave} />}

            {/* DeleteConfirmDialog */}
            <DeleteConfirmDialog
                open={openDialog}
                onClose={() => setOpenDialog(false)}
                onConfirm={handleDeleteSelected}
                reportCount={Object.keys(rowSelection).length}
            />
        </>
    )
}
