'use client'

import SearchAutoComplete from '@/app/reports/reportSearch/components/SearchAutoComplete'
import { setActiveFilters, setSearchText, toggleFiltersPanel } from '@/app/reports/slice'
import { useAppDispatch, useAppSelector } from '@/store/hooks'
import FilterListIcon from '@mui/icons-material/FilterList'
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown'
import KeyboardArrowUpIcon from '@mui/icons-material/KeyboardArrowUp'
import { Box, Button, Card, CardContent, Collapse, Grid2 as Grid, Paper } from '@mui/material'
import { isEmpty } from 'lodash'
import { useSession } from 'next-auth/react'
import { useEffect, useState } from 'react'
import { fetchFilterOptions } from '../slice'
import FilterPanel from './FilterPanel'

// Feature Flag
const BULK_UPLOAD = process.env.NEXT_PUBLIC_BULK_UPLOAD_ENABLED === 'true'

export default function ReportsHeader() {
    const { filters, activeFilters, filtersOpen } = useAppSelector((store) => store.reports)
    const { filterOptions, loading } = useAppSelector((store) => store.filterOptions)
    const [searchVal, setSearchVal] = useState<string>(filters.search)
    const dispatch = useAppDispatch()

    const { data: session } = useSession()
    const userId = session?.user?.id ?? ''

    // Fetch filter options on component mount
    useEffect(() => {
        if (isEmpty(filterOptions.category)) {
            dispatch(fetchFilterOptions(userId))
        }
    }, [dispatch])

    // Update local state if Redux state changes
    useEffect(() => {
        setSearchVal(filters.search)
    }, [filters.search])

    const handleSearchChange = (event: any, newValue: string) => {
        setSearchVal(newValue)
        dispatch(setSearchText(newValue))
    }

    const toggleFilters = () => {
        if (loading !== 'pending') dispatch(toggleFiltersPanel())
    }

    const handleApplyFilters = (filters: {
        sourceSystem: string
        domain: string
        category: string
        dates: [Date | null, Date | null]
    }) => {
        dispatch(setActiveFilters(filters))
    }

    return (
        <Grid size={{ xs: 12, md: 12, lg: 12 }}>
            <Card sx={{ p: 0, position: 'relative' }} elevation={9}>
                <CardContent>
                    <Box display="flex" justifyContent="space-between" alignItems="center" flexWrap="wrap" gap={2}>
                        {/* Left side: Search */}
                        <SearchAutoComplete
                            value={searchVal}
                            onChange={handleSearchChange}
                            sx={{
                                flexGrow: 1,
                                maxWidth: { xs: '100%', sm: 400 },
                                '& .MuiInputBase-root': {
                                    borderRadius: 20,
                                    //border: '1px solid #ccc',
                                },
                            }}
                        />

                        {/* Right side: Filters and Add Report */}
                        <Box display="flex" alignItems="center" gap={2}>
                            <Button
                                variant="outlined"
                                startIcon={<FilterListIcon />}
                                disabled={loading === 'pending'}
                                color="secondary"
                                endIcon={filtersOpen ? <KeyboardArrowUpIcon /> : <KeyboardArrowDownIcon />}
                                onClick={toggleFilters}
                                size="small"
                                sx={{
                                    borderRadius: 20,
                                    px: 2,
                                    whiteSpace: 'nowrap',
                                }}
                            >
                                Filters
                            </Button>
                        </Box>
                    </Box>

                    <Collapse in={filtersOpen} timeout="auto" unmountOnExit>
                        <Paper
                            elevation={0}
                            sx={{
                                mt: 2,
                                p: 1,
                                border: '1px solid #e0e0e0',
                                borderRadius: 2,
                            }}
                        >
                            <FilterPanel
                                onApply={handleApplyFilters}
                                initialValues={activeFilters}
                                filterOptions={filterOptions}
                            />
                        </Paper>
                    </Collapse>
                </CardContent>
            </Card>
        </Grid>
    )
}
