package model

import "time"

// ReportSearchOption is the DTO for a single report in the response.
type ReportSearchOption struct {
	ID           int    `json:"id"`
	Name         string `json:"name"`
	Category     string `json:"category"`
	DomainName   string `json:"domain_name"`
	SourceSystem string `json:"source_system"`
}

// ReportResponse represents the response format for a single report.
type ReportResponse struct {
	ID           int       `json:"id"` // Converted from uint to string
	Name         string    `json:"name"`
	ReportID     string    `json:"report_id"`
	GroupID      *string   `json:"group_id"` // Nullable Group ID
	Description  string    `json:"description"`
	Category     string    `json:"category"`
	DomainID     int       `json:"domain_id,omitempty"`
	DomainName   string    `json:"domain_name"`
	SourceSystem string    `json:"source_system"`
	ReportUrl    string    `json:"report_url"`
	CreatedBy    string    `json:"created_by"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
}

// Meta - Pagination metadata
type Meta struct {
	Total      int `json:"total"`
	Page       int `json:"page"`
	Limit      int `json:"limit"`
	TotalPages int `json:"total_pages"`
}

type ReportWithPreference struct {
	ID           int        `json:"id"`
	Name         string     `json:"name"`
	ReportID     string     `json:"report_id"`
	Description  string     `json:"description"`
	Category     string     `json:"category"`
	DomainID     int        `json:"domain_id"`
	SourceSystem string     `json:"source_system"`
	GroupID      string     `json:"group_id,omitempty"`
	CreatedBy    string     `json:"created_by"`
	CreatedAt    string     `json:"created_at"`
	UpdatedAt    string     `json:"updated_at"`
	PublishedOn  *time.Time `json:"published_on" type:"date"`
	ReportUrl     string    `json:"report_url"`
	// Preference flags
	IsFavorite bool `json:"is_favorite"`
	IsSaved    bool `json:"is_saved"`
}

// DomainWithReportsResponse represents a domain and its associated reports (augmented with preference info).
type DomainWithReportsResponse struct {
	ID          int                    `json:"id"`
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Report      []ReportWithPreference `json:"report"`
}

// GetReportsResponse is the final API response.
type GetReportsResponse struct {
	Domain []DomainWithReportsResponse `json:"data"`
	Meta   Meta                        `json:"meta"` // Pagination metadata
}

// TokenResponse represents the response from Azure AD token endpoint
type TokenResponse struct {
	AccessToken string `json:"access_token"`
}

// EmbedTokenResponse represents the response from Power BI embed token API
type EmbedTokenResponse struct {
	Token string `json:"token"`
}

// PowerBIReportMetaData contains metadata of a Power BI report
type PowerBIReportMetaData struct {
	ID        string `json:"id"`
	Name      string `json:"name"`
	WebURL    string `json:"webUrl"`
	EmbedURL  string `json:"embedUrl"`
	DatasetID string `json:"datasetId"`
	GroupID   string `json:"groupId"`
}

// LookerLoginResponse represents the response from Looker login API
type LookerLoginResponse struct {
	AccessToken string `json:"access_token"`
}

// LookerEmbedResponse represents the response from Looker embed token URL API
type LookerEmbedResponse struct {
	Url string `json:"url"`
}

// LookerDashboardMetadata holds metadata of a Looker dashboard
type LookerDashboardMetadata struct {
	ID    string `json:"id"`
	Title string `json:"title"`
}
