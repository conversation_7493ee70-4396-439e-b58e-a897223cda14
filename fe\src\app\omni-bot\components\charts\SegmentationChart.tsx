'use client'
import React from 'react'
import dynamic from 'next/dynamic'
import { Box, Typography, Paper } from '@mui/material'

// Dynamically import ApexCharts to avoid SSR issues
const Chart = dynamic(() => import('react-apexcharts'), { ssr: false })

const SegmentationChart: React.FC = () => {
    // Chart options
    const options = {
        chart: {
            id: 'customer-segmentation',
            toolbar: {
                show: false
            },
            fontFamily: 'Arial, sans-serif',
        },
        colors: ['#FFC107', '#FF9800', '#FF5722', '#F44336', '#9C27B0'],
        labels: ['High-Value Regulars', 'Occasional Big Spenders', 'Frequent Small Purchases', 'Seasonal Shoppers', 'New Customers'],
        legend: {
            position: 'bottom',
            fontSize: '12px',
            fontFamily: 'Arial, sans-serif',
            labels: {
                colors: '#616161'
            },
            formatter: function(val: string, opts: any) {
                return val + " - " + opts.w.globals.series[opts.seriesIndex] + "%"
            }
        },
        dataLabels: {
            enabled: true,
            formatter: function (val: number) {
                return val.toFixed(1) + "%"
            },
            style: {
                fontSize: '12px',
                fontFamily: 'Arial, sans-serif',
                fontWeight: 'bold',
                colors: ['#fff']
            },
            dropShadow: {
                enabled: false
            }
        },
        plotOptions: {
            pie: {
                donut: {
                    size: '50%',
                    labels: {
                        show: true,
                        name: {
                            show: true,
                            fontSize: '14px',
                            fontFamily: 'Arial, sans-serif',
                            color: '#616161',
                            offsetY: -10
                        },
                        value: {
                            show: true,
                            fontSize: '20px',
                            fontFamily: 'Arial, sans-serif',
                            color: '#111',
                            offsetY: 5,
                            formatter: function (val: number) {
                                return val + "%"
                            }
                        },
                        total: {
                            show: true,
                            label: 'Total Revenue',
                            color: '#616161',
                            fontSize: '14px',
                            fontFamily: 'Arial, sans-serif',
                            formatter: function () {
                                return '100%'
                            }
                        }
                    }
                }
            }
        },
        tooltip: {
            theme: 'light',
            y: {
                formatter: function(value: number) {
                    return value + '% of revenue'
                }
            }
        },
        responsive: [{
            breakpoint: 480,
            options: {
                chart: {
                    height: 300
                },
                legend: {
                    position: 'bottom'
                }
            }
        }]
    }

    // Chart series data
    const series = [35, 25, 20, 15, 5]

    return (
        <Paper 
            elevation={0}
            sx={{
                p: 2,
                borderRadius: 3,
                border: '1px solid',
                borderColor: 'divider',
                backgroundColor: 'white',
                mb: 3
            }}
        >
            <Typography variant="h6" gutterBottom sx={{ fontWeight: 'bold', color: '#424242' }}>
                Customer Segment Revenue Contribution
            </Typography>
            <Typography variant="body2" sx={{ color: 'text.secondary', mb: 2 }}>
                Percentage of total revenue by customer segment
            </Typography>
            <Box sx={{ height: 350 }}>
                <Chart 
                    options={options as any}
                    series={series}
                    type="donut"
                    height="100%"
                    width="100%"
                />
            </Box>
        </Paper>
    )
}

export default SegmentationChart
