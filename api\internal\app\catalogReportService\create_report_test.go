package catalogReportService

import (
	"dng-module/config"
	// "dng-module/internal/app/catalogReportService"
	"dng-module/internal/model"
	"dng-module/testing/utilsTest"
	"fmt"
	"regexp"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"
)

func TestCreateReport_Success_Create(t *testing.T) {
	db, mock := utilsTest.SetupMockDB(t)
	defer utilsTest.CloseMockDB(db)
	config.DB = db

	userEmail := "<EMAIL>"
	domainID := 1
	groupID := "group-123"
	reportID := "rep-456"

	reportDto := model.CreateReportDto{
		Name:         "Test Report",
		ReportID:     reportID,
		GroupID:      groupID,
		Description:  "Test description",
		Category:     "Finance",
		DomainID:     domainID,
		SourceSystem: "PowerBI",
		PublishedOn:  func() *time.Time { t := time.Now(); return &t }(),
		CreatedBy:    "<EMAIL>",
	}

	// 1. Fetch user
	mock.ExpectQuery(`SELECT \* FROM "users" WHERE email = \$1 ORDER BY "users"\."id" LIMIT \$2`).
		WithArgs(userEmail, 1).
		WillReturnRows(sqlmock.NewRows([]string{"id", "email"}).AddRow(10, userEmail))

	// 2. User domain access + role permissions
	mock.ExpectQuery(`SELECT uda\.domain_id, r\.can_edit, r\.is_super_admin FROM user_domain_access AS uda JOIN roles r ON uda\.role_id = r\.id WHERE uda\.user_id = \$1`).
		WithArgs(10).
		WillReturnRows(sqlmock.NewRows([]string{"domain_id", "can_edit", "is_super_admin"}).
			AddRow(domainID, true, false))

	// 3. Check domain existence
	mock.ExpectQuery(`SELECT "name" FROM "domains" WHERE id = \$1 ORDER BY "domains"\."id" LIMIT \$2`).
		WithArgs(domainID, 1).
		WillReturnRows(sqlmock.NewRows([]string{"name"}).AddRow("Finance Domain"))

	// 4. Ensure report_id is unique
	mock.ExpectQuery(`SELECT \* FROM "reports" WHERE report_id = \$1 ORDER BY "reports"\."id" LIMIT \$2`).
		WithArgs(reportID, 1).
		WillReturnError(gorm.ErrRecordNotFound)

	// 5. Insert new report
	mock.ExpectBegin()
	now := time.Now()
	mock.ExpectQuery(regexp.QuoteMeta(`INSERT INTO "reports"`)).
		WithArgs(
			"Test Report",
			"rep-456",
			"group-123",
			"Test description",
			"Finance",
			1,
			"PowerBI",
			"<EMAIL>",
			sqlmock.AnyArg(), // created_at
			sqlmock.AnyArg(), // updated_at
			sqlmock.AnyArg(), // published_on
		).WillReturnRows(sqlmock.NewRows([]string{"id", "published_on"}).
		AddRow(1, now))

	mock.ExpectCommit()

	// Run
	res, err := CreateReport(reportDto, userEmail)

	assert.NoError(t, err)
	assert.NotNil(t, res)
	assert.Equal(t, reportID, res.ReportID)
	assert.Equal(t, groupID, *res.GroupID)

	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestCreateReport_UserNotFound(t *testing.T) {
	db, mock := utilsTest.SetupMockDB(t)
	defer utilsTest.CloseMockDB(db)
	config.DB = db

	reportDto := model.CreateReportDto{
		Name:        "Test Report",
		DomainID:    1,
		ReportID:    "rep-001",
		CreatedBy:   "<EMAIL>",
		PublishedOn: func() *time.Time { t := time.Now(); return &t }(),
	}

	mock.ExpectQuery(`SELECT \* FROM "users" WHERE email = \$1 ORDER BY "users"\."id" LIMIT \$2`).
		WithArgs("<EMAIL>", 1).
		WillReturnError(gorm.ErrRecordNotFound)

	res, err := CreateReport(reportDto, "<EMAIL>")
	assert.Error(t, err)
	assert.Nil(t, res)
	assert.Contains(t, err.Error(), "user not found")
}

func TestCreateReport_UnauthorizedDomain(t *testing.T) {
	db, mock := utilsTest.SetupMockDB(t)
	defer utilsTest.CloseMockDB(db)
	config.DB = db

	reportDto := model.CreateReportDto{
		Name:        "Test Report",
		DomainID:    2,
		ReportID:    "rep-002",
		CreatedBy:   "<EMAIL>",
		PublishedOn: func() *time.Time { t := time.Now(); return &t }(),
	}

	mock.ExpectQuery(`SELECT \* FROM "users" WHERE email = \$1 ORDER BY "users"\."id" LIMIT \$2`).
		WithArgs("<EMAIL>", 1).
		WillReturnRows(sqlmock.NewRows([]string{"id", "email"}).AddRow(1, "<EMAIL>"))

	// User has access to domain 1 only
	mock.ExpectQuery(`SELECT uda\.domain_id, r\.can_edit, r\.is_super_admin FROM user_domain_access AS uda JOIN roles r ON uda\.role_id = r\.id WHERE uda\.user_id = \$1`).
		WithArgs(1).
		WillReturnRows(sqlmock.NewRows([]string{"domain_id", "can_edit", "is_super_admin"}).
			AddRow(1, true, false))

	res, err := CreateReport(reportDto, "<EMAIL>")
	assert.Error(t, err)
	assert.Nil(t, res)
	assert.Contains(t, err.Error(), "unauthorized access")
}

func TestCreateReport_Success_SuperAdmin(t *testing.T) {
	db, mock := utilsTest.SetupMockDB(t)
	defer utilsTest.CloseMockDB(db)
	config.DB = db

	userEmail := "<EMAIL>"
	domainID := 1
	groupID := "group-123"
	reportID := "rep-456"

	reportDto := model.CreateReportDto{
		Name:         "Test Report",
		ReportID:     reportID,
		GroupID:      groupID,
		Description:  "Test description",
		Category:     "Finance",
		DomainID:     domainID,
		SourceSystem: "PowerBI",
		PublishedOn:  func() *time.Time { t := time.Now(); return &t }(),
		CreatedBy:    "<EMAIL>",
	}

	// 1. Fetch user
	mock.ExpectQuery(`SELECT \* FROM "users" WHERE email = \$1 ORDER BY "users"\."id" LIMIT \$2`).
		WithArgs(userEmail, 1).
		WillReturnRows(sqlmock.NewRows([]string{"id", "email"}).AddRow(10, userEmail))

	// 2. User domain access - super admin
	mock.ExpectQuery(`SELECT uda\.domain_id, r\.can_edit, r\.is_super_admin FROM user_domain_access AS uda JOIN roles r ON uda\.role_id = r\.id WHERE uda\.user_id = \$1`).
		WithArgs(10).
		WillReturnRows(sqlmock.NewRows([]string{"domain_id", "can_edit", "is_super_admin"}).
			AddRow(0, false, true)) // is_super_admin=true

	// 3. Check domain existence
	mock.ExpectQuery(`SELECT "name" FROM "domains" WHERE id = \$1 ORDER BY "domains"\."id" LIMIT \$2`).
		WithArgs(domainID, 1).
		WillReturnRows(sqlmock.NewRows([]string{"name"}).AddRow("Finance Domain"))

	// 4. Ensure report_id is unique
	mock.ExpectQuery(`SELECT \* FROM "reports" WHERE report_id = \$1 ORDER BY "reports"\."id" LIMIT \$2`).
		WithArgs(reportID, 1).
		WillReturnError(gorm.ErrRecordNotFound)

	// 5. Insert new report
	mock.ExpectBegin()
	now := time.Now()
	mock.ExpectQuery(regexp.QuoteMeta(`INSERT INTO "reports"`)).
		WithArgs(
			"Test Report",
			"rep-456",
			"group-123",
			"Test description",
			"Finance",
			1,
			"PowerBI",
			"<EMAIL>",
			sqlmock.AnyArg(), // created_at
			sqlmock.AnyArg(), // updated_at
			sqlmock.AnyArg(), // published_on
		).WillReturnRows(sqlmock.NewRows([]string{"id", "published_on"}).
		AddRow(1, now))

	mock.ExpectCommit()

	// Run
	res, err := CreateReport(reportDto, userEmail)

	assert.NoError(t, err)
	assert.NotNil(t, res)
	assert.Equal(t, reportID, res.ReportID)
	assert.Equal(t, groupID, *res.GroupID)

	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestCreateReport_DomainNotFound(t *testing.T) {
	db, mock := utilsTest.SetupMockDB(t)
	defer utilsTest.CloseMockDB(db)
	config.DB = db

	userEmail := "<EMAIL>"
	domainID := 999 // non-existent domain

	reportDto := model.CreateReportDto{
		Name:        "Test Report",
		DomainID:    domainID,
		ReportID:    "rep-001",
		CreatedBy:   userEmail,
		PublishedOn: func() *time.Time { t := time.Now(); return &t }(),
	}

	// 1. Fetch user
	mock.ExpectQuery(`SELECT \* FROM "users" WHERE email = \$1 ORDER BY "users"\."id" LIMIT \$2`).
		WithArgs(userEmail, 1).
		WillReturnRows(sqlmock.NewRows([]string{"id", "email"}).AddRow(1, userEmail))

	// 2. User domain access
	mock.ExpectQuery(`SELECT uda\.domain_id, r\.can_edit, r\.is_super_admin FROM user_domain_access AS uda JOIN roles r ON uda\.role_id = r\.id WHERE uda\.user_id = \$1`).
		WithArgs(1).
		WillReturnRows(sqlmock.NewRows([]string{"domain_id", "can_edit", "is_super_admin"}).
			AddRow(domainID, true, false))

	// 3. Check domain existence - not found
	mock.ExpectQuery(`SELECT "name" FROM "domains" WHERE id = \$1 ORDER BY "domains"\."id" LIMIT \$2`).
		WithArgs(domainID, 1).
		WillReturnError(gorm.ErrRecordNotFound)

	res, err := CreateReport(reportDto, userEmail)
	assert.Error(t, err)
	assert.Nil(t, res)
	assert.Contains(t, err.Error(), "invalid domain ID")

	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestCreateReport_ReportIDExists(t *testing.T) {
	db, mock := utilsTest.SetupMockDB(t)
	defer utilsTest.CloseMockDB(db)
	config.DB = db

	userEmail := "<EMAIL>"
	domainID := 1
	reportID := "rep-001"

	reportDto := model.CreateReportDto{
		Name:        "Test Report",
		DomainID:    domainID,
		ReportID:    reportID,
		CreatedBy:   userEmail,
		PublishedOn: func() *time.Time { t := time.Now(); return &t }(),
	}

	// 1. Fetch user
	mock.ExpectQuery(`SELECT \* FROM "users" WHERE email = \$1 ORDER BY "users"\."id" LIMIT \$2`).
		WithArgs(userEmail, 1).
		WillReturnRows(sqlmock.NewRows([]string{"id", "email"}).AddRow(1, userEmail))

	// 2. User domain access
	mock.ExpectQuery(`SELECT uda\.domain_id, r\.can_edit, r\.is_super_admin FROM user_domain_access AS uda JOIN roles r ON uda\.role_id = r\.id WHERE uda\.user_id = \$1`).
		WithArgs(1).
		WillReturnRows(sqlmock.NewRows([]string{"domain_id", "can_edit", "is_super_admin"}).
			AddRow(domainID, true, false))

	// 3. Check domain existence
	mock.ExpectQuery(`SELECT "name" FROM "domains" WHERE id = \$1 ORDER BY "domains"\."id" LIMIT \$2`).
		WithArgs(domainID, 1).
		WillReturnRows(sqlmock.NewRows([]string{"name"}).AddRow("Finance Domain"))

	// 4. Check report_id - already exists
	mock.ExpectQuery(`SELECT \* FROM "reports" WHERE report_id = \$1 ORDER BY "reports"\."id" LIMIT \$2`).
		WithArgs(reportID, 1).
		WillReturnRows(sqlmock.NewRows([]string{"id", "report_id"}).AddRow(1, reportID))

	res, err := CreateReport(reportDto, userEmail)
	assert.Error(t, err)
	assert.Nil(t, res)
	assert.Contains(t, err.Error(), "report with this ReportID already exists")

	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestCreateReport_Success_Update(t *testing.T) {
	db, mock := utilsTest.SetupMockDB(t)
	defer utilsTest.CloseMockDB(db)
	config.DB = db

	userEmail := "<EMAIL>"
	domainID := 1
	reportID := "rep-456"
	reportDbID := uint(100)

	reportDto := model.CreateReportDto{
		ID:          int(reportDbID),
		Name:        "Updated Report",
		ReportID:    reportID,
		Description: "Updated description",
		DomainID:    domainID,
		CreatedBy:   userEmail,
		PublishedOn: func() *time.Time { t := time.Now(); return &t }(),
	}

	// 1. Fetch user
	mock.ExpectQuery(`SELECT \* FROM "users" WHERE email = \$1 ORDER BY "users"\."id" LIMIT \$2`).
		WithArgs(userEmail, 1).
		WillReturnRows(sqlmock.NewRows([]string{"id", "email"}).AddRow(10, userEmail))

	// 2. User domain access
	mock.ExpectQuery(`SELECT uda\.domain_id, r\.can_edit, r\.is_super_admin FROM user_domain_access AS uda JOIN roles r ON uda\.role_id = r\.id WHERE uda\.user_id = \$1`).
		WithArgs(10).
		WillReturnRows(sqlmock.NewRows([]string{"domain_id", "can_edit", "is_super_admin"}).
			AddRow(domainID, true, false))

	// 3. Check domain existence
	mock.ExpectQuery(`SELECT "name" FROM "domains" WHERE id = \$1 ORDER BY "domains"\."id" LIMIT \$2`).
		WithArgs(domainID, 1).
		WillReturnRows(sqlmock.NewRows([]string{"name"}).AddRow("Finance Domain"))

	// 4. Find existing report to update
	mock.ExpectQuery(`SELECT \* FROM "reports" WHERE "reports"\."id" = \$1 ORDER BY "reports"\."id" LIMIT \$2`).
		WithArgs(reportDbID, 1).
		WillReturnRows(sqlmock.NewRows([]string{"id", "name", "report_id", "domain_id"}).
			AddRow(reportDbID, "Old Report", reportID, domainID))

	// 5. Update report
	mock.ExpectBegin()
	mock.ExpectExec(regexp.QuoteMeta(`UPDATE "reports"`)).
		WithArgs(
			"Updated Report",      // name
			reportID,              // report_id
			sqlmock.AnyArg(),      // group_id
			"Updated description", // description
			sqlmock.AnyArg(),      // category
			domainID,              // domain_id
			sqlmock.AnyArg(),      // source_system
			sqlmock.AnyArg(),      // created_by
			sqlmock.AnyArg(),      // created_at
			sqlmock.AnyArg(),      // updated_at
			sqlmock.AnyArg(),      // published_on
			reportDbID,            // WHERE id
		).
		WillReturnResult(sqlmock.NewResult(1, 1))

	mock.ExpectCommit()

	res, err := CreateReport(reportDto, userEmail)
	assert.NoError(t, err)
	assert.NotNil(t, res)
	assert.Equal(t, "Updated Report", res.Name)
	assert.Equal(t, "Updated description", res.Description)

	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestCreateReport_Update_ReportNotFound(t *testing.T) {
	db, mock := utilsTest.SetupMockDB(t)
	defer utilsTest.CloseMockDB(db)
	config.DB = db

	userEmail := "<EMAIL>"
	domainID := 1
	reportDbID := uint(999) // non-existent report

	reportDto := model.CreateReportDto{
		ID:        int(reportDbID),
		Name:      "Non-existent Report",
		ReportID:  "rep-999",
		DomainID:  domainID,
		CreatedBy: userEmail,
	}

	// 1. Fetch user
	mock.ExpectQuery(`SELECT \* FROM "users" WHERE email = \$1 ORDER BY "users"\."id" LIMIT \$2`).
		WithArgs(userEmail, 1).
		WillReturnRows(sqlmock.NewRows([]string{"id", "email"}).AddRow(1, userEmail))

	// 2. User domain access
	mock.ExpectQuery(`SELECT uda\.domain_id, r\.can_edit, r\.is_super_admin FROM user_domain_access AS uda JOIN roles r ON uda\.role_id = r\.id WHERE uda\.user_id = \$1`).
		WithArgs(1).
		WillReturnRows(sqlmock.NewRows([]string{"domain_id", "can_edit", "is_super_admin"}).
			AddRow(domainID, true, false))

	// 3. Check domain existence
	mock.ExpectQuery(`SELECT "name" FROM "domains" WHERE id = \$1 ORDER BY "domains"\."id" LIMIT \$2`).
		WithArgs(domainID, 1).
		WillReturnRows(sqlmock.NewRows([]string{"name"}).AddRow("Finance Domain"))

	// 4. Find report to update - not found
	mock.ExpectQuery(`SELECT \* FROM "reports" WHERE "reports"\."id" = \$1 ORDER BY "reports"\."id" LIMIT \$2`).
		WithArgs(reportDbID, 1).
		WillReturnError(gorm.ErrRecordNotFound)

	res, err := CreateReport(reportDto, userEmail)
	assert.Error(t, err)
	assert.Nil(t, res)
	assert.Contains(t, err.Error(), "report not found")

	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestCreateReport_DatabaseError_AccessCheck(t *testing.T) {
	db, mock := utilsTest.SetupMockDB(t)
	defer utilsTest.CloseMockDB(db)
	config.DB = db

	userEmail := "<EMAIL>"

	reportDto := model.CreateReportDto{
		Name:      "Test Report",
		DomainID:  1,
		ReportID:  "rep-001",
		CreatedBy: userEmail,
	}

	// 1. Fetch user
	mock.ExpectQuery(`SELECT \* FROM "users" WHERE email = \$1 ORDER BY "users"\."id" LIMIT \$2`).
		WithArgs(userEmail, 1).
		WillReturnRows(sqlmock.NewRows([]string{"id", "email"}).AddRow(1, userEmail))

	// 2. User domain access - database error
	mock.ExpectQuery(`SELECT uda\.domain_id, r\.can_edit, r\.is_super_admin FROM user_domain_access AS uda JOIN roles r ON uda\.role_id = r\.id WHERE uda\.user_id = \$1`).
		WithArgs(1).
		WillReturnError(fmt.Errorf("database error"))

	res, err := CreateReport(reportDto, userEmail)
	assert.Error(t, err)
	assert.Nil(t, res)
	assert.Contains(t, err.Error(), "database error")

	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestCreateReport_DatabaseError_Create(t *testing.T) {
	db, mock := utilsTest.SetupMockDB(t)
	defer utilsTest.CloseMockDB(db)
	config.DB = db

	userEmail := "<EMAIL>"
	domainID := 1
	reportID := "rep-456"

	reportDto := model.CreateReportDto{
		Name:        "Test Report",
		ReportID:    reportID,
		Description: "Test description",
		DomainID:    domainID,
		CreatedBy:   userEmail,
		PublishedOn: func() *time.Time { t := time.Now(); return &t }(),
	}

	// 1. Fetch user
	mock.ExpectQuery(`SELECT \* FROM "users" WHERE email = \$1 ORDER BY "users"\."id" LIMIT \$2`).
		WithArgs(userEmail, 1).
		WillReturnRows(sqlmock.NewRows([]string{"id", "email"}).AddRow(10, userEmail))

	// 2. User domain access
	mock.ExpectQuery(`SELECT uda\.domain_id, r\.can_edit, r\.is_super_admin FROM user_domain_access AS uda JOIN roles r ON uda\.role_id = r\.id WHERE uda\.user_id = \$1`).
		WithArgs(10).
		WillReturnRows(sqlmock.NewRows([]string{"domain_id", "can_edit", "is_super_admin"}).
			AddRow(domainID, true, false))

	// 3. Check domain existence
	mock.ExpectQuery(`SELECT "name" FROM "domains" WHERE id = \$1 ORDER BY "domains"\."id" LIMIT \$2`).
		WithArgs(domainID, 1).
		WillReturnRows(sqlmock.NewRows([]string{"name"}).AddRow("Finance Domain"))

	// 4. Ensure report_id is unique
	mock.ExpectQuery(`SELECT \* FROM "reports" WHERE report_id = \$1 ORDER BY "reports"\."id" LIMIT \$2`).
		WithArgs(reportID, 1).
		WillReturnError(gorm.ErrRecordNotFound)

	// 5. Insert new report - fails
	mock.ExpectBegin()
	mock.ExpectQuery(regexp.QuoteMeta(`INSERT INTO "reports"`)).
		WithArgs(
			sqlmock.AnyArg(), // name
			sqlmock.AnyArg(), // report_id
			sqlmock.AnyArg(), // group_id
			sqlmock.AnyArg(), // description
			sqlmock.AnyArg(), // category
			sqlmock.AnyArg(), // domain_id
			sqlmock.AnyArg(), // source_system
			sqlmock.AnyArg(), // created_by
			sqlmock.AnyArg(), // created_at
			sqlmock.AnyArg(), // updated_at
			sqlmock.AnyArg(), // published_on
		).WillReturnError(fmt.Errorf("database error"))
	mock.ExpectRollback()

	res, err := CreateReport(reportDto, userEmail)
	assert.Error(t, err)
	assert.Nil(t, res)
	assert.EqualError(t, err, "database error")

	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestCreateReport_DatabaseError_Update(t *testing.T) {
	db, mock := utilsTest.SetupMockDB(t)
	defer utilsTest.CloseMockDB(db)
	config.DB = db

	userEmail := "<EMAIL>"
	domainID := 1
	reportID := "rep-456"
	reportDbID := uint(100)

	reportDto := model.CreateReportDto{
		ID:          int(reportDbID),
		Name:        "Updated Report",
		ReportID:    reportID,
		Description: "Updated description",
		DomainID:    domainID,
		CreatedBy:   userEmail,
	}

	// 1. Fetch user
	mock.ExpectQuery(`SELECT \* FROM "users" WHERE email = \$1 ORDER BY "users"\."id" LIMIT \$2`).
		WithArgs(userEmail, 1).
		WillReturnRows(sqlmock.NewRows([]string{"id", "email"}).AddRow(10, userEmail))

	// 2. User domain access
	mock.ExpectQuery(`SELECT uda\.domain_id, r\.can_edit, r\.is_super_admin FROM user_domain_access AS uda JOIN roles r ON uda\.role_id = r\.id WHERE uda\.user_id = \$1`).
		WithArgs(10).
		WillReturnRows(sqlmock.NewRows([]string{"domain_id", "can_edit", "is_super_admin"}).
			AddRow(domainID, true, false))

	// 3. Check domain existence
	mock.ExpectQuery(`SELECT "name" FROM "domains" WHERE id = \$1 ORDER BY "domains"\."id" LIMIT \$2`).
		WithArgs(domainID, 1).
		WillReturnRows(sqlmock.NewRows([]string{"name"}).AddRow("Finance Domain"))

	// 4. Find existing report to update
	mock.ExpectQuery(`SELECT \* FROM "reports" WHERE "reports"\."id" = \$1 ORDER BY "reports"\."id" LIMIT \$2`).
		WithArgs(reportDbID, 1).
		WillReturnRows(sqlmock.NewRows([]string{"id", "name", "report_id", "domain_id"}).
			AddRow(reportDbID, "Old Report", reportID, domainID))

	// 5. Update report - fails
	mock.ExpectBegin()
	mock.ExpectExec(regexp.QuoteMeta(`UPDATE "reports"`)).
		WithArgs(
			sqlmock.AnyArg(), // name
			sqlmock.AnyArg(), // report_id
			sqlmock.AnyArg(), // group_id
			sqlmock.AnyArg(), // description
			sqlmock.AnyArg(), // category
			sqlmock.AnyArg(), // domain_id
			sqlmock.AnyArg(), // source_system
			sqlmock.AnyArg(), // created_by
			sqlmock.AnyArg(), // created_at
			sqlmock.AnyArg(), // updated_at
			sqlmock.AnyArg(), // published_on
			sqlmock.AnyArg(), // id
		).WillReturnError(fmt.Errorf("database error"))

	mock.ExpectRollback()

	res, err := CreateReport(reportDto, userEmail)
	assert.Error(t, err)
	assert.Nil(t, res)
	assert.Contains(t, err.Error(), "database error")

	assert.NoError(t, mock.ExpectationsWereMet())
}
