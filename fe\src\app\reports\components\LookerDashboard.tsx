'use client'

import { LookerEmbedSDK } from '@looker/embed-sdk'
import { Box, CircularProgress, styled, Typography } from '@mui/material'
import { useEffect, useRef, useState } from 'react'
import { lookerEmbedUrlService } from '@/app/reports/services'

interface LookerDashboardProps {
    dashboardId: string
}

// Styled container for the embedded Looker dashboard
export const EmbedContainer = styled('div')(({ theme }) => ({
    width: '100%',
    height: '71vh', // Adjusted height for visual balance
    '& > iframe': {
        width: '100%',
        height: '100%'
    }
}))

const LookerDashboard = ({ dashboardId }: LookerDashboardProps) => {
    const embedContainer = useRef<HTMLDivElement | null>(null)
    const [loading, setLoading] = useState(true)
    const [hasError, setHasError] = useState(false)
    const hasFetched = useRef(false)

    useEffect(() => {
        setHasError(false)
        const fetchEmbedUrl = async () => {
            if (hasFetched.current) return // Prevent duplicate calls
            hasFetched.current = true

            try {
                const url = await lookerEmbedUrlService(dashboardId)
                if (embedContainer.current && url) {
                    LookerEmbedSDK.init(process.env.LOOKERSDK_BASE_URL ?? '') // Replace with your Looker host
                    LookerEmbedSDK.createDashboardWithId(dashboardId)
                        .withUrl(url)
                        .appendTo(embedContainer.current)
                        .on('dashboard:loaded', () => {
                            console.log('dashboard loaded')
                        })
                        .on('dashboard:run:complete', () => console.log('AA'))
                        .on('dashboard:run:start', () => console.log('AA'))
                        .build()
                        .connect()
                        .then(() => setLoading(false))
                        .catch((error) => console.error('Embedding failed', error))
                }
            } catch (error) {
                console.error('Error fetching embed URL', error)
                setHasError(true)
            } finally {
                setLoading(false)
            }
        }
        fetchEmbedUrl()
    }, [dashboardId])

    if (hasError) {
        return (
            <Typography variant='h6' my={10} textAlign='center'>
                Failed to load Looker dashboard
            </Typography>
        )
    }

    return (
        <>
            {loading && (
                <Box display='flex' justifyContent='center' mt={20}>
                    <CircularProgress color='info' size={150} />
                </Box>
            )}
            <EmbedContainer ref={embedContainer} />
        </>
    )
}

export default LookerDashboard
