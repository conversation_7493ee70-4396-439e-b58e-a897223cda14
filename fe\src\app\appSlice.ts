'use client'

import { AuthState } from '@/store/interface'
import { createSlice, PayloadAction } from '@reduxjs/toolkit'

interface AppState {
    language: string
    auth: AuthState
}

const initialState: AppState = {
    language: 'en',
    auth: {
        isAuthenticated: false,
        token: null,
    },
}

const applicationSlice = createSlice({
    name: 'application',
    initialState,
    reducers: {
        setLanguage(state, action: PayloadAction<string>) {
            state.language = action.payload
        },
        login(state, action: PayloadAction<string>) {
            state.auth.isAuthenticated = true
            state.auth.token = action.payload
        },
        logout(state) {
            state.auth.isAuthenticated = false
            state.auth.token = null
        },
    },
})
export const { setLanguage, login, logout } = applicationSlice.actions

export default applicationSlice.reducer
