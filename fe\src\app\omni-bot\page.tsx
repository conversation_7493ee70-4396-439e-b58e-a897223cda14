'use client'
import { snackbar } from '@/utils/toast'
import { Add as AddIcon } from '@mui/icons-material'
import { <PERSON>ert, Box, Fab, Snackbar, Tooltip, useMediaQuery, useTheme } from '@mui/material'
import { useEffect, useState } from 'react'
import ChatInterface from './components/ChatInterface'
import OmnibotSidebar from './components/OmnibotSidebar'
import TopicsGrid from './components/TopicsGrid'
import { createChatService, fetchChatsService, fetchTopicsService } from './services'
import { TOPICS } from './data/omnibotData'
import { Chat, Topic } from './types'

const OmnibotPage = () => {
    const theme = useTheme()
    const isMobile = useMediaQuery(theme.breakpoints.down('md'))
    const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false)
    const [activeChatTitle, setActiveChatTitle] = useState<string | null>(null)
    const [snackbarOpen, setSnackbarOpen] = useState(false)
    const [snackbarMessage, setSnackbarMessage] = useState('')
    const [snackbarSeverity, setSnackbarSeverity] = useState<'success' | 'error'>('success')
    const [topics, setTopics] = useState<Topic[]>([])
    const [chats, setChats] = useState<Chat[]>([])

    useEffect(() => {
        const loadData = async () => {
            try {
                const topicsData = await fetchTopicsService()
                const chatsData = await fetchChatsService()
                setTopics(topicsData)
                setChats(chatsData)
            } catch (error) {
                console.error('Error loading data:', error)
                snackbar.error('Error loading data')
            }
        }

        loadData()
    }, [])

    // Handle chat selection
    const handleChatSelect = (chatTitle: string) => {
        setActiveChatTitle(chatTitle)
        // console.log(`Opened chat: ${chatTitle}`)
    }

    // Handle new chat creation
    const handleNewChat = async () => {
        try {
            const now = new Date()
            const timeString = now.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
            const newChatTitle = `New Chat (${timeString})`

            const newChat = await createChatService({ title: newChatTitle })
            setChats((prevChats) => [...prevChats, newChat])
            setActiveChatTitle(newChatTitle)
        } catch (error) {
            console.error('Error creating new chat:', error)
        }
    }

    // Dynamically generate topic information for the chatbot
    const generateTopicInfo = () => {
        const info: Record<number, { title: string; prompt: string }> = {}

        // Generate prompts based on topic titles and descriptions
        TOPICS.forEach((topic) => {
            // Create a default prompt based on the topic title and description
            const prompt = `Tell me about ${topic.title.toLowerCase()}. ${topic.description}`

            info[topic.id] = {
                title: topic.title,
                prompt: prompt,
            }
        })

        return info
    }

    // Topic information for the chatbot
    const topicInfo = generateTopicInfo()

    // Handle topic read click
    const handleTopicRead = (topicId: number) => {
        console.log(`Reading topic ${topicId}...`)

        // Get the topic info
        const topic = topicInfo[topicId]

        if (!topic) return

        // Create a new chat with the topic title
        const newChatTitle = topic.title

        setActiveChatTitle(newChatTitle)

        // Only store the topic ID, not the prompt
        // This prevents pre-populating the input field
        sessionStorage.setItem('topicId', topicId.toString())
    }

    // Handle chat close
    const handleChatClose = () => {
        setActiveChatTitle(null)
    }

    // Handle chat rename
    const handleChatRename = (chatId: string, newTitle: string) => {
        // In a real app, this would call an API to update the chat title
        console.log(`Renamed chat ${chatId} to "${newTitle}"`)

        // If the active chat is being renamed, update the active chat title
        if (activeChatTitle?.includes(chatId)) {
            setActiveChatTitle(newTitle)
        }

        // Show success message
        setSnackbarMessage(`Chat renamed to "${newTitle}"`)
        setSnackbarSeverity('success')
        setSnackbarOpen(true)
    }

    // Handle chat delete
    const handleChatDelete = (chatId: string) => {
        // In a real app, this would call an API to delete the chat
        console.log(`Deleted chat ${chatId}`)

        // If the active chat is being deleted, close it
        if (activeChatTitle?.includes(chatId)) {
            setActiveChatTitle(null)
        }

        // Show success message
        setSnackbarMessage('Chat deleted successfully')
        setSnackbarSeverity('success')
        setSnackbarOpen(true)
    }

    // Close mobile sidebar when a chat is selected
    useEffect(() => {
        if (isMobile && activeChatTitle) {
            setIsMobileSidebarOpen(false)
        }
    }, [activeChatTitle, isMobile])

    return (
        <Box
            sx={{
                display: 'flex',
                width: '100%',
                height: '100%',
                backgroundColor: 'white',
                fontFamily: 'Arial, sans-serif',
                position: 'relative',
            }}
        >
            {/* Sidebar */}
            <OmnibotSidebar
                isMobile={isMobile}
                isOpen={isMobileSidebarOpen}
                onClose={() => setIsMobileSidebarOpen(false)}
                onChatSelect={handleChatSelect}
                onNewChat={handleNewChat}
                onExplore={handleChatClose}
                showExploreButton={activeChatTitle !== null} // Only show when in chat mode
                onRenameChat={handleChatRename}
                onDeleteChat={handleChatDelete}
                chats={chats}
            />

            {/* Snackbar for notifications */}
            <Snackbar
                open={snackbarOpen}
                autoHideDuration={4000}
                onClose={() => setSnackbarOpen(false)}
                anchorOrigin={{ vertical: 'bottom', horizontal: 'left' }}
            >
                <Alert
                    onClose={() => setSnackbarOpen(false)}
                    severity={snackbarSeverity}
                    variant="filled"
                    sx={{ width: '100%' }}
                >
                    {snackbarMessage}
                </Alert>
            </Snackbar>

            {/* Main Content */}
            <Box
                sx={{
                    flexGrow: 1,
                    ml: isMobile ? 0 : '250px',
                    backgroundColor: 'white',
                    height: '100%',
                    overflow: 'hidden',
                    display: 'flex',
                    flexDirection: 'column',
                    position: 'relative',
                    width: 'calc(100% - 250px)', // Subtract sidebar width
                    maxWidth: isMobile ? '100%' : 'calc(100% - 250px)', // Ensure it doesn't overlap
                }}
            >
                {activeChatTitle ? (
                    <ChatInterface chatTitle={activeChatTitle} onClose={handleChatClose} />
                ) : (
                    <TopicsGrid topics={topics} onReadClick={handleTopicRead} />
                )}
            </Box>

            {/* Floating New Chat Button */}
            <Tooltip title="Start new chat" placement="left">
                <Fab
                    className="new-chat-button"
                    color="primary"
                    size="large"
                    aria-label="start new chat"
                    onClick={handleNewChat}
                    sx={{
                        position: 'fixed',
                        bottom: 32,
                        right: { xs: 28, sm: 40, md: 56 },
                        backgroundColor: '#FFC107',
                        color: 'black',
                        '&:hover': {
                            backgroundColor: '#FFB000',
                            transform: 'scale(1.05)',
                            boxShadow: '0 6px 12px rgba(255, 193, 7, 0.4)',
                        },
                        transition: 'all 0.2s ease',
                        zIndex: 1000,
                    }}
                >
                    <AddIcon />
                </Fab>
            </Tooltip>
        </Box>
    )
}

export default OmnibotPage
