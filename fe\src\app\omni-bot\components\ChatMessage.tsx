import { Box, Divider, Paper, Typography } from '@mui/material'
import React from 'react'
import { Message } from '../types'

interface ChatMessageProps {
    message: Message
}

const ChatMessage: React.FC<ChatMessageProps> = ({ message }) => {
    const isUser = message.sender === 'user'

    return (
        <Box
            sx={{
                display: 'flex',
                justifyContent: isUser ? 'flex-end' : 'flex-start',
                mb: 2,
            }}
        >
            <Paper
                elevation={isUser ? 1 : 0}
                sx={{
                    p: 2,
                    maxWidth: isUser ? '70%' : '85%', // Wider for bot messages with insights
                    borderRadius: isUser ? '32px' : '24px',
                    background: isUser ? '#FFC107' : '#FFF',
                    color: isUser ? 'black' : 'inherit',
                    boxShadow: isUser ? '0 2px 8px rgba(255, 193, 7, 0.3)' : '0 1px 3px rgba(0,0,0,0.08)',
                    border: '1px solid',
                    borderColor: isUser ? '#FFC107' : '#e0e0e0',
                    position: 'relative',
                    animation: 'fadeIn 0.3s ease-out',
                    '@keyframes fadeIn': {
                        '0%': {
                            opacity: 0,
                            transform: isUser ? 'translateX(20px)' : 'translateX(-20px)',
                        },
                        '100%': {
                            opacity: 1,
                            transform: 'translateX(0)',
                        },
                    },
                }}
            >
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                    <Typography variant="subtitle2" fontWeight="bold" color={isUser ? 'inherit' : '#FFC107'}>
                        {isUser ? 'You' : 'Omnibot'}
                    </Typography>
                    <Typography
                        variant="caption"
                        sx={{
                            opacity: 0.7,
                            fontSize: '0.7rem',
                        }}
                    >
                        {new Date(message.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                    </Typography>
                </Box>
                <Box sx={{ whiteSpace: 'pre-line' }}>
                    {message.text.split('\n').map((line, index) => {
                        // Create a more stable key using message id and content
                        const lineKey = `${message.id}-line-${index}-${line.substring(0, 10).replace(/\s+/g, '-')}`

                        // Handle markdown-like formatting
                        if (line.startsWith('# ')) {
                            // Main heading
                            return (
                                <Typography
                                    key={lineKey}
                                    variant="h5"
                                    sx={{
                                        fontWeight: 'bold',
                                        mt: 1,
                                        mb: 2,
                                        color: isUser ? 'inherit' : '#000',
                                    }}
                                >
                                    {line.substring(2)}
                                </Typography>
                            )
                        } else if (line.startsWith('## ')) {
                            // Subheading
                            return (
                                <React.Fragment key={lineKey}>
                                    <Divider sx={{ my: 2 }} />
                                    <Typography
                                        variant="h6"
                                        sx={{
                                            fontWeight: 'bold',
                                            mb: 1.5,
                                            color: isUser ? 'inherit' : '#FFC107',
                                        }}
                                    >
                                        {line.substring(3)}
                                    </Typography>
                                </React.Fragment>
                            )
                        } else if (line.startsWith('• ')) {
                            // Bullet point
                            return (
                                <Box
                                    key={lineKey}
                                    sx={{
                                        display: 'flex',
                                        alignItems: 'flex-start',
                                        mb: 1,
                                    }}
                                >
                                    <Box
                                        sx={{
                                            minWidth: '6px',
                                            height: '6px',
                                            borderRadius: '50%',
                                            bgcolor: isUser ? 'black' : '#FFC107',
                                            mt: 1,
                                            mr: 1.5,
                                        }}
                                    />
                                    <Typography
                                        variant="body1"
                                        sx={{
                                            lineHeight: 1.5,
                                            wordBreak: 'break-word',
                                        }}
                                    >
                                        {line.substring(2)}
                                    </Typography>
                                </Box>
                            )
                        } else {
                            // Regular text
                            return (
                                <Typography
                                    key={lineKey}
                                    variant="body1"
                                    sx={{
                                        lineHeight: 1.5,
                                        wordBreak: 'break-word',
                                        mb: line.trim() === '' ? 0.5 : 0,
                                    }}
                                >
                                    {line}
                                </Typography>
                            )
                        }
                    })}
                </Box>
            </Paper>
        </Box>
    )
}

export default ChatMessage
