import DownloadTemplate from '@/app/reports/components/DownloadTemplate'
import { render, screen } from '@testing-library/react'

jest.mock('@/app/reports/services', () => ({
    bulkInsertDownloadService: jest.fn(() => Promise.resolve(new Blob())),
}))
jest.mock('@/utils/helper', () => ({
    downloadBlob: jest.fn(),
}))

describe('DownloadTemplate', () => {
    it('renders download button', () => {
        render(<DownloadTemplate />)
        expect(screen.getByText(/Download Template/i)).toBeInTheDocument()
    })
})
