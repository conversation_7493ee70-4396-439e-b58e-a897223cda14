'use client'

import { Al<PERSON>, Snackbar } from '@mui/material'
import { ReactNode } from 'react'
import { createRoot } from 'react-dom/client'

type ToastVariant = 'success' | 'error' | 'info' | 'warning'

interface ToastOptions {
    duration: number | null
    position?: {
        vertical: 'top' | 'bottom'
        horizontal: 'left' | 'center' | 'right'
    }
    width?: number
}

const defaultOptions: ToastOptions = {
    duration: 5000,
    position: {
        vertical: 'top',
        horizontal: 'right',
    },
    width: 20,
}

class ToastManager {
    private containerRef: HTMLDivElement | null = null
    private root: ReturnType<typeof createRoot> | null = null

    private createContainer() {
        if (typeof document === 'undefined') return // Check for SSR

        if (!this.containerRef) {
            this.containerRef = document.createElement('div')
            this.containerRef.id = 'toast-container'
            document.body.appendChild(this.containerRef)
            this.root = createRoot(this.containerRef)
        }
    }

    private show(message: ReactNode, variant: ToastVariant, options?: ToastOptions) {
        const mergedOptions = { ...defaultOptions, ...options }

        const handleClose = () => {
            if (this.root) {
                this.root.render(<></>)
            }
        }

        this.createContainer()

        if (this.root) {
            this.root.render(
                <Snackbar
                    open={true}
                    autoHideDuration={mergedOptions.duration}
                    onClose={handleClose}
                    anchorOrigin={mergedOptions.position}
                    {...options}
                    sx={{ width: mergedOptions.width + 'rem' }}
                >
                    <Alert onClose={handleClose} severity={variant} variant="filled" sx={{ width: '100%' }}>
                        {message}
                    </Alert>
                </Snackbar>,
            )

            // Auto cleanup after duration
            setTimeout(handleClose, mergedOptions.duration ?? 0)
        }
    }

    success(message: ReactNode, options?: ToastOptions) {
        this.show(message, 'success', options)
    }

    error(message: ReactNode, options?: ToastOptions) {
        this.show(message, 'error', options)
    }

    info(message: ReactNode, options?: ToastOptions) {
        this.show(message, 'info', options)
    }

    warning(message: ReactNode, options?: ToastOptions) {
        this.show(message, 'warning', options)
    }
}

export const snackbar = new ToastManager()
