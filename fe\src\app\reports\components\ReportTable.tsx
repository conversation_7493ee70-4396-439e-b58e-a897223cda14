'use client'

import AddReport from '@/app/reports/components/AddReport'
import { ReportDetails } from '@/app/reports/components/Utils'
import DeleteConfirmDialog from '@/app/reports/reportCards/components/DeleteConfirmDialog'
import TableActions from '@/app/reports/reportCards/components/TableActions'
import { deleteReportsService, getReports } from '@/app/reports/services'
import { setRefreshFlag } from '@/app/reports/slice'
import { Report as BaseReport, Domain } from '@/app/reports/types'
import { defaultMRTOptions } from '@/components/common/table/tableOptions'
import { useAppDispatch, useAppSelector } from '@/store/hooks'
import { checkIsAdmin, normalizeDate } from '@/utils/helper'
import { snackbar } from '@/utils/toast'
import { Delete } from '@mui/icons-material'
import { Box, Button, Tooltip } from '@mui/material'
import {
    MaterialReactTable,
    MRT_ColumnFiltersState,
    MRT_SortingState,
    MRT_TableInstance,
    MRT_GlobalFilterTextField as MRTGlobalFilterTextField,
    useMaterialReactTable,
} from 'material-react-table'
import { useSession } from 'next-auth/react'
import { useCallback, useEffect, useMemo, useState } from 'react'
import { getReportTableColumns } from './ReportTableColumns'

interface Report extends BaseReport {
    domainName: string
}

type RowData = Domain | Report

type ReportTableProps = {
    view: 'all' | 'favorites' | 'saved-later'
}

export default function ReportsTreeTable({ view }: Readonly<ReportTableProps>) {
    const [rowSelection, setRowSelection] = useState<Record<string, boolean>>({})
    const { data: session } = useSession()
    const user = session?.user
    const [data, setData] = useState<Domain[]>([])
    const [rowCount, setRowCount] = useState(0)
    const [isLoading, setIsLoading] = useState(false)
    const [pagination, setPagination] = useState({ pageIndex: 0, pageSize: 10 })
    const [expanded, setExpanded] = useState<Record<string, boolean>>({})
    const { activeFilters, filters: reportFilters } = useAppSelector((store) => store.reports)

    const [globalFilter, setGlobalFilter] = useState('')
    const [columnFilters, setColumnFilters] = useState<MRT_ColumnFiltersState>([])
    const [sorting, setSorting] = useState<MRT_SortingState>([])
    const [openDialog, setOpenDialog] = useState(false)

    const dispatch = useAppDispatch()
    const refreshFlag = useAppSelector((state) => state.reports.refreshFlag)

    const isAdmin = checkIsAdmin(session?.user)

    useEffect(() => {
        if (refreshFlag) {
            dispatch(setRefreshFlag(false)) // Reset the flag after refresh
        }
    }, [refreshFlag, dispatch])

    const renderActionsCell = useCallback((isSubRow: boolean, row: Report) => {
        return isSubRow && <TableActions row={row} />
    }, [])

    const renderDiscCell = useCallback((isSubRow: boolean, value: string) => {
        if (!isSubRow) return null
        return (
            <>
                {isSubRow && (
                    <Tooltip title={value}>
                        <Box
                            sx={{
                                whiteSpace: 'nowrap',
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                                maxWidth: 200,
                            }}
                        >
                            {value}
                        </Box>
                    </Tooltip>
                )}
            </>
        )
    }, [])

    const toggleSubRow = useCallback(
        (reportId: string) => {
            const reportKey = `report_${reportId}`
            setExpanded((prev) => {
                const isAlreadyExpanded = prev[reportKey]
                // Collapse all sub rows
                const newExpanded: Record<string, boolean> = Object.keys(prev).reduce(
                    (acc, key) => {
                        if (key.startsWith('report_')) {
                            acc[key] = false
                        } else {
                            acc[key] = prev[key]
                        }
                        return acc
                    },
                    {} as Record<string, boolean>,
                )
                newExpanded[reportKey] = !isAlreadyExpanded
                return newExpanded
            })
        },
        [setExpanded],
    )

    const columns = useMemo(
        () => getReportTableColumns(toggleSubRow, renderDiscCell, renderActionsCell),
        [toggleSubRow, renderDiscCell, renderActionsCell],
    )

    const fetchData = useCallback(async () => {
        try {
            setIsLoading(true)
            const sort = sorting?.[0]
            const filters: Record<string, string> = {}
            columnFilters.forEach((filter) => {
                filters[filter.id] = filter.value as string
            })
            const { dates } = activeFilters
            const payload = {
                page: pagination.pageIndex,
                size: pagination.pageSize,
                view: view,
                sort: sort?.id || 'name',
                order: sort?.desc ? 'desc' : 'asc',
                search: reportFilters.search,
                filters, // <-- column filters
                domain: activeFilters.domain,
                source_system: activeFilters.sourceSystem,
                category: activeFilters.category,
                start_date: normalizeDate(dates?.[0] || null),
                end_date: normalizeDate(dates?.[1] || null),
            }
            const res = await getReports(payload)
            setData(res.data ? res.data : [])
            setRowCount(res.meta.total)
            const initialExpanded: Record<string, boolean> = {}
            res.data.forEach((domain) => {
                initialExpanded[domain.id] = true
            })
            setExpanded(initialExpanded)
        } catch (error) {
            console.error(error)
            snackbar.error('Failed to fetch reports')
        } finally {
            setIsLoading(false)
        }
    }, [
        pagination.pageIndex,
        pagination.pageSize,
        view,
        sorting,
        columnFilters,
        activeFilters,
        reportFilters.search,
        setIsLoading,
        setData,
        setRowCount,
        setExpanded,
        refreshFlag,
    ])

    useEffect(() => {
        ;(async () => {
            await fetchData()
        })()
    }, [fetchData])

    const handleDeleteReports = async () => {
        const selectedIds = Object.keys(rowSelection)
            .filter((key) => rowSelection[key])
            .map((key) => parseInt(key.replace('report_', '')))
        try {
            await deleteReportsService(user?.user_id ?? '', selectedIds)
            snackbar.success('Reports deleted successfully')
            setRowSelection({}) // Clear selection after delete
            dispatch(setRefreshFlag(true))
        } catch (error) {
            console.error(error)
            snackbar.error('Error deleting reports')
        } finally {
            setOpenDialog(false)
        }
    }

    const table = useMaterialReactTable({
        columns,
        data,
        manualPagination: true,
        manualFiltering: true,
        manualSorting: true,
        rowCount,
        onPaginationChange: setPagination,
        onGlobalFilterChange: setGlobalFilter,
        onColumnFiltersChange: setColumnFilters,
        onSortingChange: setSorting,
        onExpandedChange: (newExpanded) => setExpanded(newExpanded as Record<string, boolean>),
        onRowSelectionChange: setRowSelection,
        state: {
            isLoading,
            pagination,
            globalFilter,
            columnFilters,
            sorting,
            expanded,
            rowSelection,
            showProgressBars: isLoading,
        },
        enableExpanding: true,
        enableExpandAll: false,
        getSubRows: (originalRow) => originalRow.report, //default, can customize
        enableRowSelection: isAdmin
            ? (row) => {
                  const isParentRow = 'report' in row.original
                  return !isParentRow
              }
            : false,
        getRowId: (originalRow) => {
            //return 'id' in originalRow ? originalRow.id : String(originalRow.name) // fallback if needed
            if ('report' in originalRow) {
                // It's a Domain (parent row)
                return `${originalRow.id}`
            } else {
                // It's a Report (sub row)
                return `report_${originalRow.id}`
            }
        },
        enableTopToolbar: isAdmin,
        renderTopToolbar: ({ table }) => {
            const typedTable = table as MRT_TableInstance<RowData>
            return (
                <Box
                    sx={{
                        display: 'flex',
                        gap: '0.5rem',
                        p: '8px',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                    }}
                >
                    {/* Left Side: Global Search */}
                    <Box sx={{ display: 'flex', gap: '0.5rem', alignItems: 'center' }}>
                        <MRTGlobalFilterTextField size={'small'} style={{ visibility: 'hidden' }} table={typedTable} />
                    </Box>

                    {view === 'all' && (
                        <Box sx={{ display: 'flex', gap: '0.5rem', alignItems: 'center' }}>
                            <>
                                <AddReport />
                                <Button
                                    color="error"
                                    disabled={Object.keys(rowSelection).length === 0}
                                    size={'small'}
                                    variant="contained"
                                    startIcon={<Delete />}
                                    onClick={() => setOpenDialog(true)}
                                >
                                    Delete ({Object.keys(rowSelection).length})
                                </Button>
                            </>
                        </Box>
                    )}
                </Box>
            )
        },
        ...defaultMRTOptions,
        initialState: {
            ...defaultMRTOptions.initialState,
            columnOrder: ['name', 'category', 'description', 'published_on', 'actions'],
        },
        enableHiding: false,
        enableFullScreenToggle: false,
        enableDensityToggle: false,
        enableGlobalFilter: true,
        enableFilters: false,
        defaultColumn: {
            minSize: 20,
            maxSize: 50,
            size: 100,
        },

        muiTableBodyRowProps: ({ row }) => ({
            sx: {
                backgroundColor: 'report' in row.original ? 'rgba(0, 0, 0, 0.04)' : 'inherit', // light gray bg for parent
                fontWeight: 'report' in row.original ? 'bold' : 'normal', // bold text for parent
            },
        }),
        muiExpandButtonProps: ({ row }) => ({
            style: {
                //display: 'report' in row.original ? 'none' : 'inline-flex', // hide for sub-rows
                display: row.depth === 1 ? 'none' : 'inline-flex',
            },
        }),
        displayColumnDefOptions: {
            'mrt-row-expand': {
                size: 10,
                muiTableHeadCellProps: {
                    sx: {
                        width: '10px',
                        minWidth: '10px',
                        maxWidth: '10px',
                        marginRight: '20px',
                        textAlign: 'center',
                        visibility: 'hidden',
                    },
                },
            },
        },
        muiSelectAllCheckboxProps: {
            color: 'secondary',
        },
        muiSelectCheckboxProps: ({ row }) => ({
            color: 'secondary',
            style: {
                display: 'report' in row.original ? 'none' : 'inline-flex',
            },
        }),
        renderDetailPanel: ({ row }) => {
            const isSubRow = !('report' in row.original)
            const report = row.original as Report
            if (!isSubRow) return null
            return (
                <Box sx={{ margin: 2 }}>
                    <ReportDetails report={report} />
                </Box>
            )
        },
    })

    return (
        <>
            <MaterialReactTable table={table} />
            <DeleteConfirmDialog
                open={openDialog}
                onClose={() => setOpenDialog(false)}
                onConfirm={handleDeleteReports}
                reportCount={Object.keys(rowSelection).length}
            />
        </>
    )
}
