import { SingleDateTimePicker } from '@/components/common/utility/DatePicker'
import GenericModal from '@/components/common/utility/GenericModal'
import { ALERTS_TYPE_OPTIONS } from '@/utils/constants'
import { formatDateTime } from '@/utils/helper'
import { snackbar } from '@/utils/toast'
import AnnouncementIcon from '@mui/icons-material/Announcement'
import ErrorIcon from '@mui/icons-material/Error'
import EventIcon from '@mui/icons-material/Event'
import WarningAmberIcon from '@mui/icons-material/WarningAmber'
import {
    Box,
    Button,
    FormControl,
    FormControlLabel,
    Grid2 as Grid,
    MenuItem,
    Switch,
    TextField,
    Typography,
    useMediaQuery,
    useTheme,
} from '@mui/material'
import { Form, Formik } from 'formik'
import { capitalize } from 'lodash'
import { useSession } from 'next-auth/react'
import { ReactNode, useState } from 'react'
import * as Yup from 'yup'
import { createAlertsService, editAlertsService } from '../services'
import { IAlert } from '../types'

interface MessageModalProps {
    open: boolean
    onClose: () => void
    rowData?: IAlert
    onSaveSuccess: () => void
}

const MESSAGE_TYPE_TO_SEVERITY: Record<string, 'success' | 'info' | 'error' | 'warning'> = {
    event: 'info',
    news: 'success',
    alert: 'warning',
    error: 'error',
}

export const TYPE_ICONS: Record<string, ReactNode> = {
    event: <EventIcon fontSize="small" sx={{ color: '#1976d2' }} />, // Blue for events
    news: <AnnouncementIcon fontSize="small" sx={{ color: '#388e3c' }} />, // Green for news
    alert: <WarningAmberIcon fontSize="small" sx={{ color: '#f57c00' }} />, // Orange for alerts
    error: <ErrorIcon fontSize="small" sx={{ color: '#d32f2f' }} />, // Red for errors
    info: <AnnouncementIcon fontSize="small" sx={{ color: '#7b1fa2' }} />, // Purple for info
}

const AlertSchema = Yup.object().shape({
    type: Yup.string().required('Type is required.'),
    title: Yup.string().required('Title is required.'),
    description: Yup.string().required('Description is required.'),
    start_date_time: Yup.date().nullable().required('Start Date is required.'),
    end_date_time: Yup.date()
        .nullable()
        .required('End Date is required.')
        .test('end-after-start', 'End Date cannot be before Start Date.', function (value) {
            const { start_date_time } = this.parent
            if (value && start_date_time) {
                return new Date(value) >= new Date(start_date_time)
            }
            return true
        }),
    is_visible: Yup.boolean(),
})

const AlertsModal: React.FC<MessageModalProps> = ({ open, onClose, rowData, onSaveSuccess }) => {
    const theme = useTheme()
    const isMobile = useMediaQuery(theme.breakpoints.down('sm'))
    const { data: session } = useSession()
    const userEmail = session?.user?.email ?? ''

    const initialValues: IAlert = rowData ? mapRowDataToAlert(rowData) : getDefaultAlertData()

    const [loading, setLoading] = useState(false)

    const handleSubmit = async (values: IAlert, { setSubmitting, resetForm }: any) => {
        setLoading(true)
        try {
            if (rowData) {
                await editAlertsService({ ...values, created_by: userEmail })
                snackbar.success('Alert updated successfully!')
            } else {
                await createAlertsService({ ...values, created_by: userEmail })
                snackbar.success('Alert created successfully!')
            }
            onSaveSuccess()
            resetForm()
            onClose()
        } catch (error: any) {
            if (error.message === 'no changes detected') {
                snackbar.error('No changes detected. Please modify the alert before saving.')
            } else if (error.message) {
                snackbar.error(error.message)
            } else {
                snackbar.error('Failed to save alert. Please try again.')
            }
        } finally {
            setLoading(false)
            setSubmitting(false)
        }
    }

    const handlePreview = (values: IAlert) => {
        try {
            AlertSchema.validateSync(values, { abortEarly: false })
        } catch (validationError: any) {
            snackbar.error('Please fill in all required fields before previewing.')
            console.log('Error validating form:', validationError)
            return
        }

        const alertType = MESSAGE_TYPE_TO_SEVERITY[values.type] || 'info'
        snackbar[alertType](
            <Box sx={{ width: '100%' }}>
                <Typography variant="h6" fontWeight="bold">
                    {values.title}
                </Typography>
                <Typography variant="body2" sx={{ mb: 1 }}>
                    {values.description}
                </Typography>
                <Typography variant="body2">
                    <strong>From:</strong> {formatDateTime(values.start_date_time?.toISOString() ?? '') || 'N/A'}
                </Typography>
                <Typography variant="body2" sx={{ mb: 1 }}>
                    <strong>To:</strong> {formatDateTime(values.end_date_time?.toISOString() ?? '') || 'N/A'}
                </Typography>
                <Typography variant="body2">
                    <strong>Visibility:</strong> {values.is_visible ? 'Visible' : 'Hidden'}
                </Typography>
            </Box>,
            { duration: 20000, width: 40 },
        )
    }

    return (
        <GenericModal
            open={open}
            onClose={onClose}
            title="Create News/Alert"
            width={isMobile ? false : 'sm'}
            actions={null}
        >
            <Formik
                initialValues={initialValues}
                validationSchema={AlertSchema}
                enableReinitialize
                onSubmit={handleSubmit}
            >
                {({
                    values,
                    errors,
                    touched,
                    handleChange: formikHandleChange,
                    setFieldValue,
                    isSubmitting,
                    resetForm,
                    dirty,
                }) => (
                    <Form>
                        <ModalContent
                            values={values}
                            errors={errors}
                            touched={touched}
                            handleChange={formikHandleChange}
                            setFieldValue={setFieldValue}
                        />
                        <Box display="flex" gap={1} justifyContent="flex-end" mt={2}>
                            <Button
                                variant="outlined"
                                size={'small'}
                                color="info"
                                onClick={() => handlePreview(values)}
                                sx={{ bgcolor: '#f8eaaa', color: 'text.primary', '&:hover': { bgcolor: '#f0e090' } }}
                            >
                                Preview
                            </Button>
                            <Button
                                variant="outlined"
                                size={'small'}
                                color="error"
                                onClick={() => {
                                    resetForm()
                                    onClose()
                                }}
                                disabled={isSubmitting || loading}
                            >
                                Cancel
                            </Button>
                            <Button
                                variant="contained"
                                color="success"
                                size={'small'}
                                type="submit"
                                disabled={isSubmitting || loading || !dirty}
                            >
                                {loading ? 'Saving...' : 'Save'}
                            </Button>
                        </Box>
                    </Form>
                )}
            </Formik>
        </GenericModal>
    )
}

export default AlertsModal

const ModalContent = ({
    values,
    errors,
    touched,
    handleChange,
    setFieldValue,
}: {
    values: IAlert
    errors: any
    touched: any
    handleChange: any
    setFieldValue: any
}) => (
    <Box sx={{ width: '100%', display: 'flex', flexDirection: 'column', gap: 1 }}>
        <FormControl fullWidth size="small" sx={{ my: 1 }}>
            <TextField
                select
                variant={'outlined'}
                fullWidth
                size="small"
                name="type"
                label="Type *"
                value={values.type}
                onChange={handleChange}
                color="secondary"
                error={!!(touched.type && errors.type)}
                helperText={touched.type && errors.type}
            >
                {ALERTS_TYPE_OPTIONS.map((option) => (
                    <MenuItem key={option} value={option}>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            {TYPE_ICONS[option]}
                            {capitalize(option)}
                        </Box>
                    </MenuItem>
                ))}
            </TextField>
        </FormControl>
        <TextFieldWithError
            label="Title *"
            name="title"
            value={values.title}
            onChange={handleChange}
            error={touched.title && errors.title}
            size="small"
        />
        <TextFieldWithError
            label="Description *"
            name="description"
            value={values.description}
            onChange={handleChange}
            error={touched.description && errors.description}
            multiline
            rows={3}
            size="small"
        />
        <Grid container spacing={2}>
            <Grid size={{ xs: 12, md: 6 }}>
                <SingleDateTimePicker
                    label="Start Date *"
                    value={values.start_date_time}
                    onChange={(value) => setFieldValue('start_date_time', value)}
                    minDate={new Date()}
                    error={touched.start_date_time && errors.start_date_time}
                />
            </Grid>
            <Grid size={{ xs: 12, md: 6 }}>
                <SingleDateTimePicker
                    label="End Date *"
                    value={values.end_date_time}
                    onChange={(value) => setFieldValue('end_date_time', value)}
                    minDate={values.start_date_time ?? new Date()}
                    error={touched.end_date_time && errors.end_date_time}
                />
            </Grid>
        </Grid>
        <VisibilitySwitch visibility={values.is_visible} onChange={(checked) => setFieldValue('is_visible', checked)} />
    </Box>
)

const TextFieldWithError = ({
    label,
    name,
    value,
    onChange,
    error,
    ...props
}: {
    label: ReactNode
    name: string
    value: string
    onChange: (e: React.ChangeEvent<HTMLInputElement>) => void
    error?: any
} & React.ComponentProps<typeof TextField>) => (
    <Box>
        <TextField
            fullWidth
            name={name}
            label={label}
            value={value}
            onChange={onChange}
            variant="outlined"
            color="secondary"
            error={!!error}
            helperText={error ?? ' '}
            {...props}
        />
    </Box>
)

const VisibilitySwitch = ({ visibility, onChange }: { visibility: boolean; onChange: (checked: boolean) => void }) => (
    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 1 }}>
        <Typography variant="inherit">Visibility</Typography>
        <FormControlLabel
            control={
                <Switch
                    size="small"
                    color="secondary"
                    checked={visibility}
                    onChange={(e) => onChange(e.target.checked)}
                />
            }
            label={visibility ? 'ON' : 'OFF'}
            labelPlacement="start"
        />
    </Box>
)

function getDefaultAlertData(): IAlert {
    return {
        type: 'event',
        title: '',
        description: '',
        start_date_time: null,
        end_date_time: null,
        is_visible: false,
        status: 'scheduled',
        created_by: '',
    }
}

function mapRowDataToAlert(rowData: IAlert): IAlert {
    return {
        ...rowData,
        start_date_time: rowData.start_date_time ? new Date(rowData.start_date_time) : null,
        end_date_time: rowData.end_date_time ? new Date(rowData.end_date_time) : null,
    }
}
