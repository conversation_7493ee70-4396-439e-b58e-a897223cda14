import { ArrowForwardOutlined as <PERSON><PERSON>orwardIcon } from '@mui/icons-material'
import { Box, Paper, Typography } from '@mui/material'
import Image from 'next/image'
import Link from 'next/link'

// Icon mapping using image paths relative to the public folder
const iconMapping = {
    chart: '/images/landing/chart.svg',
    chat: '/images/landing/message.svg',
    list: '/images/landing/clipboard.svg',
    presentation: '/images/landing/presentation.svg',
}

interface FeatureCardProps {
    title: string
    icon: keyof typeof iconMapping
    link: string
}

// Icon component for better clarity
const IconWrapper = ({ icon }: { icon: keyof typeof iconMapping }) => {
    const iconPath = iconMapping[icon]

    return (
        <Box
            sx={{
                width: 100,
                height: 100,
                borderRadius: 2,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
            }}
        >
            <Image src={iconPath} alt={icon} width={70} height={10} style={{ height: 'auto' }} />
        </Box>
    )
}

// Title Box component
const TitleBox = ({ title }: { title: string }) => (
    <Box
        sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            gap: 2,
            backgroundColor: 'primary.light',
            borderRadius: '20px',
            pl: 2,
            pr: 2,
        }}
    >
        <Typography variant="subtitle1" sx={{ textAlign: 'center' }}>
            {title}
        </Typography>
        <ArrowForwardIcon sx={{ color: 'secondary' }} />
    </Box>
)

export default function FeatureCard({ title, icon, link }: Readonly<FeatureCardProps>) {
    return (
        <Paper
            elevation={4}
            sx={{
                p: 2,
                borderRadius: '15px',
                height: '100%',
                cursor: 'pointer',
                transition: 'transform 0.3s',
                '&:hover': {
                    bgcolor: 'action.hover',
                    transform: 'scale(1.05)',
                },
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'center',
            }}
        >
            <>
                {title === 'Self-Service' && (
                    <a target="_blank" href={link} rel="noopener noreferrer">
                        <Box
                            sx={{
                                display: 'flex',
                                flexDirection: 'column',
                                alignItems: 'center',
                                gap: 2,
                            }}
                        >
                            <IconWrapper icon={icon} />
                            <TitleBox title={title} />
                        </Box>
                    </a>
                )}
                {title !== 'Self-Service' && (
                    <Link href={link}>
                        <Box
                            sx={{
                                display: 'flex',
                                flexDirection: 'column',
                                alignItems: 'center',
                                gap: 2,
                            }}
                        >
                            <IconWrapper icon={icon} />
                            <TitleBox title={title} />
                        </Box>
                    </Link>
                )}
            </>
        </Paper>
    )
}
