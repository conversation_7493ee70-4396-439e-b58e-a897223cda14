package catalogReportService

import (
	// "dng-module/internal/app/catalogReportService"
	"dng-module/internal/model"
	"dng-module/testing/utilsTest"
	"errors"
	"regexp"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
)

func TestGetReports(t *testing.T) {
	db, mock := utilsTest.SetupMockDB(t)
	defer utilsTest.CloseMockDB(db)

	userID := 1
	params := model.GetReportsQueryParams{
		Page:         0,
		Size:         5,
		View:         "all",
		Search:       "",
		Sort:         "name",
		Order:        "asc",
		Domain:       []int{},
		Category:     []string{},
		SourceSystem: []string{},
	}

	// Test cases
	t.Run("Error getting domains for user domain access", func(t *testing.T) {
		mock.ExpectQuery(regexp.QuoteMeta(`SELECT uda.domain_id FROM user_domain_access AS uda JOIN roles r ON r.id = uda.role_id WHERE uda.user_id = $1 AND r.can_view = $2`)).
			WithArgs(userID, true).
			WillReturnError(errors.New("failed to get domains"))

		_, err := GetReports(db, params, userID, "")
		assert.Error(t, err)
		assert.EqualError(t, err, "failed to get domains")
	})

	t.Run("Error fetching user report preferences", func(t *testing.T) {
		mock.ExpectQuery(regexp.QuoteMeta(`SELECT uda.domain_id FROM user_domain_access AS uda JOIN roles r ON r.id = uda.role_id WHERE uda.user_id = $1 AND r.can_view = $2`)).
			WithArgs(userID, true).
			WillReturnRows(sqlmock.NewRows([]string{"domain_id"}).AddRow(1))

		mock.ExpectQuery(regexp.QuoteMeta(`SELECT * FROM "user_report_preference" WHERE user_id = $1`)).
			WithArgs(userID).
			WillReturnError(errors.New("preferences fetch error"))

		_, err := GetReports(db, params, userID, "")
		assert.Error(t, err)
		assert.EqualError(t, err, "preferences fetch error")
	})

	t.Run("Error fetching individual report", func(t *testing.T) {
		mock.ExpectQuery(regexp.QuoteMeta(`SELECT uda.domain_id FROM user_domain_access AS uda JOIN roles r ON r.id = uda.role_id WHERE uda.user_id = $1 AND r.can_view = $2`)).
			WithArgs(userID, true).
			WillReturnRows(sqlmock.NewRows([]string{"domain_id"}).AddRow(1))

		mock.ExpectQuery(regexp.QuoteMeta(`SELECT * FROM "user_report_preference" WHERE user_id = $1`)).
			WithArgs(userID).
			WillReturnRows(sqlmock.NewRows([]string{"user_id", "report_id", "is_favorite", "is_saved"}).
				AddRow(userID, 101, true, false))

		mock.ExpectQuery(regexp.QuoteMeta(`SELECT * FROM "reports" WHERE id = $1 ORDER BY "reports"."id" LIMIT $2`)).
			WithArgs(101, 1).
			WillReturnError(errors.New("report fetch error"))

		_, err := GetReports(db, params, userID, "")
		assert.Error(t, err)
		assert.EqualError(t, err, "report fetch error")
	})
	t.Run("Test with domain filter in params", func(t *testing.T) {
		userID := 1
		params := model.GetReportsQueryParams{
			Domain: []int{1, 2},
		}

		// 1. Expect user domain access query
		mock.ExpectQuery(regexp.QuoteMeta(`
			SELECT uda.domain_id 
			FROM user_domain_access AS uda 
			JOIN roles r ON r.id = uda.role_id 
			WHERE uda.user_id = $1 AND r.can_view = $2`)).
			WithArgs(userID, true).
			WillReturnRows(sqlmock.NewRows([]string{"domain_id"}).AddRow(1).AddRow(2))

		// 2. Expect user report preferences query
		mock.ExpectQuery(regexp.QuoteMeta(`
			SELECT * FROM "user_report_preference" WHERE user_id = $1`)).
			WithArgs(userID).
			WillReturnRows(sqlmock.NewRows([]string{"user_id", "report_id", "is_favorite", "is_saved"})) // no preferences

		// 3. Expect domains query
		mock.ExpectQuery(regexp.QuoteMeta(`
			SELECT * FROM "domains" WHERE id IN ($1,$2) AND id IN ($3,$4)`)).
			WithArgs(1, 2, 1, 2).
			WillReturnRows(sqlmock.NewRows([]string{"id", "name", "description"}).
				AddRow(1, "Domain 1", "Desc 1").
				AddRow(2, "Domain 2", "Desc 2"))

		// 4. Expect reports query (combined IN clause)
		mock.ExpectQuery(regexp.QuoteMeta(`
			SELECT * FROM "reports" WHERE "reports"."domain_id" IN ($1,$2)`)).
			WithArgs(1, 2).
			WillReturnRows(sqlmock.NewRows([]string{"id", "name", "domain_id"}).
				AddRow(101, "Report A", 1).
				AddRow(102, "Report B", 2))

		// Call the actual function
		result, err := GetReports(db, params, userID, "")
		assert.NoError(t, err)
		assert.NotNil(t, result)
	})

	t.Run("Test sorting and pagination scenarios", func(t *testing.T) {
		testCases := []struct {
			name     string
			sort     string
			order    string
			page     int
			size     int
			expSort  string
			expOrder string
			expPage  int
			expSize  int
		}{
			{"default sorting", "", "", 0, 0, "created_at", "asc", 0, 5},
			{"name desc", "name", "desc", -1, -1, "name", "desc", 0, 5},
			{"category asc", "category", "asc", 2, 10, "category", "asc", 2, 10},
			{"source_system desc", "source_system", "desc", 0, 0, "source_system", "desc", 0, 5},
		}

		for _, tc := range testCases {
			t.Run(tc.name, func(t *testing.T) {
				params := model.GetReportsQueryParams{
					Sort:  tc.sort,
					Order: tc.order,
					Page:  tc.page,
					Size:  tc.size,
				}

				// Setup mock expectations
				mock.ExpectQuery(regexp.QuoteMeta(`SELECT uda.domain_id FROM user_domain_access AS uda JOIN roles r ON r.id = uda.role_id WHERE uda.user_id = $1 AND r.can_view = $2`)).
					WithArgs(userID, true).
					WillReturnRows(sqlmock.NewRows([]string{"domain_id"}).AddRow(1))

				mock.ExpectQuery(regexp.QuoteMeta(`SELECT * FROM "user_report_preference" WHERE user_id = $1`)).
					WithArgs(userID).
					WillReturnRows(sqlmock.NewRows([]string{"user_id", "report_id", "is_favorite", "is_saved"}))

				mock.ExpectQuery(regexp.QuoteMeta(`SELECT * FROM "domains" WHERE id IN ($1)`)).
					WithArgs(1).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name", "description"}).AddRow(1, "Domain", "Desc"))

				// Add reports with different values to test sorting
				mock.ExpectQuery(regexp.QuoteMeta(`SELECT * FROM "reports" WHERE "reports"."domain_id" = $1`)).
					WithArgs(1).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "category", "source_system", "created_at", "domain_id",
					}).
						AddRow(1, "Report B", "Cat B", "Sys B", time.Now().Add(-time.Hour), 1).
						AddRow(2, "Report A", "Cat A", "Sys A", time.Now(), 1))

				result, err := GetReports(db, params, userID, "")
				assert.NoError(t, err)
				assert.Equal(t, tc.expPage, result.Meta.Page)
				assert.Equal(t, tc.expSize, result.Meta.Limit)
			})
		}
	})
	t.Run("Error fetching domains", func(t *testing.T) {
		mock.ExpectQuery(regexp.QuoteMeta(`SELECT uda.domain_id FROM user_domain_access AS uda JOIN roles r ON r.id = uda.role_id WHERE uda.user_id = $1 AND r.can_view = $2`)).
			WithArgs(userID, true).
			WillReturnRows(sqlmock.NewRows([]string{"domain_id"}).AddRow(1))

		mock.ExpectQuery(regexp.QuoteMeta(`SELECT * FROM "user_report_preference" WHERE user_id = $1`)).
			WithArgs(userID).
			WillReturnRows(sqlmock.NewRows([]string{"user_id", "report_id", "is_favorite", "is_saved"}).
				AddRow(userID, 101, true, false))

		mock.ExpectQuery(regexp.QuoteMeta(`SELECT * FROM "reports" WHERE id = $1 ORDER BY "reports"."id" LIMIT $2`)).
			WithArgs(101, 1).
			WillReturnRows(sqlmock.NewRows([]string{
				"id", "name", "description", "category", "domain_id",
				"source_system", "group_id", "created_by", "created_at", "updated_at", "published_on",
			}).AddRow(101, "Report A", "Desc", "Cat", 1, "System", "Group", userID, time.Now(), time.Now(), time.Now()))

		mock.ExpectQuery(regexp.QuoteMeta(`SELECT * FROM "domains" WHERE id IN ($1)`)).
			WithArgs(1).
			WillReturnError(errors.New("domain fetch error"))

		_, err := GetReports(db, params, userID, "")
		assert.Error(t, err)
		assert.EqualError(t, err, "domain fetch error")
	})

	t.Run("Successful report retrieval", func(t *testing.T) {
		mock.ExpectQuery(regexp.QuoteMeta(`SELECT uda.domain_id FROM user_domain_access AS uda JOIN roles r ON r.id = uda.role_id WHERE uda.user_id = $1 AND r.can_view = $2`)).
			WithArgs(userID, true).
			WillReturnRows(sqlmock.NewRows([]string{"domain_id"}).AddRow(1))

		mock.ExpectQuery(regexp.QuoteMeta(`SELECT * FROM "user_report_preference" WHERE user_id = $1`)).
			WithArgs(userID).
			WillReturnRows(sqlmock.NewRows([]string{"user_id", "report_id", "is_favorite", "is_saved"}).
				AddRow(userID, 101, true, false))

		mock.ExpectQuery(regexp.QuoteMeta(`SELECT * FROM "reports" WHERE id = $1 ORDER BY "reports"."id" LIMIT $2`)).
			WithArgs(101, 1).
			WillReturnRows(sqlmock.NewRows([]string{
				"id", "name", "description", "category", "domain_id",
				"source_system", "group_id", "created_by", "created_at", "updated_at", "published_on",
			}).AddRow(101, "Report A", "Desc", "Cat", 1, "System", "Group", userID, time.Now(), time.Now(), time.Now()))

		mock.ExpectQuery(regexp.QuoteMeta(`SELECT * FROM "domains" WHERE id IN ($1)`)).
			WithArgs(1).
			WillReturnRows(sqlmock.NewRows([]string{"id", "name", "description"}).AddRow(1, "Domain A", "Description"))

		mock.ExpectQuery(regexp.QuoteMeta(`SELECT * FROM "reports" WHERE "reports"."domain_id" = $1`)).
			WithArgs(1).
			WillReturnRows(sqlmock.NewRows([]string{
				"id", "name", "description", "category", "domain_id",
				"source_system", "group_id", "created_by", "created_at", "updated_at", "published_on",
			}).AddRow(102, "Report B", "Desc B", "Cat B", 1, "System B", "Group B", userID, time.Now(), time.Now(), time.Now()))

		result, err := GetReports(db, params, userID, "")
		assert.NoError(t, err)
		assert.NotNil(t, result)
	})

	t.Run("Test view filter scenarios", func(t *testing.T) {
		testCases := []struct {
			name       string
			viewFilter string
			isFavorite bool
			isSaved    bool
			shouldShow bool
		}{
			{"all view - favorite", "all", true, false, true},
			{"all view - saved", "all", false, true, true},
			{"favorites view - match", "favorites", true, false, true},
			{"favorites view - no match", "favorites", false, true, false},
			{"saved-later view - match", "saved-later", false, true, true},
			{"saved-later view - no match", "saved-later", true, false, false},
			{"empty view filter", "", true, false, true},
		}

		for _, tc := range testCases {
			t.Run(tc.name, func(t *testing.T) {
				params := model.GetReportsQueryParams{
					View: tc.viewFilter,
				}

				mock.ExpectQuery(regexp.QuoteMeta(`SELECT uda.domain_id FROM user_domain_access AS uda JOIN roles r ON r.id = uda.role_id WHERE uda.user_id = $1 AND r.can_view = $2`)).
					WithArgs(userID, true).
					WillReturnRows(sqlmock.NewRows([]string{"domain_id"}).AddRow(1))

				// Setup preferences
				mock.ExpectQuery(regexp.QuoteMeta(`SELECT * FROM "user_report_preference" WHERE user_id = $1`)).
					WithArgs(userID).
					WillReturnRows(sqlmock.NewRows([]string{"user_id", "report_id", "is_favorite", "is_saved"}).
						AddRow(userID, 101, tc.isFavorite, tc.isSaved))

				mock.ExpectQuery(regexp.QuoteMeta(`SELECT * FROM "reports" WHERE id = $1 ORDER BY "reports"."id" LIMIT $2`)).
					WithArgs(101, 1).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name", "domain_id"}).
						AddRow(101, "Test Report", 1))

				mock.ExpectQuery(regexp.QuoteMeta(`SELECT * FROM "domains" WHERE id IN ($1)`)).
					WithArgs(1).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name", "description"}).AddRow(1, "Domain", "Desc"))

				mock.ExpectQuery(regexp.QuoteMeta(`SELECT * FROM "reports" WHERE "reports"."domain_id" = $1`)).
					WithArgs(1).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name", "domain_id"}).
						AddRow(101, "Test Report", 1))

				result, err := GetReports(db, params, userID, "")
				assert.NoError(t, err)

				if tc.shouldShow {
					assert.Greater(t, len(result.Domain), 0)
					if len(result.Domain) > 0 {
						assert.Greater(t, len(result.Domain[0].Report), 0)
					}
				} else {
					if len(result.Domain) > 0 {
						assert.Equal(t, 0, len(result.Domain[0].Report))
					}
				}
			})
		}
	})

	t.Run("Test search and date filtering", func(t *testing.T) {
		now := time.Now()
		yesterday := now.AddDate(0, 0, -1)
		tomorrow := now.AddDate(0, 0, 1)

		testCases := []struct {
			name        string
			search      string
			startDate   string
			endDate     string
			reportName  string
			reportDesc  string
			reportCat   string
			publishedOn *time.Time
			shouldShow  bool
		}{
			{"search matches name", "test", "", "", "Test Report", "Desc", "Cat", &now, true},
			{"search matches description", "desc", "", "", "Report", "Test Desc", "Cat", &now, true},
			{"search matches category", "cat", "", "", "Report", "Desc", "Test Cat", &now, true},
			{"search no match", "nomatch", "", "", "Report", "Desc", "Cat", &now, false},
			{"date within range", "", yesterday.Format("2006-01-02"), tomorrow.Format("2006-01-02"), "Report", "Desc", "Cat", &now, true},
			{"date before start", "", now.Format("2006-01-02"), "", "Report", "Desc", "Cat", &yesterday, false},
			{"date after end", "", "", now.Format("2006-01-02"), "Report", "Desc", "Cat", &tomorrow, false},
			{"nil publishedOn with date filter", "", yesterday.Format("2006-01-02"), "", "Report", "Desc", "Cat", nil, false},
		}

		for _, tc := range testCases {
			t.Run(tc.name, func(t *testing.T) {
				params := model.GetReportsQueryParams{
					Search:    tc.search,
					StartDate: tc.startDate,
					EndDate:   tc.endDate,
				}

				mock.ExpectQuery(regexp.QuoteMeta(`SELECT uda.domain_id FROM user_domain_access AS uda JOIN roles r ON r.id = uda.role_id WHERE uda.user_id = $1 AND r.can_view = $2`)).
					WithArgs(userID, true).
					WillReturnRows(sqlmock.NewRows([]string{"domain_id"}).AddRow(1))

				mock.ExpectQuery(regexp.QuoteMeta(`SELECT * FROM "user_report_preference" WHERE user_id = $1`)).
					WithArgs(userID).
					WillReturnRows(sqlmock.NewRows([]string{"user_id", "report_id", "is_favorite", "is_saved"}))

				mock.ExpectQuery(regexp.QuoteMeta(`SELECT * FROM "domains" WHERE id IN ($1)`)).
					WithArgs(1).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name", "description"}).AddRow(1, "Domain", "Desc"))

				mock.ExpectQuery(regexp.QuoteMeta(`SELECT * FROM "reports" WHERE "reports"."domain_id" = $1`)).
					WithArgs(1).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "description", "category", "published_on", "domain_id",
					}).AddRow(1, tc.reportName, tc.reportDesc, tc.reportCat, tc.publishedOn, 1))

				result, err := GetReports(db, params, userID, "")
				assert.NoError(t, err)

				if tc.shouldShow {
					assert.Greater(t, len(result.Domain), 0)
					if len(result.Domain) > 0 {
						assert.Greater(t, len(result.Domain[0].Report), 0)
					}
				} else {
					if len(result.Domain) > 0 {
						assert.Equal(t, 0, len(result.Domain[0].Report))
					}
				}
			})
		}
	})

	t.Run("Test category and source system filtering", func(t *testing.T) {
		testCases := []struct {
			name          string
			categories    []string
			sourceSystems []string
			reportCat     string
			reportSys     string
			shouldShow    bool
		}{
			{"category match", []string{"Cat1", "Cat2"}, nil, "Cat1", "Sys1", true},
			{"category no match", []string{"Cat1"}, nil, "Cat2", "Sys1", false},
			{"source system match", nil, []string{"Sys1", "Sys2"}, "Cat1", "Sys1", true},
			{"source system no match", nil, []string{"Sys1"}, "Cat1", "Sys2", false},
			{"both match", []string{"Cat1"}, []string{"Sys1"}, "Cat1", "Sys1", true},
			{"category match but system no match", []string{"Cat1"}, []string{"Sys1"}, "Cat1", "Sys2", false},
		}

		for _, tc := range testCases {
			t.Run(tc.name, func(t *testing.T) {
				params := model.GetReportsQueryParams{
					Category:     tc.categories,
					SourceSystem: tc.sourceSystems,
				}

				mock.ExpectQuery(regexp.QuoteMeta(`SELECT uda.domain_id FROM user_domain_access AS uda JOIN roles r ON r.id = uda.role_id WHERE uda.user_id = $1 AND r.can_view = $2`)).
					WithArgs(userID, true).
					WillReturnRows(sqlmock.NewRows([]string{"domain_id"}).AddRow(1))

				mock.ExpectQuery(regexp.QuoteMeta(`SELECT * FROM "user_report_preference" WHERE user_id = $1`)).
					WithArgs(userID).
					WillReturnRows(sqlmock.NewRows([]string{"user_id", "report_id", "is_favorite", "is_saved"}))

				mock.ExpectQuery(regexp.QuoteMeta(`SELECT * FROM "domains" WHERE id IN ($1)`)).
					WithArgs(1).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name", "description"}).AddRow(1, "Domain", "Desc"))

				mock.ExpectQuery(regexp.QuoteMeta(`SELECT * FROM "reports" WHERE "reports"."domain_id" = $1`)).
					WithArgs(1).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "category", "source_system", "domain_id",
					}).AddRow(1, "Report", tc.reportCat, tc.reportSys, 1))

				result, err := GetReports(db, params, userID, "")
				assert.NoError(t, err)

				if tc.shouldShow {
					assert.Greater(t, len(result.Domain), 0)
					if len(result.Domain) > 0 {
						assert.Greater(t, len(result.Domain[0].Report), 0)
					}
				} else {
					if len(result.Domain) > 0 {
						assert.Equal(t, 0, len(result.Domain[0].Report))
					}
				}
			})
		}
	})
}

func TestGetReports_EmptyDomains(t *testing.T) {
	db, mock := utilsTest.SetupMockDB(t)
	defer utilsTest.CloseMockDB(db)

	userID := 2
	params := model.GetReportsQueryParams{}

	// User domain access returns no rows
	mock.ExpectQuery(regexp.QuoteMeta(
		`SELECT uda.domain_id FROM user_domain_access AS uda JOIN roles r ON r.id = uda.role_id WHERE uda.user_id = $1 AND r.can_view = $2`,
	)).WithArgs(userID, true).WillReturnRows(sqlmock.NewRows([]string{"domain_id"}))

	resp, err := GetReports(db, params, userID, "")
	assert.NoError(t, err)
	assert.Equal(t, 0, len(resp.Domain))
	assert.Equal(t, 0, resp.Meta.Total)
	assert.Equal(t, 0, resp.Meta.TotalPages)
	assert.NoError(t, mock.ExpectationsWereMet())
}
