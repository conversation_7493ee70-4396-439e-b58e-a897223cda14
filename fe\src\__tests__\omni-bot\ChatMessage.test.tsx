import '@testing-library/jest-dom'
import { render, screen } from '@testing-library/react'
import ChatMessage from '../../app/omni-bot/components/ChatMessage'
import { Message } from '../../app/omni-bot/types'

describe('ChatMessage', () => {
    it('renders user message', () => {
        const msg: Message = {
            id: '1',
            sender: 'user',
            text: 'Hello, bot!',
            timestamp: new Date(),
        }
        render(<ChatMessage message={msg} />)
        expect(screen.getByText('You')).toBeInTheDocument()
        expect(screen.getByText('Hello, bot!')).toBeInTheDocument()
    })

    it('renders bot message', () => {
        const msg: Message = {
            id: '2',
            sender: 'bot',
            text: 'Hi, user!',
            timestamp: new Date(),
        }
        render(<ChatMessage message={msg} />)
        expect(screen.getByText('Omnibot')).toBeInTheDocument()
        expect(screen.getByText('Hi, user!')).toBeInTheDocument()
    })

    it('renders markdown headings and bullets', () => {
        const msg: Message = {
            id: '3',
            sender: 'bot',
            text: '# Heading\n## Subheading\n• Bullet 1\n• Bullet 2',
            timestamp: new Date(),
        }
        render(<ChatMessage message={msg} />)
        expect(screen.getByText('Heading')).toBeInTheDocument()
        expect(screen.getByText('Subheading')).toBeInTheDocument()
        expect(screen.getByText('Bullet 1')).toBeInTheDocument()
        expect(screen.getByText('Bullet 2')).toBeInTheDocument()
    })
})
