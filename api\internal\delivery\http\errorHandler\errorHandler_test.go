package errorHandler_test

import (
	"dng-module/internal/delivery/http/errorHandler"
	"net/http"
	"net/http/httptest"
	"testing"

	// "dng-module/internal/errorHandler"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

// helper to capture logs - optional if you want to check logs
func captureLogs(t *testing.T, f func()) string {
	// assuming utils.Logger is a logrus or similar logger
	// To keep it simple, you can skip log capture or use a custom writer here
	// This is a placeholder if you want to verify logs
	f()
	return ""
}

func setupRouter() *gin.Engine {
	gin.SetMode(gin.TestMode)
	r := gin.New()
	return r
}

func TestErrorHandler_Handle404(t *testing.T) {
	r := setupRouter()
	eh := errorHandler.NewErrorHandler()

	r.GET("/notfound", func(c *gin.Context) {
		eh.Handle404(c)
	})

	req := httptest.NewRequest(http.MethodGet, "/notfound", nil)
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)

	assert.Equal(t, http.StatusNotFound, w.Code)
}

func TestErrorHandler_Handle500(t *testing.T) {
	r := setupRouter()
	eh := errorHandler.NewErrorHandler()

	r.GET("/error500", func(c *gin.Context) {
		eh.Handle500(c)
	})

	req := httptest.NewRequest(http.MethodGet, "/error500", nil)
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)

	assert.Equal(t, http.StatusInternalServerError, w.Code)
}

func TestErrorHandler_Handle403(t *testing.T) {
	r := setupRouter()
	eh := errorHandler.NewErrorHandler()

	r.GET("/forbidden", func(c *gin.Context) {
		eh.Handle403(c)
	})

	req := httptest.NewRequest(http.MethodGet, "/forbidden", nil)
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)

	assert.Equal(t, http.StatusForbidden, w.Code)
}

func TestErrorHandler_HandleError(t *testing.T) {
	r := setupRouter()
	eh := errorHandler.NewErrorHandler()

	r.GET("/customerror", func(c *gin.Context) {
		eh.HandleError(c, http.StatusTeapot) // 418 I'm a teapot
	})

	req := httptest.NewRequest(http.MethodGet, "/customerror", nil)
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)

	assert.Equal(t, http.StatusTeapot, w.Code)
}

func TestErrorHandler_NoRoute(t *testing.T) {
	r := setupRouter()
	eh := errorHandler.NewErrorHandler()

	r.NoRoute(func(c *gin.Context) {
		eh.NoRoute(c)
	})

	req := httptest.NewRequest(http.MethodGet, "/some-random-route", nil)
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)

	assert.Equal(t, http.StatusNotFound, w.Code)
}
func TestErrorHandler_NoMethod(t *testing.T) {
	r := setupRouter()

	// Enable Gin to handle method not allowed (405) instead of 404
	r.HandleMethodNotAllowed = true

	eh := errorHandler.NewErrorHandler()

	r.GET("/methodtest", func(c *gin.Context) {
		c.String(http.StatusOK, "ok")
	})

	r.NoMethod(func(c *gin.Context) {
		eh.NoMethod(c)
	})

	req := httptest.NewRequest(http.MethodPost, "/methodtest", nil) // POST not allowed on GET route
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)

	assert.Equal(t, http.StatusMethodNotAllowed, w.Code)
}
