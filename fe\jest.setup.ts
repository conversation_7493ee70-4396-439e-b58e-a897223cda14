import '@testing-library/jest-dom'
import axios from 'axios'

jest.mock('axios')

const mockedAxios = axios as jest.Mocked<typeof axios>

mockedAxios.create = jest.fn(() => mockedAxios)

process.env.NEXT_PUBLIC_BACKEND_URL = 'http://localhost:8000/dng/api'

// Polyfill for crypto.randomUUID for Jest/node environments that lack it
if (!globalThis.crypto) {
    // @ts-ignore
    globalThis.crypto = {}
}
if (!globalThis.crypto.randomUUID) {
    // @ts-ignore
    globalThis.crypto.randomUUID = () => Math.random().toString(16).slice(2) + Math.random().toString(16).slice(2)
}

// Mock axios methods globally for all tests in this file
jest.mock('axios', () => ({
    get: jest.fn(),
    post: jest.fn(),
    put: jest.fn(),
    delete: jest.fn(),
    create: () => ({
        get: jest.fn(),
        post: jest.fn(),
        put: jest.fn(),
        delete: jest.fn(),
        interceptors: { request: { use: jest.fn() }, response: { use: jest.fn() } },
    }),
}))

// Mock redux hooks globally for all tests in this file
jest.mock('@/store/hooks', () => ({
    useAppDispatch: () => jest.fn(),
    useAppSelector: jest.fn(),
}))

// Mock fetch globally for all tests in this file
beforeAll(() => {
    global.fetch = jest.fn(() =>
        Promise.resolve({
            ok: true,
            json: () => Promise.resolve({}),
        }),
    ) as jest.Mock
})

afterAll(() => {
    // Only restore if fetch is a jest mock function
    if (typeof global.fetch === 'function' && 'mockClear' in global.fetch) {
        // @ts-ignore
        global?.fetch?.mockClear()
        // @ts-ignore
        global?.fetch?.mockReset()
        // @ts-ignore
        global?.fetch?.mockRestore()
    }
})
