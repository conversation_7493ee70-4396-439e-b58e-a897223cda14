import React from 'react'
import { AppBar, Box, IconButton, Stack, styled, Toolbar } from '@mui/material'
// components
import Profile from './profile'
import MenuIcon from '@mui/icons-material/Menu'
import { Logo } from '@/components/common/layout/shared/logo/logo'

interface ItemType {
    toggleMobileSidebar: (event: React.MouseEvent<HTMLElement>) => void
    showLogo?: boolean
}

const Header = ({ toggleMobileSidebar, showLogo }: ItemType) => {
    const AppBarStyled = styled(AppBar)(({ theme }) => ({
        boxShadow: 'none',
        //background: theme.palette.background.paper,
        justifyContent: 'center',
        backdropFilter: 'blur(4px)',
        [theme.breakpoints.up('lg')]: {
            minHeight: '70px',
        },
    }))
    const ToolbarStyled = styled(Toolbar)(({ theme }) => ({
        width: '100%',
        color: theme.palette.text.secondary,
    }))

    return (
        <AppBarStyled position="sticky" color="default">
            <ToolbarStyled>
                {!showLogo && (
                    <IconButton
                        color="inherit"
                        aria-label="menu"
                        onClick={toggleMobileSidebar}
                        sx={{
                            display: {
                                lg: 'none',
                                xs: 'inline',
                            },
                        }}
                    >
                        <MenuIcon width="20" height="20" />
                    </IconButton>
                )}
                {showLogo && <Logo />}
                <Box flexGrow={1} />
                <Stack spacing={1} direction="row" alignItems="center">
                    <IconButton
                        size="large"
                        aria-label="show 11 new notifications"
                        color="inherit"
                        aria-controls="msgs-menu"
                        aria-haspopup="true"
                    >
                        {/*<Badge variant="dot" color="primary">
              <IconBellRinging size="21" stroke="1.5" />
            </Badge>*/}
                    </IconButton>
                    <Profile />
                </Stack>
            </ToolbarStyled>
        </AppBarStyled>
    )
}

export default Header
