// app/api/tableau-token/route.ts
import { NextRequest, NextResponse } from 'next/server'
import jwt, { JwtHeader, SignOptions } from 'jsonwebtoken'

const CLIENT_ID = process.env.TABLEAU_CLIENT_ID!
const SECRET_ID = process.env.TABLEAU_SECRET_ID!
const SECRET_VALUE = process.env.TABLEAU_SECRET_VALUE!
const TABLEAU_USER_EMAIL = process.env.TABLEAU_USER_EMAIL! // licensed user

export async function GET(req: NextRequest) {
    try {
        const now = Math.floor(Date.now() / 1000)

        const payload = {
            iss: CLIENT_ID,
            sub: TABLEAU_USER_EMAIL,
            aud: 'tableau',
            exp: now + 0.5 * 60,
            jti: `${now}`,
            scp: ['tableau:views:embed', 'tableau:content:read'],
        }

        const header: JwtHeader = {
            alg: 'HS256',
            kid: SECRET_ID,
        }

        const options: SignOptions = {
            algorithm: 'HS256',
            header,
        }

        const signedJWT = jwt.sign(payload, SECRET_VALUE, options)

        // Exchange JWT for access token to make API calls
        const tokenRes = await fetch('https://prod-useast-b.online.tableau.com/api/3.25/auth/signin', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json', Accept: 'application/json' },
            body: JSON.stringify({
                credentials: {
                    jwt: signedJWT,
                    site: {
                        contentUrl: 'dgmninsights',
                    },
                },
            }),
        })
        if (!tokenRes.ok) {
            const errorText = await tokenRes.text()
            return NextResponse.json(
                {
                    error: 'Failed to get access token',
                    details: errorText,
                },
                { status: 500 },
            )
        }
        const data = await tokenRes.json()
        const access_token = data.credentials?.token

        return NextResponse.json({ access_token, token: signedJWT })
    } catch (error) {
        console.error('JWT error:', error)
        return NextResponse.json({ error: error }, { status: 500 })
    }
}
