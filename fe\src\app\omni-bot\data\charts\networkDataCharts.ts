import { MONTH_LABELS } from './dcSpecificTrendsCharts'
import networkDatasetsData from './networkDataChartsData.json'

export const networkDataCharts = [
    {
        id: 'dc_audit_accuracy_trend',
        type: 'line',
        title: 'Audit Accuracy Trend by DC (6-Month)',
        data: {
            labels: MONTH_LABELS,
            datasets: networkDatasetsData,
        },
        options: {
            scales: {
                y: {
                    suggestedMin: 90,
                    suggestedMax: 100,
                    title: { display: true, text: 'Audit Accuracy (%)' },
                },
            },
            plugins: {
                annotation: {
                    annotations: {
                        targetLine: {
                            type: 'line',
                            yMin: 97,
                            yMax: 97,
                            borderColor: '#008000',
                            borderWidth: 2,
                            label: { content: 'Target: 97%', enabled: true },
                        },
                    },
                },
            },
        },
    },
]
