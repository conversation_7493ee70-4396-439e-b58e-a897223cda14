import { render, screen } from '@testing-library/react'
import TableActions from '../../../app/reports/reportCards/components/TableActions'

jest.mock('next-auth/react', () => ({
    useSession: () => ({ data: { user: { user_id: '1', role: 'admin' } } }),
}))
jest.mock('@/utils/helper', () => ({
    checkRole: () => true,
    createPBILink: () => 'http://example.com',
    downloadBlob: jest.fn(),
}))
jest.mock('@/app/reports/services', () => ({
    updatePreferenceService: jest.fn(() => Promise.resolve(200)),
    downloadPBIService: jest.fn(),
    lookerEmbedUrlService: jest.fn(),
    lookerPDFApi: jest.fn(),
}))
jest.mock('@/app/reports/slice', () => ({
    setRefreshFlag: jest.fn(),
}))
jest.mock('@/store/hooks', () => ({
    useAppDispatch: () => jest.fn(),
    useAppSelector: jest.fn((selector) =>
        selector({
            filterOptions: {
                filterOptions: {
                    domains: [],
                },
            },
        }),
    ),
}))

const row = {
    id: '1',
    source_system: 'PowerBI',
    group_id: 'g1',
    report_id: 'r1',
    is_favorite: false,
    is_saved: false,
    name: 'Test Report',
    category: '',
    description: '',
    domain_id: 1,
    created_by: '',
    created_at: '',
    updated_at: '',
    published_on: null,
}

describe('TableActions', () => {
    it('renders favorite and saved buttons', () => {
        render(<TableActions row={row as any} />)
        expect(screen.getAllByRole('button').length).toBeGreaterThan(0)
    })
})
