import '@testing-library/jest-dom'
import { fireEvent, render, screen } from '@testing-library/react'
import ExplorationQuestions from '../../app/omni-bot/components/ExplorationQuestions'
import { ExplorationQuestion } from '../../app/omni-bot/types'

const questions: ExplorationQuestion[] = [
    { question: 'What is audit accuracy?', answer: '93%' },
    { question: 'Show me error rates.', answer: '5%' },
]

describe('ExplorationQuestions', () => {
    it('renders all questions', () => {
        render(<ExplorationQuestions questions={questions} onQuestionClick={() => {}} />)
        expect(screen.getByText('What is audit accuracy?')).toBeInTheDocument()
        expect(screen.getByText('Show me error rates.')).toBeInTheDocument()
    })

    it('calls onQuestionClick when a question is clicked', () => {
        const onQuestionClick = jest.fn()
        render(<ExplorationQuestions questions={questions} onQuestionClick={onQuestionClick} />)
        fireEvent.click(screen.getByText('Show me error rates.'))
        expect(onQuestionClick).toHaveBeenCalledWith('Show me error rates.')
    })
})
