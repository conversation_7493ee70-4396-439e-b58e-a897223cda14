package healthHandler

import (
	"dng-module/config"
	"dng-module/internal/utils"
	"net/http"
	"os"
	"runtime"
	"time"

	"github.com/gin-gonic/gin"
)

const (
	HeaderHealthStatus      = "x-health-status"
	HeaderDatabaseConnected = "x-health-database-connected"
)

// HealthLiveHandler handles the /health/live endpoint
func HealthLiveHandler(c *gin.Context) {
	c.<PERSON>(HeaderHealthStatus, "ok")

	// For HEAD requests, just return status
	if c.Request.Method == "HEAD" {
		c.Status(http.StatusOK)
		return
	}

	// For GET requests, return a JSON response
	c.JSON(http.StatusOK, gin.H{
		"status":    "ok",
		"message":   "Service is up and running",
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

// HealthInfoHandler handles the /health/info endpoint
// This provides information about the application
func HealthInfoHandler(c *gin.Context) {
	c.<PERSON>(HeaderHealthStatus, "ok")

	// Get hostname
	hostname, _ := os.Hostname()

	// Return application information
	c.J<PERSON>N(http.StatusOK, gin.H{
		"status": "ok",
		"application": gin.H{
			"name":        "DNG Module",
			"environment": os.Getenv("APP_ENV"),
			"version":     os.Getenv("APP_VERSION"),
			"build_time":  os.Getenv("BUILD_TIME"),
		},
		"system": gin.H{
			"hostname":      hostname,
			"go_version":    runtime.Version(),
			"os":            runtime.GOOS,
			"arch":          runtime.GOARCH,
			"num_cpu":       runtime.NumCPU(),
			"num_goroutine": runtime.NumGoroutine(),
		},
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

// HealthReadyHandler handles the /health/ready endpoint
// This is a readiness probe that checks if the application is ready to serve traffic
// It checks database connectivity
func HealthReadyHandler(c *gin.Context) {
	utils.Logger.Infof("Health %s check requested from %s for path %s", c.Request.Method, c.ClientIP(), c.Request.URL.Path)

	// For HEAD requests, we perform the same checks but don't return a body
	if c.Request.Method == "HEAD" {
		utils.Logger.Info("Performing database connectivity check for HEAD request")
		checkDatabaseConnectivity(c)
		return
	}

	// For GET requests, we return a more detailed response
	_, err := IsDatabaseConnected()

	response := gin.H{
		"status": "ok",
		"components": gin.H{
			"database": gin.H{
				"status": "ok",
			},
		},
	}

	if err != nil {
		response["status"] = "degraded"
		response["components"].(gin.H)["database"] = gin.H{
			"status": "down",
			"error":  err.Error(),
		}
		c.Header(HeaderHealthStatus, "degraded")
		c.Header(HeaderDatabaseConnected, "false")
		c.JSON(http.StatusOK, response)
		return
	}

	c.Header(HeaderHealthStatus, "ok")
	c.Header(HeaderDatabaseConnected, "true")
	c.JSON(http.StatusOK, response)
}

// Helper function to check database connectivity
func checkDatabaseConnectivity(c *gin.Context) {
	utils.Logger.Info("Checking database connectivity")
	_, err := IsDatabaseConnected()

	if err != nil {
		utils.Logger.Errorf("Database connectivity check failed: %v", err)
		c.Header(HeaderHealthStatus, "degraded")
		c.Header(HeaderDatabaseConnected, "false")
		c.Status(http.StatusOK)
		return
	}

	utils.Logger.Info("Database connectivity check passed")
	utils.Logger.Info("Database connected: true")
	utils.Logger.Infof("Returning status %d with health status '%s'", http.StatusOK, "ok")

	c.Header(HeaderHealthStatus, "ok")
	c.Header(HeaderDatabaseConnected, "true")
	c.Status(http.StatusOK)
}

// Helper function to check if the database is connected
func IsDatabaseConnected() (bool, error) {
	utils.Logger.Info("Pinging database...")

	// For GORM, we can use DB.Raw to execute a simple SQL query
	// to check if the database is connected
	result := config.DB.Exec("SELECT 1")
	if result.Error != nil {
		return false, result.Error
	}

	return true, nil
}
