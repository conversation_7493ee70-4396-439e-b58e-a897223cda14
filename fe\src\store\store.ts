import filterOptionsReducer from '@/app/reports/reportSearch/slice'
import reportsReducer from '@/app/reports/slice'
import applicationSlice from '@/app/appSlice'
import { configureStore } from '@reduxjs/toolkit'

// config the store
const store = configureStore({
    reducer: {
        application: applicationSlice,
        reports: reportsReducer,
        filterOptions: filterOptionsReducer,
    },
})

export default store
export type RootState = ReturnType<typeof store.getState>
export type AppDispatch = typeof store.dispatch
