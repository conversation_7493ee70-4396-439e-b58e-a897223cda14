'use client'

import { resetActiveFilters } from '@/app/reports/slice'
import { DateRangePicker } from '@/components/common/utility/DatePicker'
import { useAppDispatch } from '@/store/hooks'
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown'
import ClearIcon from '@mui/icons-material/Clear'
import {
    Box,
    Button,
    FormControl,
    IconButton,
    InputAdornment,
    InputLabel,
    MenuItem,
    Select,
    SelectChangeEvent,
} from '@mui/material'
import React, { useCallback, useEffect, useMemo, useState } from 'react'
import { Domain } from '../../types'
import { FilterKey, FilterOption, IFilterOptions } from '../types'

const FILTER_KEYS: FilterKey[] = ['sourceSystem', 'domain', 'category']

const CustomSelectIcon: React.FC<{ showIcon: boolean }> = ({ showIcon }) => (showIcon ? <ArrowDropDownIcon /> : null)

const getIconComponent = (showIcon: boolean) => {
    return function IconComponent() {
        return <CustomSelectIcon showIcon={showIcon} />
    }
}

const FilterPanel: React.FC<{
    onApply: (filters: {
        sourceSystem: string
        domain: string
        category: string
        dates: [Date | null, Date | null]
    }) => void
    initialValues?: Partial<Record<FilterKey, string>>
    filterOptions: IFilterOptions
}> = ({ onApply, initialValues = {}, filterOptions }) => {
    const dispatch = useAppDispatch()

    const [filters, setFilters] = useState<Record<FilterKey, string>>({
        sourceSystem: initialValues.sourceSystem ?? '-',
        domain: initialValues.domain ?? '-',
        category: initialValues.category ?? '-',
    })

    const [dateRange, setDateRange] = useState<[Date | null, Date | null]>([null, null])

    useEffect(() => {
        setFilters((prev) => ({
            sourceSystem: initialValues.sourceSystem ?? prev.sourceSystem,
            domain: initialValues.domain ?? prev.domain,
            category: initialValues.category ?? prev.category,
        }))
    }, [initialValues])

    const FILTER_OPTIONS: Record<FilterKey, FilterOption> = useMemo(() => {
        return {
            sourceSystem: {
                label: 'Source System',
                options: formatWithFallback(formatOptions(filterOptions.source_system), 'None'),
            },
            domain: {
                label: 'Domain',
                options: formatWithFallback(formatOptions(filterOptions.domains, true), 'None'),
            },
            category: {
                label: 'Category',
                options: formatWithFallback(formatOptions(filterOptions.category), 'None'),
            },
        }
    }, [filterOptions])

    const handleFilterChange = useCallback(
        (field: FilterKey) => (event: SelectChangeEvent<string>) => {
            setFilters((prev) => ({
                ...prev,
                [field]: event.target.value,
            }))
        },
        [],
    )

    const clearIndividualFilter = useCallback((field: FilterKey) => {
        setFilters((prev) => ({
            ...prev,
            [field]: '-',
        }))
    }, [])

    const handleDateChange = useCallback((dates: [Date | null, Date | null]) => {
        setDateRange(dates)
    }, [])

    const handleClearFilters = useCallback(() => {
        setFilters({ sourceSystem: '-', domain: '-', category: '-' })
        setDateRange([null, null])
        dispatch(resetActiveFilters())
        //dispatch(fetchReports())
    }, [dispatch])

    const handleApplyFilters = useCallback(() => {
        const domainValue = filters.domain !== '-' ? Number(filters.domain) : null
        onApply({
            sourceSystem: filters.sourceSystem,
            domain: domainValue?.toString() ?? '-',
            category: filters.category,
            dates: dateRange,
        })
    }, [filters, dateRange, onApply])

    return (
        <Box component="form" sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
            <Box
                sx={{
                    display: 'grid',
                    gridTemplateColumns: {
                        xs: '1fr',
                        sm: '1fr 1fr',
                        md: '1fr 1fr 1fr 1fr',
                    },
                    alignItems: 'center',
                    gap: 1,
                }}
            >
                {FILTER_KEYS.map((key) => (
                    <FormControl key={key} size="small">
                        <InputLabel color="secondary">{FILTER_OPTIONS[key].label}</InputLabel>
                        <Select
                            value={filters[key]}
                            onChange={handleFilterChange(key)}
                            label={FILTER_OPTIONS[key].label}
                            color="secondary"
                            sx={{ borderRadius: 1 }}
                            IconComponent={getIconComponent(filters[key] === '-')}
                            endAdornment={
                                filters[key] !== '-' && (
                                    <InputAdornment position="end">
                                        <IconButton
                                            size="small"
                                            onClick={() => clearIndividualFilter(key)}
                                            edge="end"
                                            aria-label={`Clear ${key}`}
                                        >
                                            <ClearIcon fontSize="small" />
                                        </IconButton>
                                    </InputAdornment>
                                )
                            }
                        >
                            {FILTER_OPTIONS[key].options.map((option) => (
                                <MenuItem key={option.value} value={option.value}>
                                    {option.label}
                                </MenuItem>
                            ))}
                        </Select>
                    </FormControl>
                ))}
                <DateRangePicker value={dateRange} onChange={handleDateChange} label="Published Dates" />
            </Box>

            <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2, mt: 0 }}>
                <Button
                    variant="outlined"
                    size={'small'}
                    onClick={handleClearFilters}
                    sx={{ borderRadius: 20 }}
                    color="secondary"
                >
                    Clear filters
                </Button>
                <Button
                    variant="contained"
                    size={'small'}
                    onClick={handleApplyFilters}
                    sx={{ borderRadius: 20 }}
                    color="secondary"
                >
                    Apply
                </Button>
            </Box>
        </Box>
    )
}

export default FilterPanel

// ====================
// 🔽 UTILITY FUNCTIONS
// ====================

const formatOptions = (items: string[] | Domain[], isDomain = false): { value: string; label: string }[] => {
    return items.length === 0
        ? []
        : (items as any[]).map((item) =>
              isDomain
                  ? { value: String((item as Domain).id), label: (item as Domain).name }
                  : { value: item as string, label: item as string },
          )
}

const formatWithFallback = (
    list: { value: string; label: string }[],
    fallbackLabel: string,
): { value: string; label: string }[] => {
    return list.length > 0 ? [{ value: '-', label: 'All' }, ...list] : [{ value: '-', label: fallbackLabel }]
}
