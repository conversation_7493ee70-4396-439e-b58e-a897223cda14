'use client'

import { useState } from 'react'
import { <PERSON>, <PERSON><PERSON>, Card, Chip, <PERSON>lapse, Di<PERSON>r, Fade, Grid2 as Grid, Icon<PERSON>utton, Typography } from '@mui/material'
import { ArrowBackIos, ArrowForwardIos } from '@mui/icons-material'
import { IAlert } from '@/app/alerts/types'
import { CalendarIcon } from '@mui/x-date-pickers'
import { format } from 'date-fns'
import CampaignIcon from '@mui/icons-material/Campaign'

interface AlertsNewsCarouselProps {
    alerts: IAlert[]
}

export default function AlertsCarousel({ alerts }: Readonly<AlertsNewsCarouselProps>) {
    const [tabValue, setTabValue] = useState<'news' | 'alert'>('news')
    const [newsIndex, setNewsIndex] = useState(0)
    const [eventIndex, setEventIndex] = useState(0)
    const [expandedNews, setExpandedNews] = useState(false)
    const [expandedEvent, setExpandedEvent] = useState(false)

    const messages = Array.isArray(alerts) ? alerts.filter((a) => a.type === tabValue) : []
    const events = Array.isArray(alerts) ? alerts.filter((a) => a.type === 'event') : []

    const currentNews = messages[newsIndex]
    const currentEvent = events[eventIndex]

    const isLongNews = currentNews?.description.length > 100
    const isLongEvent = currentEvent?.description.length > 100

    return (
        <Grid container spacing={2}>
            <Grid size={{ xs: 12, md: 8 }}>
                <Card
                    elevation={0}
                    sx={{
                        textAlign: 'left',
                        p: 1,
                        height: 180,
                        flexDirection: 'column',
                        justifyContent: 'left',
                        position: 'relative',
                    }}
                >
                    <Box display="flex" justifyContent="left" alignItems="left" gap={1} mb={1}>
                        <CampaignIcon />
                        <Chip
                            label="News"
                            clickable
                            color={tabValue === 'news' ? 'secondary' : 'default'}
                            onClick={() => {
                                setExpandedNews(false)
                                setNewsIndex(0)
                                setTabValue('news')
                            }}
                            size="small"
                        />
                        {/*<Chip
                            label="Messages"
                            clickable
                            color={tabValue === 'alert' ? 'secondary' : 'default'}
                            onClick={() => {
                                setExpandedNews(false)
                                setNewsIndex(0)
                                setTabValue('alert')
                            }}
                            size="small"
                        />*/}
                    </Box>

                    <Divider sx={{ my: 1, backgroundColor: '#000' }} />

                    {messages.length === 0 ? (
                        <Typography>No {tabValue} alerts available.</Typography>
                    ) : (
                        <>
                            {messages.length > 1 && (
                                <IconButton
                                    onClick={() => setNewsIndex(newsIndex === 0 ? messages.length - 1 : newsIndex - 1)}
                                    size="small"
                                    sx={{ position: 'absolute', left: 8, top: '50%', transform: 'translateY(-50%)' }}
                                >
                                    <ArrowBackIos sx={{ fontSize: 14 }} />
                                </IconButton>
                            )}

                            <Fade in={true} key={currentNews?.id} timeout={300}>
                                <Box sx={{ m: 3, mt: 1.5, maxHeight: 120, overflowY: 'auto' }}>
                                    <Typography variant="subtitle2" fontWeight="bold">
                                        {currentNews?.title}
                                    </Typography>
                                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 1 }}>
                                        <CalendarIcon fontSize="small" color="action" />
                                        <Typography variant="subtitle1">
                                            {currentNews?.start_date_time &&
                                                format(currentNews?.start_date_time, 'MMM dd, yyyy')}
                                        </Typography>
                                    </Box>
                                    <Collapse in={expandedNews || !isLongNews} collapsedSize={20}>
                                        <Typography variant="body2" display="block" sx={{ mt: 1 }}>
                                            {currentNews?.description}
                                        </Typography>
                                        {expandedNews && (
                                            <Chip
                                                variant="outlined"
                                                label={`Created by: ${currentNews?.created_by}`}
                                                color="secondary"
                                                size="small"
                                                sx={{ mt: 1 }}
                                            />
                                        )}
                                    </Collapse>

                                    {isLongNews && (
                                        <Button
                                            size="small"
                                            color="info"
                                            variant="contained"
                                            onClick={() => setExpandedNews(!expandedNews)}
                                            sx={{ mt: 1, fontSize: 10, p: 0.5 }}
                                        >
                                            {expandedNews ? 'Show less' : 'Read more'}
                                        </Button>
                                    )}
                                </Box>
                            </Fade>

                            {messages.length > 1 && (
                                <IconButton
                                    onClick={() => setNewsIndex(newsIndex === messages.length - 1 ? 0 : newsIndex + 1)}
                                    size="small"
                                    sx={{ position: 'absolute', right: 8, top: '50%', transform: 'translateY(-50%)' }}
                                >
                                    <ArrowForwardIos sx={{ fontSize: 14 }} />
                                </IconButton>
                            )}
                        </>
                    )}
                </Card>
            </Grid>

            {/* Events Carousel */}
            <Grid size={{ xs: 12, md: 4 }}>
                <Card
                    elevation={0}
                    sx={{
                        textAlign: 'center',
                        p: 1,
                        height: 180,
                        flexDirection: 'column',
                        justifyContent: 'center',
                        position: 'relative',
                    }}
                >
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 1, ml: 12 }}>
                        <CalendarIcon fontSize="small" color="action" />
                        <Typography variant="body2" fontWeight="bold" color="text.primary">
                            Upcoming Events
                        </Typography>
                    </Box>
                    <Divider sx={{ my: 0.6, backgroundColor: '#000' }} />

                    {events.length === 0 ? (
                        <Typography>No upcoming events.</Typography>
                    ) : (
                        <>
                            {events.length > 1 && (
                                <IconButton
                                    onClick={() => {
                                        setExpandedEvent(false)
                                        setEventIndex(eventIndex === 0 ? events.length - 1 : eventIndex - 1)
                                    }}
                                    size="small"
                                    sx={{ position: 'absolute', left: 8, top: '50%', transform: 'translateY(-50%)' }}
                                >
                                    <ArrowBackIos sx={{ fontSize: 14 }} />
                                </IconButton>
                            )}

                            <Fade in={true} key={currentEvent?.id} timeout={300}>
                                <Box sx={{ m: 3, mt: 1.5, maxHeight: 120, overflowY: 'auto' }}>
                                    <Typography variant="subtitle2" fontWeight="bold">
                                        {currentEvent?.title}
                                    </Typography>
                                    {/*<Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 1, ml: 12 }}>
                                        <CalendarIcon fontSize="small" color="action" />*/}
                                    <Typography variant="subtitle1">
                                        {currentEvent?.start_date_time &&
                                            format(currentEvent.start_date_time, 'MMM dd, yyyy')}
                                    </Typography>
                                    {/*</Box>*/}
                                    <Collapse in={expandedEvent || !isLongEvent} collapsedSize={20}>
                                        <Typography variant="body2" display="block" sx={{ mt: 1 }}>
                                            {currentEvent?.description}
                                        </Typography>
                                    </Collapse>

                                    {isLongEvent && (
                                        <Button
                                            size="small"
                                            color="info"
                                            variant="contained"
                                            onClick={() => setExpandedEvent(!expandedEvent)}
                                            sx={{ mt: 1, fontSize: 10, p: 0.5 }}
                                        >
                                            {expandedEvent ? 'Show less' : 'Read more'}
                                        </Button>
                                    )}
                                </Box>
                            </Fade>

                            {events.length > 1 && (
                                <IconButton
                                    onClick={() => {
                                        setExpandedEvent(false)
                                        setEventIndex(eventIndex === events.length - 1 ? 0 : eventIndex + 1)
                                    }}
                                    size="small"
                                    sx={{ position: 'absolute', right: 8, top: '50%', transform: 'translateY(-50%)' }}
                                >
                                    <ArrowForwardIos sx={{ fontSize: 14 }} />
                                </IconButton>
                            )}
                        </>
                    )}
                </Card>
            </Grid>
        </Grid>
    )
}
