import { upsertUser } from '@/app/api/auth/[...nextauth]/services'
import { Account, AuthOptions } from 'next-auth'
import { JWT } from 'next-auth/jwt'
import AzureADProvider from 'next-auth/providers/azure-ad'

// Define custom token type for better type safety
interface CustomToken extends JWT {
    access_token?: string
    refresh_token?: string
    expires_at?: number
    details?: any
    error?: string
}

export const authOptions: AuthOptions = {
    providers: [
        AzureADProvider({
            clientId: process.env.AZURE_CLIENT_ID!,
            clientSecret: process.env.AZURE_CLIENT_SECRET!,
            tenantId: process.env.AZURE_TENANT_ID!,
            authorization: {
                params: { scope: 'openid profile user.Read email offline_access' },
            },
        }),
    ],
    secret: process.env.NEXTAUTH_SECRET,
    session: {
        strategy: 'jwt',
        maxAge: 30 * 24 * 60 * 60, // 30 days
    },
    callbacks: {
        async jwt({ token, account, profile }) {
            // console.log('[JWT Callback] Token:', account)

            // Handle initial sign in
            if (account && profile) {
                const [first_name, last_name] = profile.name?.split(' ') || []
                const ad_groups = await fetchAzureGroups(account)
                const email = profile.email ?? ''
                const response = await upsertUser({
                    email: email,
                    ad_groups,
                    first_name,
                    last_name,
                    access_token: account?.access_token ?? null,
                })

                // IMPORTANT FIX: Properly calculate expires_at if not directly provided
                // expires_in is in seconds, so convert to milliseconds and add to current time
                let expiresAt: number
                if (account.expires_at) {
                    // If expires_at is already a timestamp (in seconds), convert to milliseconds
                    expiresAt = account.expires_at * 1000
                } else {
                    // Default to 1 hour if neither is provided
                    expiresAt = Date.now() + 3600 * 1000
                }

                // console.log('Setting initial token expiry to:', new Date(expiresAt).toISOString())

                const customToken = token as CustomToken
                customToken.access_token = account?.access_token
                customToken.refresh_token = account.refresh_token
                customToken.expires_at = expiresAt
                customToken.details = response.data

                return customToken
            }

            const customToken = token as CustomToken

            // Debug token expiration info
            // if (customToken.expires_at) {
            //     const expirationDate = new Date(customToken.expires_at)
            //     const nowDate = new Date()
            //     console.log('Token expiration date:', expirationDate.toISOString())
            //     console.log('Current date:', nowDate.toISOString())
            //     console.log('Time difference (minutes):', (customToken.expires_at - Date.now()) / (1000 * 60))
            // } else {
            //     console.log('No expires_at found in token')
            // }

            // Token not expired, return it
            // IMPORTANT: Ensure expires_at is in milliseconds for correct comparison with Date.now()
            if (customToken.expires_at && Date.now() < customToken.expires_at) {
                // console.log('Token still valid, returning existing token')
                return customToken
            }

            // Token expired or expiry data missing, try to refresh it
            // console.log('Token expired or expiry missing, attempting refresh')
            return refreshAccessToken(customToken)
        },

        async session({ session, token }) {
            // console.log('[Session Callback] Session:', session, token)
            const customToken = token as CustomToken

            const user = {
                ...(customToken.details ?? {}),
                ...session.user,
                access_token: customToken.access_token,
                refresh_token: customToken.refresh_token,
            }

            return { ...session, user }
        },
    },
    pages: {
        signIn: '/authentication/login',
        error: '/authentication/login',
    },
}

async function fetchAzureGroups(account: Account | null): Promise<string[]> {
    if (!account?.access_token) return []
    return ['gcp-iam-bus-da-super-admin', 'gcp-iam-bus-da-admin-dev', 'gcp-iam-bus-da-viewer-dev']
}

export default authOptions

// Helper function for token refresh with better error handling and logging
async function refreshAccessToken(token: CustomToken): Promise<CustomToken> {
    // console.log('token received', token)
    try {
        // Check if we have a refresh token
        if (!token.refresh_token) {
            // console.error('No refresh token available')
            return {
                ...token,
                error: 'NoRefreshTokenError',
            }
        }

        // console.log('Attempting to refresh token with refresh_token length:', token.refresh_token.length)

        // Fix: Use the same tenant ID that was used in the provider setup
        const tenantId = process.env.AZURE_TENANT_ID
        if (!tenantId) {
            throw new Error('AZURE_TENANT_ID is not defined in environment variables')
        }

        const url = `https://login.microsoftonline.com/${tenantId}/oauth2/v2.0/token`

        // Create params object for better debugging
        const params = {
            client_id: process.env.AZURE_CLIENT_ID!,
            client_secret: process.env.AZURE_CLIENT_SECRET!,
            grant_type: 'refresh_token',
            refresh_token: token.refresh_token,
        }

        // console.log('Refresh token params (excluding the actual token):', {
        //     client_id: params.client_id,
        //     grant_type: params.grant_type,
        //     hasClientSecret: !!params.client_secret,
        //     tenantId: tenantId, // Log the tenant ID being used
        // })

        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams(params),
        })

        const refreshedTokens = await response.json()

        if (!response.ok) {
            // console.error('Token refresh failed with status:', response.status)
            // console.error('Error details:', refreshedTokens)
            throw new Error(refreshedTokens.error_description ?? refreshedTokens.error)
        }

        // console.log('Token refreshed successfully with new token length:', refreshedTokens.access_token.length)

        // Calculate expiry time (Azure AD typically includes expires_in in the refresh response)
        const expiresIn = refreshedTokens.expires_in ?? 3600 // Default to 1 hour if not provided
        const expiresAt = Date.now() + expiresIn * 1000

        return {
            ...token,
            access_token: refreshedTokens.access_token,
            expires_at: expiresAt,
            refresh_token: refreshedTokens.refresh_token ?? token.refresh_token,
        }
    } catch (error) {
        console.error('Error refreshing token:', error)

        return {
            ...token,
            error: 'RefreshAccessTokenError',
        }
    }
}
