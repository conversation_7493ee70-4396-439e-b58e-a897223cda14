'use client'
import React from 'react'
import dynamic from 'next/dynamic'
import { Box, Typography, Paper } from '@mui/material'

// Dynamically import ApexCharts to avoid SSR issues
const Chart = dynamic(() => import('react-apexcharts'), { ssr: false })

const ShoppingTrendsChart: React.FC = () => {
    // Chart options
    const options = {
        chart: {
            id: 'shopping-trends',
            toolbar: {
                show: false
            },
            fontFamily: 'Arial, sans-serif',
        },
        colors: ['#FFC107', '#FF9800', '#2196F3', '#4CAF50'],
        xaxis: {
            categories: ['Q1 2023', 'Q2 2023', 'Q3 2023', 'Q4 2023', 'Q1 2024'],
            labels: {
                style: {
                    colors: '#616161',
                    fontSize: '12px',
                    fontFamily: 'Arial, sans-serif',
                }
            }
        },
        yaxis: {
            title: {
                text: 'Percentage of Total Sales',
                style: {
                    color: '#616161',
                    fontSize: '12px',
                    fontFamily: 'Arial, sans-serif',
                }
            },
            labels: {
                formatter: function(value: number) {
                    return value + '%'
                },
                style: {
                    colors: '#616161',
                    fontSize: '12px',
                    fontFamily: 'Arial, sans-serif',
                }
            }
        },
        dataLabels: {
            enabled: false
        },
        stroke: {
            curve: 'smooth',
            width: 2
        },
        legend: {
            position: 'top',
            horizontalAlign: 'right',
            fontSize: '12px',
            fontFamily: 'Arial, sans-serif',
            labels: {
                colors: '#616161'
            }
        },
        tooltip: {
            theme: 'light',
            y: {
                formatter: function(value: number) {
                    return value + '%'
                }
            }
        },
        grid: {
            borderColor: '#f1f1f1',
            row: {
                colors: ['transparent', 'transparent'],
                opacity: 0.5
            }
        }
    }

    // Chart series data
    const series = [
        {
            name: 'Mobile Shopping',
            data: [42, 48, 56, 65, 70]
        },
        {
            name: 'Desktop Shopping',
            data: [58, 52, 44, 35, 30]
        },
        {
            name: 'Voice Search Usage',
            data: [5, 8, 12, 18, 25]
        },
        {
            name: 'AR Features Usage',
            data: [2, 4, 7, 12, 18]
        }
    ]

    return (
        <Paper 
            elevation={0}
            sx={{
                p: 2,
                borderRadius: 3,
                border: '1px solid',
                borderColor: 'divider',
                backgroundColor: 'white',
                mb: 3
            }}
        >
            <Typography variant="h6" gutterBottom sx={{ fontWeight: 'bold', color: '#424242' }}>
                Online Shopping Trends
            </Typography>
            <Typography variant="body2" sx={{ color: 'text.secondary', mb: 2 }}>
                Quarterly breakdown of shopping channels and feature usage
            </Typography>
            <Box sx={{ height: 300 }}>
                <Chart 
                    options={options as any}
                    series={series}
                    type="line"
                    height="100%"
                    width="100%"
                />
            </Box>
        </Paper>
    )
}

export default ShoppingTrendsChart
