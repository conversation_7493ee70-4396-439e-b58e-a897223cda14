package catalogReportService

import (
	"bytes"
	"net/http"
	"testing"

	// "dng-module/internal/app/catalogReportService"

	"github.com/stretchr/testify/assert"
	"github.com/xuri/excelize/v2"
)

// mockResponseWriter implements http.ResponseWriter for capturing output
type mockResponseWriter struct {
	header http.Header
	body   *bytes.Buffer
	status int
}

func newMockResponseWriter() *mockResponseWriter {
	return &mockResponseWriter{
		header: make(http.Header),
		body:   new(bytes.Buffer),
	}
}

func (m *mockResponseWriter) Header() http.Header {
	return m.header
}

func (m *mockResponseWriter) Write(b []byte) (int, error) {
	return m.body.Write(b)
}

func (m *mockResponseWriter) WriteHeader(statusCode int) {
	m.status = statusCode
}

func TestGenerateExcelTemplate(t *testing.T) {
	w := newMockResponseWriter()

	err := GenerateExcelTemplate(w)
	assert.NoError(t, err, "GenerateExcelTemplate should not return error")

	// Check headers
	assert.Equal(t, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", w.Header().Get("Content-Type"))
	assert.Equal(t, "attachment; filename=Report_Template.xlsx", w.Header().Get("Content-Disposition"))
	assert.Equal(t, "Report_Template.xlsx", w.Header().Get("File-Name"))
	assert.Equal(t, "File-Name", w.Header().Get("Access-Control-Expose-Headers"))

	// Check response body is not empty (Excel file written)
	assert.Greater(t, w.body.Len(), 0, "Response body should contain Excel file data")

	// Optional: Try to open excel content from bytes to verify minimal validity
	// This requires importing github.com/xuri/excelize/v2 in your test file
	f, err := excelize.OpenReader(bytes.NewReader(w.body.Bytes()))
	assert.NoError(t, err, "Excel file content should be valid")

	// Check that the expected sheets exist
	sheets := f.GetSheetList()
	assert.Contains(t, sheets, "Template")
	assert.Contains(t, sheets, "Instructions")
}
