// app/tableau/page.tsx or a component
'use client'

import { useEffect, useRef, useState } from 'react'
import { TableauViz } from '@tableau/embedding-api'

export default function TableauEmbed() {
    const containerRef = useRef<HTMLDivElement>(null)
    const [token, setToken] = useState<string | null>(null)

    const tableauUrl =
        'https://prod-useast-b.online.tableau.com/#/site/dgmninsights/views/CriteoEOCDashboard/CriteoEOCview?:iid=1'
    //'https://prod-useast-b.online.tableau.com/#/site/dgmninsights/views/EndOfCampaignReportingDashboard-New/FilterSelection?:iid=1'

    useEffect(() => {
        const fetchToken = async () => {
            const res = await fetch('/api/tab')
            /*const tableautoken = await fetch('/api/tableau-token')
            const { access_token } = await tableautoken.json()
            console.log(access_token)

            const metaData = await fetch('/api/tableau-view', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    viewId: '2474981',
                    siteId: '6f8ad44b-6044-433a-a1cf-7f790f2c5abd',
                    access_token,
                }),
            })
            console.log(await metaData.json())*/
            const data = await res.json()
            setToken(data.token)
        }

        fetchToken()
    }, [])

    useEffect(() => {
        if (!token || !containerRef.current) return

        containerRef.current.innerHTML = '' // clean up if re-rendered

        const viz = new TableauViz()
        viz.src = tableauUrl
        viz.token = token
        //viz.toolbar = 'bottom'
        viz.height = '800px'
        viz.width = '100%'

        containerRef.current.appendChild(viz)
    }, [token])

    return (
        <div className="p-4">
            <h1 className="text-xl font-semibold mb-4">Embedded Tableau Dashboard</h1>
            <div ref={containerRef} />
        </div>
    )
}
