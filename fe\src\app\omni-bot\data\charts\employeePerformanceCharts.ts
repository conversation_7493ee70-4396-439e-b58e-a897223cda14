import { MONTH_LABELS } from './dcSpecificTrendsCharts'
import employeePerformanceDatasets from './employeePerformanceChartsData.json'

// Employee Performance Charts
export const employeePerformanceCharts = [
    {
        id: 'employee_audit_accuracy',
        type: 'bar',
        title: 'Employee Audit Accuracy (Current Month)',
        data: {
            labels: MONTH_LABELS,
            datasets: employeePerformanceDatasets,
        },
    },
    {
        id: 'audit_completion_trend',
        type: 'line',
        title: 'Weekly Audit Completion Rate (4-Week)',
        data: {
            labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],
            datasets: [
                {
                    label: 'Completion %',
                    data: [92.5, 94.8, 91.2, 96.7],
                    borderColor: '#FFC107',
                    backgroundColor: 'rgba(255, 193, 7, 0.1)',
                    fill: true,
                },
            ],
        },
    },
]
