import { cleanup, fireEvent, render, screen } from '@testing-library/react'

import AuthLogin from '@/app/authentication/auth/auth-login'
import { signIn } from 'next-auth/react'

// Mock next-auth
jest.mock('next-auth/react', () => ({
    signIn: jest.fn(),
}))

// Mock useSearchParams to prevent null errors
jest.mock('next/navigation', () => ({
    ...jest.requireActual('next/navigation'),
    useSearchParams: jest.fn(() => ({
        get: () => null,
    })),
}))

describe('AuthLogin Component', () => {
    afterEach(() => {
        cleanup()
        jest.clearAllMocks()
    })

    it('renders login button and subtitle', () => {
        render(<AuthLogin title="Login" subtitle={<div>Welcome back</div>} />)

        expect(screen.getByText('Sign In')).toBeInTheDocument()
        expect(screen.getByRole('button', { name: /Sign In With SSO/i })).toBeInTheDocument()
    })

    it('calls signIn on click', async () => {
        render(<AuthLogin />)

        const button = screen.getByRole('button', { name: /Sign In With SSO/i })

        await fireEvent.click(button)

        expect(signIn).toHaveBeenCalledWith('azure-ad', { callbackUrl: '/' })

        // Wait for state update (simulate loading UI)
        expect(await screen.findByText('Signing In...')).toBeInTheDocument()
    })

    it('disables button while loading', async () => {
        render(<AuthLogin />)

        const button = screen.getByRole('button', { name: /Sign In With SSO/i })

        await fireEvent.click(button)

        expect(button).toBeDisabled()
        expect(await screen.findByText(/Signing In.../i)).toBeInTheDocument()
    })
})
