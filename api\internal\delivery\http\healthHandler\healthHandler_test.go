package healthHandler

import (
	"encoding/json"
	"errors"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"
	"time"

	"dng-module/config"
	"dng-module/testing/utilsTest"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
	"gorm.io/gorm"
)

type HealthHandlerTestSuite struct {
	suite.Suite
	router *gin.Engine
	db     *gorm.DB
	mock   sqlmock.Sqlmock
}

func (suite *HealthHandlerTestSuite) SetupTest() {
	// Setup Gin router
	gin.SetMode(gin.TestMode)
	suite.router = gin.New()

	// Setup mock DB
	db, mock := utilsTest.SetupMockDB(suite.T())
	suite.db = db
	suite.mock = mock
	config.DB = suite.db

	// Initialize logger
	// utils.InitializeLogger("info")
}

func (suite *HealthHandlerTestSuite) TearDownTest() {
	utilsTest.CloseMockDB(suite.db)
}

func TestHealthHandlerSuite(t *testing.T) {
	suite.Run(t, new(HealthHandlerTestSuite))
}

func (suite *HealthHandlerTestSuite) TestHealthLiveHandler_GET() {
	// Setup
	suite.router.GET("/health/live", HealthLiveHandler)

	req, _ := http.NewRequest("GET", "/health/live", nil)
	resp := httptest.NewRecorder()

	// Execute
	suite.router.ServeHTTP(resp, req)

	// Verify
	assert.Equal(suite.T(), http.StatusOK, resp.Code)
	assert.Equal(suite.T(), "ok", resp.Header().Get(HeaderHealthStatus))

	var response map[string]interface{}
	err := json.Unmarshal(resp.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)

	assert.Equal(suite.T(), "ok", response["status"])
	assert.Equal(suite.T(), "Service is up and running", response["message"])
	_, err = time.Parse(time.RFC3339, response["timestamp"].(string))
	assert.NoError(suite.T(), err)
}

func (suite *HealthHandlerTestSuite) TestHealthLiveHandler_HEAD() {
	// Setup
	suite.router.HEAD("/health/live", HealthLiveHandler)

	req, _ := http.NewRequest("HEAD", "/health/live", nil)
	resp := httptest.NewRecorder()

	// Execute
	suite.router.ServeHTTP(resp, req)

	// Verify
	assert.Equal(suite.T(), http.StatusOK, resp.Code)
	assert.Equal(suite.T(), "ok", resp.Header().Get(HeaderHealthStatus))
	assert.Equal(suite.T(), 0, resp.Body.Len())
}

func (suite *HealthHandlerTestSuite) TestHealthInfoHandler() {
	// Setup environment variables for the test
	os.Setenv("APP_ENV", "test")
	os.Setenv("APP_VERSION", "1.0.0")
	os.Setenv("BUILD_TIME", "2023-01-01T00:00:00Z")
	defer func() {
		os.Unsetenv("APP_ENV")
		os.Unsetenv("APP_VERSION")
		os.Unsetenv("BUILD_TIME")
	}()

	suite.router.GET("/health/info", HealthInfoHandler)

	req, _ := http.NewRequest("GET", "/health/info", nil)
	resp := httptest.NewRecorder()

	// Execute
	suite.router.ServeHTTP(resp, req)

	// Verify
	assert.Equal(suite.T(), http.StatusOK, resp.Code)
	assert.Equal(suite.T(), "ok", resp.Header().Get(HeaderHealthStatus))

	var response map[string]interface{}
	err := json.Unmarshal(resp.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)

	assert.Equal(suite.T(), "ok", response["status"])

	appInfo := response["application"].(map[string]interface{})
	assert.Equal(suite.T(), "DNG Module", appInfo["name"])
	assert.Equal(suite.T(), "test", appInfo["environment"])
	assert.Equal(suite.T(), "1.0.0", appInfo["version"])
	assert.Equal(suite.T(), "2023-01-01T00:00:00Z", appInfo["build_time"])

	systemInfo := response["system"].(map[string]interface{})
	assert.NotEmpty(suite.T(), systemInfo["hostname"])
	assert.NotEmpty(suite.T(), systemInfo["go_version"])
	assert.NotEmpty(suite.T(), systemInfo["os"])
	assert.NotEmpty(suite.T(), systemInfo["arch"])
	assert.NotZero(suite.T(), systemInfo["num_cpu"])
	assert.NotZero(suite.T(), systemInfo["num_goroutine"])

	_, err = time.Parse(time.RFC3339, response["timestamp"].(string))
	assert.NoError(suite.T(), err)
}

func (suite *HealthHandlerTestSuite) TestHealthReadyHandler_GET_DatabaseConnected() {
	// Setup mock DB expectation
	suite.mock.ExpectExec("SELECT 1").WillReturnResult(sqlmock.NewResult(1, 1))

	suite.router.GET("/health/ready", HealthReadyHandler)

	req, _ := http.NewRequest("GET", "/health/ready", nil)
	resp := httptest.NewRecorder()

	// Execute
	suite.router.ServeHTTP(resp, req)

	// Verify
	assert.Equal(suite.T(), http.StatusOK, resp.Code)
	assert.Equal(suite.T(), "ok", resp.Header().Get(HeaderHealthStatus))
	assert.Equal(suite.T(), "true", resp.Header().Get(HeaderDatabaseConnected))

	var response map[string]interface{}
	err := json.Unmarshal(resp.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)

	assert.Equal(suite.T(), "ok", response["status"])

	components := response["components"].(map[string]interface{})
	database := components["database"].(map[string]interface{})
	assert.Equal(suite.T(), "ok", database["status"])
}

func (suite *HealthHandlerTestSuite) TestHealthReadyHandler_GET_DatabaseError() {
	// Setup mock DB to return error
	suite.mock.ExpectExec("SELECT 1").WillReturnError(errors.New("connection failed"))

	suite.router.GET("/health/ready", HealthReadyHandler)

	req, _ := http.NewRequest("GET", "/health/ready", nil)
	resp := httptest.NewRecorder()

	// Execute
	suite.router.ServeHTTP(resp, req)

	// Verify
	assert.Equal(suite.T(), http.StatusOK, resp.Code)
	assert.Equal(suite.T(), "degraded", resp.Header().Get(HeaderHealthStatus))
	assert.Equal(suite.T(), "false", resp.Header().Get(HeaderDatabaseConnected))

	var response map[string]interface{}
	err := json.Unmarshal(resp.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)

	assert.Equal(suite.T(), "degraded", response["status"])

	components := response["components"].(map[string]interface{})
	database := components["database"].(map[string]interface{})
	assert.Equal(suite.T(), "down", database["status"])
	assert.Equal(suite.T(), "connection failed", database["error"])
}

func (suite *HealthHandlerTestSuite) TestHealthReadyHandler_HEAD_DatabaseConnected() {
	// Setup mock DB expectation
	suite.mock.ExpectExec("SELECT 1").WillReturnResult(sqlmock.NewResult(1, 1))

	suite.router.HEAD("/health/ready", HealthReadyHandler)

	req, _ := http.NewRequest("HEAD", "/health/ready", nil)
	resp := httptest.NewRecorder()

	// Execute
	suite.router.ServeHTTP(resp, req)

	// Verify
	assert.Equal(suite.T(), http.StatusOK, resp.Code)
	assert.Equal(suite.T(), "ok", resp.Header().Get(HeaderHealthStatus))
	assert.Equal(suite.T(), "true", resp.Header().Get(HeaderDatabaseConnected))
	assert.Equal(suite.T(), 0, resp.Body.Len())
}

func (suite *HealthHandlerTestSuite) TestHealthReadyHandler_HEAD_DatabaseError() {
	// Setup mock DB to return error
	suite.mock.ExpectExec("SELECT 1").WillReturnError(errors.New("connection failed"))

	suite.router.HEAD("/health/ready", HealthReadyHandler)

	req, _ := http.NewRequest("HEAD", "/health/ready", nil)
	resp := httptest.NewRecorder()

	// Execute
	suite.router.ServeHTTP(resp, req)

	// Verify
	assert.Equal(suite.T(), http.StatusOK, resp.Code)
	assert.Equal(suite.T(), "degraded", resp.Header().Get(HeaderHealthStatus))
	assert.Equal(suite.T(), "false", resp.Header().Get(HeaderDatabaseConnected))
	assert.Equal(suite.T(), 0, resp.Body.Len())
}

func (suite *HealthHandlerTestSuite) TestIsDatabaseConnected_Success() {
	// Setup mock DB expectation
	suite.mock.ExpectExec("SELECT 1").WillReturnResult(sqlmock.NewResult(1, 1))

	// Execute
	connected, err := IsDatabaseConnected()

	// Verify
	assert.True(suite.T(), connected)
	assert.NoError(suite.T(), err)
}

func (suite *HealthHandlerTestSuite) TestIsDatabaseConnected_Error() {
	// Setup mock DB to return error
	suite.mock.ExpectExec("SELECT 1").WillReturnError(errors.New("connection failed"))

	// Execute
	connected, err := IsDatabaseConnected()

	// Verify
	assert.False(suite.T(), connected)
	assert.Error(suite.T(), err)
	assert.Equal(suite.T(), "connection failed", err.Error())
}
