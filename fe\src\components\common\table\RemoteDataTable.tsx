'use client'

import { defaultMRTOptions } from '@/components/common/table/tableOptions'
import { Box } from '@mui/material'
import {
    MaterialReactTable,
    MRT_ColumnFiltersState,
    MRT_Row,
    MRT_RowSelectionState,
    MRT_SortingState,
    MRT_TableInstance,
    useMaterialReactTable,
    type MRT_ColumnDef,
} from 'material-react-table'
import { useEffect, useState, type Dispatch, type SetStateAction } from 'react'

type RemoteDataTableProps<T extends Record<string, any>> = {
    columns: MRT_ColumnDef<T>[]
    fetchData: (
        pageIndex: number,
        pageSize: number,
        globalFilter: string,
        columnFilters: MRT_ColumnFiltersState,
        sorting: MRT_SortingState,
    ) => Promise<{ data: T[]; rowCount: number }>
    title?: string
    enableRowSelection?: boolean
    getRowId?: (originalRow: any, index: number, parentRow: MRT_Row<any>) => string
    renderActions?: (selectedRows: T[], table: MRT_TableInstance<T>) => React.ReactNode
    renderTopToolbarFilters?: (table: MRT_TableInstance<T>) => React.ReactNode
    rowSelection: Record<string, boolean>
    onRowSelectionChange?: Dispatch<SetStateAction<MRT_RowSelectionState>>
    forceRefresh?: boolean // Prop to trigger refresh
}

export default function RemoteDataTable<T extends Record<string, any>>({
    columns,
    fetchData,
    title,
    enableRowSelection = false,
    getRowId,
    renderActions,
    renderTopToolbarFilters,
    rowSelection,
    onRowSelectionChange,
    forceRefresh = false,
}: Readonly<RemoteDataTableProps<T>>) {
    const [data, setData] = useState<T[]>([])
    const [rowCount, setRowCount] = useState(0)
    const [globalFilter, setGlobalFilter] = useState('')
    const [columnFilters, setColumnFilters] = useState<MRT_ColumnFiltersState>([])
    const [sorting, setSorting] = useState<MRT_SortingState>([])
    const [loading, setLoading] = useState(false)
    const [pagination, setPagination] = useState({
        pageIndex: 0,
        pageSize: 10,
    })

    useEffect(() => {
        const loadData = async () => {
            setLoading(true)
            const result = await fetchData(
                pagination.pageIndex,
                pagination.pageSize,
                globalFilter,
                columnFilters,
                sorting,
            )
            setData(result.data)
            setRowCount(result.rowCount)
            setLoading(false)
        }

        loadData()
    }, [fetchData, pagination.pageIndex, pagination.pageSize, globalFilter, columnFilters, sorting, forceRefresh]) // Re-fetch data when forceRefresh is true

    const table = useMaterialReactTable({
        columns,
        data,
        manualPagination: true,
        manualFiltering: true,
        manualSorting: true,
        rowCount,
        getRowId: getRowId ?? ((row) => String(row.id)),
        onPaginationChange: setPagination,
        onGlobalFilterChange: setGlobalFilter,
        onColumnFiltersChange: setColumnFilters,
        onSortingChange: setSorting,
        onRowSelectionChange: onRowSelectionChange,
        muiSelectCheckboxProps: {
            color: 'secondary',
        },
        muiSelectAllCheckboxProps: {
            color: 'secondary',
        },
        state: {
            isLoading: loading,
            pagination,
            globalFilter,
            columnFilters,
            sorting,
            showProgressBars: loading,
            rowSelection,
        },
        ...defaultMRTOptions,
        /*muiTableContainerProps: {
            sx: {
                overflowX: 'auto',
            },
        },
        muiTablePaperProps: {
            sx: {
                width: '100%',
                overflow: 'hidden',
            },
        },*/
        enableRowSelection,
        renderTopToolbar: ({ table }) => {
            const typedTable = table as MRT_TableInstance<T>
            const selectedRows = typedTable.getSelectedRowModel().flatRows.map((row) => row.original)

            return (
                <Box
                    sx={{
                        display: 'flex',
                        gap: '0.5rem',
                        p: '8px',
                        justifyContent: 'space-between',
                    }}
                >
                    <Box sx={{ display: 'flex', gap: '0.5rem', alignItems: 'center' }}>
                        {renderTopToolbarFilters?.(typedTable)}
                    </Box>
                    <Box sx={{ display: 'flex', gap: '0.5rem', alignItems: 'center' }}>
                        {renderActions?.(selectedRows, typedTable)}
                    </Box>
                </Box>
            )
        },
    })

    return (
        <Box sx={{ width: '100%' }}>
            <MaterialReactTable table={table} />
        </Box>
    )
}
