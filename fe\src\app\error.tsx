'use client'

import { Box, Button, Typography } from '@mui/material'
import React from 'react'

interface ErrorProps {
    error: Error
    reset: () => void
}

const ErrorPage: React.FC<ErrorProps> = ({ error, reset }) => {
    return (
        <Box
            display="flex"
            flexDirection="column"
            alignItems="center"
            justifyContent="center"
            padding="2rem"
            textAlign="center"
            mt={20}
        >
            <Typography variant="h4" gutterBottom>
                Something went wrong!
            </Typography>
            <Typography variant="body1" color="textSecondary" gutterBottom>
                {error.message}
            </Typography>
            <Box display="flex" gap={3} alignItems="center">
                <Button
                    variant="outlined"
                    color="info"
                    onClick={() => (window.location.href = '/')}
                    sx={{ marginTop: '1rem' }}
                >
                    Go to Home
                </Button>
                <Button variant="contained" color="primary" onClick={reset} sx={{ marginTop: '1rem' }}>
                    Try Again
                </Button>
            </Box>
        </Box>
    )
}

export default ErrorPage
