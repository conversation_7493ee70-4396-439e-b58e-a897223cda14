import { authOptions } from '@/utils/authOptions'
import axios from 'axios'

jest.mock('@/app/api/auth/[...nextauth]/services', () => ({
    upsertUser: jest.fn().mockResolvedValue({ data: {} }),
}))

const mockAccount = {
    providerAccountId: 'mock-provider-account-id',
    provider: 'azure-ad',
    type: 'oauth' as const,
    access_token: 'access-token',
    refresh_token: 'refresh-token',
    expires_at: Math.floor(Date.now() / 1000) + 3600,
}
const mockProfile = { name: 'Test User', email: '<EMAIL>' }
const mockUser = {
    id: '1',
    name: 'Test User',
    email: '<EMAIL>',
    user_id: '1',
    role_id: 1,
    role_name: 'admin',
    priority: 1,
    roles: [{ role_id: '1', role: 'admin' }],
}
const mockToken = {}

// Silence console logs during tests
beforeAll(() => {
    jest.spyOn(console, 'log').mockImplementation(() => {})
    jest.spyOn(console, 'error').mockImplementation(() => {})
    jest.spyOn(console, 'warn').mockImplementation(() => {})
})

afterAll(() => {
    ;(console.log as jest.Mock).mockRestore()
    ;(console.error as jest.Mock).mockRestore()
    ;(console.warn as jest.Mock).mockRestore()
})

// Mock axios.get to always return a valid group structure
;(axios.get as jest.Mock).mockResolvedValue({
    data: {
        value: [{ displayName: 'gcp-iam-bus-da-super-admin' }],
    },
})

describe('authOptions.jwt callback', () => {
    it('should set tokens on initial sign in', async () => {
        const token = await authOptions.callbacks!.jwt!({
            token: {},
            user: mockUser,
            account: mockAccount,
            profile: mockProfile,
        })
        expect(token.access_token).toBe('access-token')
        expect(token.refresh_token).toBe('refresh-token')
        expect(token.expires_at).toBeGreaterThan(Date.now())
    })

    it('should return token if not expired', async () => {
        const now = Date.now()
        const token = {
            access_token: 'access-token',
            refresh_token: 'refresh-token',
            expires_at: now + 3600 * 1000,
        }
        const result = await authOptions.callbacks!.jwt!({
            token,
            user: mockUser,
            account: null, // Explicitly pass account as null
        })
        expect(result).toBe(token)
    })

    xit('should refresh token if expired', async () => {
        // Mock fetch to simulate a successful refresh response
        global.fetch = jest.fn().mockResolvedValue({
            ok: true,
            json: async () => ({
                access_token: 'new-access-token',
                refresh_token: 'new-refresh-token',
                expires_in: 3600,
            }),
        }) as any

        const expiredToken = {
            access_token: 'old-access-token',
            refresh_token: 'refresh-token',
            expires_at: Date.now() - 1000,
        }
        const expiredAccount = {
            providerAccountId: 'mock-provider-account-id',
            provider: 'azure-ad',
            type: 'oauth' as const,
            ...expiredToken,
        }
        const result = await authOptions.callbacks!.jwt!({
            token: expiredToken,
            user: mockUser,
            account: expiredAccount,
        })
        expect(result.access_token).toBe('new-access-token')
        expect(result.refresh_token).toBe('new-refresh-token')
        expect(result.expires_at).toBeGreaterThan(Date.now())
    })

    it('should set error if refresh fails', async () => {
        global.fetch = jest.fn().mockResolvedValue({
            ok: false,
            status: 400,
            json: async () => ({
                error: 'invalid_grant',
                error_description: 'Refresh token is invalid or expired',
            }),
        }) as any

        const expiredToken = {
            access_token: 'old-access-token',
            refresh_token: 'refresh-token',
            expires_at: Date.now() - 1000,
        }
        const expiredAccount = {
            providerAccountId: 'mock-provider-account-id',
            provider: 'azure-ad',
            type: 'oauth' as const,
            ...expiredToken,
        }
        const result = await authOptions.callbacks!.jwt!({
            token: expiredToken,
            user: mockUser,
            account: expiredAccount,
        })
        expect(result.error).toBe('RefreshAccessTokenError')
    })
})
