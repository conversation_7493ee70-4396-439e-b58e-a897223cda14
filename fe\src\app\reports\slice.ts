import { Filter<PERSON><PERSON> } from '@/app/reports/reportSearch/types'
import { createSlice, PayloadAction } from '@reduxjs/toolkit'

interface ReportsState {
    filters: {
        search: string
    }
    activeFilters: {
        sourceSystem: string
        domain: string
        category: string
        dates: [Date | null, Date | null]
    }
    loading: 'idle' | 'pending' | 'succeeded' | 'failed'
    error: string | null
    filtersOpen: boolean
    refreshFlag: boolean
}

// Initial state based on your service params
const initialState: ReportsState = {
    filters: {
        search: '',
    },
    activeFilters: {
        sourceSystem: '-',
        domain: '-',
        category: '-',
        dates: [null, null],
    },
    loading: 'idle',
    error: null,
    filtersOpen: false,
    refreshFlag: false,
}

// Create the reports slice
const reportsSlice = createSlice({
    name: 'reports',
    initialState,
    reducers: {
        setSearchText: (state, action: PayloadAction<string>) => {
            state.filters.search = action.payload
        },
        setActiveFilters: (state, action: PayloadAction<Record<FilterKey, string>>) => {
            state.activeFilters = { ...state.activeFilters, ...action.payload }
        },
        resetActiveFilters: (state) => {
            state.activeFilters = initialState.activeFilters
        },
        toggleFiltersPanel: (state) => {
            state.filtersOpen = !state.filtersOpen
        },
        setRefreshFlag: (state, action: PayloadAction<boolean>) => {
            state.refreshFlag = action.payload
        },
    },
})

export const { setSearchText, setActiveFilters, resetActiveFilters, toggleFiltersPanel, setRefreshFlag } =
    reportsSlice.actions

export default reportsSlice.reducer
