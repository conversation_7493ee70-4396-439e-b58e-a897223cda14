'use client'

import React from 'react'
import { getErrorImagePath } from '@/utils/errorHandler'
import HeaderWithErrorImage from '@/components/common/errors/HeaderWithErrorImage'

// Define the error types and their corresponding status codes
const errorTypes: Record<string, number> = {
  'server': 500,
  'not-found': 404,
  'unauthorized': 401,
  'forbidden': 403,
  'bad-request': 400,
  'api': 503
}

export default function ErrorPage({ params }: Readonly<{ params: { type: string } }>) {
  // Get the error type from the URL parameter
  const { type } = params
  
  // Get the status code for this error type (default to 500 if not found)
  const statusCode = errorTypes[type] || 500
  
  // Get the appropriate error image path
  const imagePath = getErrorImagePath(statusCode)
  
  return (
    <HeaderWithErrorImage 
      imagePath={imagePath}
      showHeader={true}
      showFooterSpace={true}
    />
  )
}
