apiVersion: v1
kind: Service
metadata:
  labels: 
    cattle.io/creator: {{GITLAB_USER_LOGIN}}
  annotations:
   field.cattle.io/ipAddresses: "null"
   field.cattle.io/targetDnsRecordIds: "null"
   field.cattle.io/targetWorkloadIds: '["deployment:{{KUBE_NAMESPACE}}:{{CI_PROJECT_NAME}}"]'
  name: {{KUBE_SERVICE_NAME}}
  namespace: {{KUBE_NAMESPACE}}
spec:
  selector:
    dg.app: {{CI_PROJECT_NAME}}
    dg.environment: {{CI_ENVIRONMENT_SLUG}}
  ports: 
  - name: "{{DOMAIN}}-{{PORT_MAPPING}}"
    port: {{PORT_MAPPING}}
    protocol: TCP
    targetPort: {{PORT_MAPPING}}
  sessionAffinity: None
  type: ClusterIP
