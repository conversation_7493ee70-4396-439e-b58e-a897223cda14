package catalogReportService

import (
	"dng-module/internal/model"
	"dng-module/internal/utils"
	"sort"
	"strings"
	"time"

	"gorm.io/gorm"
)

func GetReports(db *gorm.DB, params model.GetReportsQueryParams, userID int, _ string) (model.GetReportsResponse, error) {
	utils.Logger.Info("Starting GetReports ", "userID", userID)

	// Step 1: Get accessible domain IDs based on user_id and can_view=true
	var accessibleDomainIDs []int
	err := db.Table("user_domain_access AS uda").
		Select("uda.domain_id").
		Joins("JOIN roles r ON r.id = uda.role_id").
		Where("uda.user_id = ? AND r.can_view = ?", userID, true).
		Pluck("uda.domain_id", &accessibleDomainIDs).Error
	if err != nil {
		utils.Logger.Error("Failed to fetch user domain access ", "error", err)
		return model.GetReportsResponse{}, err
	}

	if len(accessibleDomainIDs) == 0 {
		utils.Logger.Warn("User has no view access to any domains ", "userID", userID)
		return model.GetReportsResponse{}, nil
	}

	// Step 2: Fetch user report preferences
	var preferences []model.UserReportPreference
	err = db.Where("user_id = ?", userID).Find(&preferences).Error
	if err != nil {
		utils.Logger.Error("Failed to fetch user report preferences ", "error", err)
		return model.GetReportsResponse{}, err
	}

	// Step 3: Fetch reports for preferences
	for i := range preferences {
		err = db.Where("id = ?", preferences[i].ReportID).First(&preferences[i].Report).Error
		if err != nil {
			utils.Logger.Error("Failed to fetch report for preference ", "error ", err, "reportID ", preferences[i].ReportID)
			return model.GetReportsResponse{}, err
		}
	}

	// Step 4: Fetch domains and reports (with filters)
	var domains []model.Domain
	if len(params.Domain) > 0 {
		err = db.Preload("Report").Where("id IN ? AND id IN ?", accessibleDomainIDs, params.Domain).Find(&domains).Error
	} else {
		err = db.Preload("Report").Where("id IN ?", accessibleDomainIDs).Find(&domains).Error
	}
	if err != nil {
		utils.Logger.Error("Failed to fetch domains ", "error", err)
		return model.GetReportsResponse{}, err
	}

	// --- Filtering and Sorting Setup ---
	searchQuery := strings.ToLower(params.Search)
	viewFilter := strings.ToLower(params.View)
	if viewFilter == "" {
		viewFilter = "all"
	}
	prefMap := make(map[int]model.UserReportPreference)
	for _, pref := range preferences {
		prefMap[pref.Report.ID] = pref
	}
	sortColumn := "created_at"
	switch params.Sort {
	case "name", "category", "source_system":
		sortColumn = params.Sort
	}
	sortOrder := "asc"
	if params.Order == "desc" {
		sortOrder = "desc"
	}
	page := params.Page
	if page < 0 {
		page = 0
	}
	size := params.Size
	if size < 1 {
		size = 5
	}

	// --- Filtering Reports ---
	var allFilteredReports []struct {
		Report            model.ReportWithPreference
		DomainID          int
		DomainName        string
		DomainDescription string
	}

	for _, dom := range domains {
		// Sort reports in this domain
		sort.SliceStable(dom.Report, func(i, j int) bool {
			a, b := dom.Report[i], dom.Report[j]
			switch sortColumn {
			case "name":
				if sortOrder == "desc" {
					return a.Name > b.Name
				}
				return a.Name < b.Name
			case "category":
				if sortOrder == "desc" {
					return a.Category > b.Category
				}
				return a.Category < b.Category
			case "source_system":
				if sortOrder == "desc" {
					return a.SourceSystem > b.SourceSystem
				}
				return a.SourceSystem < b.SourceSystem
			case "created_at":
				if sortOrder == "desc" {
					return a.CreatedAt.After(b.CreatedAt)
				}
				return a.CreatedAt.Before(b.CreatedAt)
			default:
				return true
			}
		})

		// Apply filters
		for _, rep := range dom.Report {
			if len(params.Category) > 0 && !utils.Contains(params.Category, rep.Category) {
				continue
			}
			if len(params.SourceSystem) > 0 && !utils.Contains(params.SourceSystem, rep.SourceSystem) {
				continue
			}
			if params.StartDate != "" || params.EndDate != "" {
				if rep.PublishedOn == nil {
					continue
				}
				if params.StartDate != "" {
					if startDate, err := time.Parse("2006-01-02", params.StartDate); err == nil {
						if rep.PublishedOn.Before(startDate) {
							continue
						}
					}
				}
				if params.EndDate != "" {
					if endDate, err := time.Parse("2006-01-02", params.EndDate); err == nil {
						if rep.PublishedOn.After(endDate) {
							continue
						}
					}
				}
			}
			rwp := model.ReportWithPreference{
				ID:           rep.ID,
				Name:         rep.Name,
				ReportID:     rep.ReportID,
				Description:  rep.Description,
				Category:     rep.Category,
				DomainID:     rep.DomainID,
				SourceSystem: rep.SourceSystem,
				GroupID:      rep.GroupID,
				CreatedBy:    rep.CreatedBy,
				CreatedAt:    rep.CreatedAt.String(),
				UpdatedAt:    rep.UpdatedAt.String(),
				PublishedOn:  rep.PublishedOn,
				ReportUrl:    rep.ReportUrl,
			}
			if pref, ok := prefMap[rep.ID]; ok {
				rwp.IsFavorite = pref.IsFavorite
				rwp.IsSaved = pref.IsSaved
			}
			if searchQuery != "" &&
				!strings.Contains(strings.ToLower(rep.Name), searchQuery) &&
				!strings.Contains(strings.ToLower(rep.Description), searchQuery) &&
				!strings.Contains(strings.ToLower(rep.Category), searchQuery) {
				continue
			}
			if viewFilter == "favorites" && !rwp.IsFavorite {
				continue
			}
			if viewFilter == "saved-later" && !rwp.IsSaved {
				continue
			}
			allFilteredReports = append(allFilteredReports, struct {
				Report            model.ReportWithPreference
				DomainID          int
				DomainName        string
				DomainDescription string
			}{
				Report:            rwp,
				DomainID:          dom.ID,
				DomainName:        dom.Name,
				DomainDescription: dom.Description,
			})
		}
	}

	// --- Pagination ---
	totalReports := len(allFilteredReports)
	offset := page * size
	end := offset + size
	if offset > totalReports {
		offset = totalReports
	}
	if end > totalReports {
		end = totalReports
	}
	paginatedReports := allFilteredReports[offset:end]

	// --- Group by domain ---
	domainMap := make(map[int]model.DomainWithReportsResponse)
	for _, item := range paginatedReports {
		d, exists := domainMap[item.DomainID]
		if !exists {
			d = model.DomainWithReportsResponse{
				ID:          item.DomainID,
				Name:        item.DomainName,
				Description: item.DomainDescription,
			}
		}
		d.Report = append(d.Report, item.Report)
		domainMap[item.DomainID] = d
	}

	var domainResponses []model.DomainWithReportsResponse
	for _, d := range domainMap {
		domainResponses = append(domainResponses, d)
	}

	resp := model.GetReportsResponse{
		Domain: domainResponses,
		Meta: model.Meta{
			Total:      totalReports,
			Page:       page,
			Limit:      size,
			TotalPages: (totalReports + size - 1) / size,
		},
	}

	utils.Logger.Info("GetReports completed ", " userID ", userID, " totalReports ", totalReports)
	return resp, nil
}
