import { fireEvent, render, screen } from '@testing-library/react'
import AlertsCarousel from '@/app/(landing)/components/AlertsCarousel'
import { IAlert } from '@/app/alerts/types'

const mockAlerts: IAlert[] = [
    {
        id: 1,
        type: 'news',
        title: 'News 1',
        description: 'Short news description.',
        start_date_time: new Date(),
        end_date_time: null,
        is_visible: true,
        status: 'active',
        created_by: 'Alice',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
    },
    {
        id: 2,
        type: 'news',
        title: 'News 2',
        description: 'Another news description.',
        start_date_time: new Date(),
        end_date_time: null,
        is_visible: true,
        status: 'active',
        created_by: 'Bob',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
    },
    {
        id: 3,
        type: 'event',
        title: 'Event 1',
        description: 'Event description.',
        start_date_time: new Date(),
        end_date_time: null,
        is_visible: true,
        status: 'scheduled',
        created_by: 'Eve',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
    },
    {
        id: 4,
        type: 'alert',
        title: 'Urgent Alert',
        description: 'This is an important alert message.',
        start_date_time: new Date(),
        end_date_time: null,
        is_visible: true,
        status: 'active',
        created_by: 'Admin',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
    },
]

describe('AlertsCarousel Component', () => {
    it('renders News and Events sections', () => {
        render(<AlertsCarousel alerts={mockAlerts} />)

        expect(screen.getByText('News')).toBeInTheDocument()
        expect(screen.getByText('Upcoming Events')).toBeInTheDocument()
    })

    it('displays the first news item by default', () => {
        render(<AlertsCarousel alerts={mockAlerts} />)
        expect(screen.getByText('News 1')).toBeInTheDocument()
    })

    it('cycles to next news item on clicking forward button', () => {
        render(<AlertsCarousel alerts={mockAlerts} />)

        const forwardButton = screen.getAllByRole('button').find((btn) => btn.innerHTML.includes('ArrowForwardIos'))

        fireEvent.click(forwardButton!)
        expect(screen.getByText('News 2')).toBeInTheDocument()
    })

    it('displays the event title', () => {
        render(<AlertsCarousel alerts={mockAlerts} />)

        expect(screen.getByText('Event 1')).toBeInTheDocument()
        expect(screen.getByText(/Upcoming Events/)).toBeInTheDocument()
    })

    it('expands and collapses long description if needed', () => {
        const longAlert: IAlert = {
            ...mockAlerts[0],
            description: 'This is a very long description '.repeat(10), // > 100 chars
        }

        render(<AlertsCarousel alerts={[longAlert]} />)

        const readMoreButton = screen.getByText('Read more')
        expect(readMoreButton).toBeInTheDocument()

        fireEvent.click(readMoreButton)
        expect(screen.getByText('Show less')).toBeInTheDocument()
    })
})
