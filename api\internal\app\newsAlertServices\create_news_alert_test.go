package newsAlertService

import (
	"dng-module/config"
	// newsAlertService "dng-module/internal/app/newsAlertServices"
	"dng-module/internal/model"
	"dng-module/testing/utilsTest"
	"errors"
	"regexp"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
)

func TestCreateNewsAlert(t *testing.T) {
	// Setup mock DB
	db, mock := utilsTest.SetupMockDB(t)
	config.DB = db
	defer utilsTest.CloseMockDB(db)

	now := time.Now().UTC()
	past := now.Add(-1 * time.Hour)
	future := now.Add(1 * time.Hour)

	tests := []struct {
		name        string
		alert       model.NewsAlert
		mockClosure func()
		expected    *model.NewsAlert
		expectError bool
	}{
		{
			name: "success - scheduled alert",
			alert: model.NewsAlert{
				Title:         "Scheduled Alert",
				StartDateTime: future,
				EndDateTime:   future.Add(2 * time.Hour),
			},
			mockClosure: func() {
				mock.ExpectBegin()
				mock.ExpectQuery(regexp.QuoteMeta(
					`INSERT INTO "news_alerts"`)).
					WithArgs(
						"",                      // type (empty string as default)
						"Scheduled Alert",       // title
						"",                      // description
						future,                  // start_date_time
						future.Add(2*time.Hour), // end_date_time
						false,                   // is_visible (default false)
						"scheduled",             // status
						"",                      // created_by (empty string as default)
						sqlmock.AnyArg(),        // created_at
						sqlmock.AnyArg(),        // updated_at
					).
					WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))
				mock.ExpectCommit()
			},
			expected: &model.NewsAlert{
				ID:            1,
				Title:         "Scheduled Alert",
				Status:        "scheduled",
				StartDateTime: future,
				EndDateTime:   future.Add(2 * time.Hour),
				CreatedAt:     now,
				UpdatedAt:     now,
			},
			expectError: false,
		},
		{
			name: "success - active alert",
			alert: model.NewsAlert{
				Title:         "Active Alert",
				StartDateTime: past,
				EndDateTime:   future,
			},
			mockClosure: func() {
				mock.ExpectBegin()
				mock.ExpectQuery(regexp.QuoteMeta(
					`INSERT INTO "news_alerts"`)).
					WithArgs(
						"",               // type
						"Active Alert",   // title
						"",               // description
						past,             // start_date_time
						future,           // end_date_time
						false,            // is_visible
						"active",         // status
						"",               // created_by
						sqlmock.AnyArg(), // created_at
						sqlmock.AnyArg(), // updated_at
					).
					WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(2))
				mock.ExpectCommit()
			},
			expected: &model.NewsAlert{
				ID:            2,
				Title:         "Active Alert",
				Status:        "active",
				StartDateTime: past,
				EndDateTime:   future,
				CreatedAt:     now,
				UpdatedAt:     now,
			},
			expectError: false,
		},
		{
			name: "error - database failure",
			alert: model.NewsAlert{
				Title:         "Failing Alert",
				StartDateTime: past,
				EndDateTime:   future,
			},
			mockClosure: func() {
				mock.ExpectBegin()
				mock.ExpectQuery(regexp.QuoteMeta(
					`INSERT INTO "news_alerts"`)).
					WithArgs(
						"",               // type
						"Failing Alert",  // title
						"",               // description
						past,             // start_date_time
						future,           // end_date_time
						false,            // is_visible
						"active",         // status
						"",               // created_by
						sqlmock.AnyArg(), // created_at
						sqlmock.AnyArg(), // updated_at
					).
					WillReturnError(errors.New("database error"))
				mock.ExpectRollback()
			},
			expected:    nil,
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup mock expectations
			tt.mockClosure()

			// Execute the function
			result, err := CreateNewsAlert(tt.alert)

			// Assertions
			if tt.expectError {
				assert.Error(t, err)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)

				// Compare fields individually to handle time comparisons
				assert.Equal(t, tt.expected.ID, result.ID)
				assert.Equal(t, tt.expected.Title, result.Title)
				assert.Equal(t, tt.expected.Status, result.Status)
				assert.Equal(t, tt.expected.StartDateTime, result.StartDateTime)
				assert.Equal(t, tt.expected.EndDateTime, result.EndDateTime)

				// Check timestamps are set and close to now
				assert.WithinDuration(t, now, result.CreatedAt, time.Second)
				assert.WithinDuration(t, now, result.UpdatedAt, time.Second)
			}

			// Ensure all expectations were met
			assert.NoError(t, mock.ExpectationsWereMet())
		})
	}
}
