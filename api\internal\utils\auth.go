// middleware/jwt.go
package utils

import (
	"fmt"
	"io"
	"net/http"
	"strings"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v4"
)

var (
	// Cache for validated tokens to reduce API calls
	tokenCache     = make(map[string]time.Time)
	tokenCacheLock sync.RWMutex
	cacheExpiry    = 5 * time.Minute // How long to consider a cached token valid
)

// JWTMiddleware validates Azure AD tokens using Microsoft Graph API
func JWTMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get the Authorization header
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			handleUnauthorizedError(c, "Missing authorization token")
			return
		}

		// Expected format: "Bearer <token>"
		tokenParts := strings.Split(authHeader, " ")
		if len(tokenParts) != 2 || tokenParts[0] != "Bearer" {
			handleUnauthorizedError(c, "Invalid authorization header format")
			return
		}

		tokenString := tokenParts[1]

		// Check if token is valid (either from cache or by validating with Microsoft)
		isValid := validateToken(tokenString)
		if !isValid {
			handleUnauthorizedError(c, "Invalid or expired token")
			return
		}

		// Parse token without verification to extract claims
		token, _, err := new(jwt.Parser).ParseUnverified(tokenString, jwt.MapClaims{})
		if err != nil {
			handleUnauthorizedError(c, "Invalid token format")
			return
		}

		// Extract claims
		claims, ok := token.Claims.(jwt.MapClaims)
		if !ok {
			handleUnauthorizedError(c, "Invalid token claims")
			return
		}

		// Store claims in the context for later use
		c.Set("claims", claims)

		// Continue to the next handler
		c.Next()
	}
}

// validateToken checks if the token is valid, either from cache or by calling Microsoft Graph API
func validateToken(token string) bool {
	// Check cache first for better performance
	tokenCacheLock.RLock()
	cacheTime, exists := tokenCache[token]
	tokenCacheLock.RUnlock()

	// If token exists in cache and hasn't expired
	if exists && time.Since(cacheTime) < cacheExpiry {
		return true
	}

	// Token not in cache or expired, validate with Microsoft
	isValid := validateTokenWithGraphAPI(token)
	if isValid {
		// Add to cache if valid
		tokenCacheLock.Lock()
		tokenCache[token] = time.Now()

		// Clean up expired tokens from cache (periodically)
		if len(tokenCache) > 1000 { // Arbitrary limit to prevent memory issues
			cleanupExpiredTokens()
		}
		tokenCacheLock.Unlock()
	}

	return isValid
}

// validateTokenWithGraphAPI uses Microsoft Graph API to validate the token
func validateTokenWithGraphAPI(token string) bool {
	// Microsoft Graph API endpoint that requires valid authentication
	graphURL := "https://graph.microsoft.com/v1.0/me"

	// Create HTTP client with timeout
	client := &http.Client{Timeout: 5 * time.Second}
	req, err := http.NewRequest(http.MethodGet, graphURL, nil)
	if err != nil {
		return false
	}

	// Add token to Authorization header
	req.Header.Add("Authorization", fmt.Sprintf("Bearer %s", token))

	// Send request
	resp, err := client.Do(req)
	if err != nil {
		return false
	}
	defer resp.Body.Close()

	// Safely read the response body
	_, err = io.ReadAll(resp.Body)
	if err != nil {
		return false
	}

	// If we get a 200 OK, the token is valid
	return resp.StatusCode == http.StatusOK
}

// cleanupExpiredTokens removes expired tokens from the cache
func cleanupExpiredTokens() {
	now := time.Now()
	for token, timestamp := range tokenCache {
		if now.Sub(timestamp) > cacheExpiry {
			delete(tokenCache, token)
		}
	}
}

// handleUnauthorizedError sends a generic unauthorized error response
func handleUnauthorizedError(c *gin.Context, message string) {
	c.JSON(http.StatusUnauthorized, gin.H{"error": "Your access token is invalid or expired"})
	c.Abort()
}
