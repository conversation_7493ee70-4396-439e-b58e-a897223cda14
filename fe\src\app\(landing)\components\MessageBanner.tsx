'use client'

import { useState } from 'react'
import { Box, Button, Collapse, IconButton, Paper, Typography } from '@mui/material'
import { ArrowBackIos, ArrowForwardIos, Close, InfoOutlined } from '@mui/icons-material'
import { IAlert } from '@/app/alerts/types'

interface MessageBannerProps {
    alerts: IAlert[]
}

export default function MessageBanner({ alerts }: Readonly<MessageBannerProps>) {
    const messages = alerts?.length ? alerts.filter((alert) => alert.type === 'alert') : []

    const [index, setIndex] = useState(0)
    const [expanded, setExpanded] = useState(false)
    const [isVisible, setIsVisible] = useState(true)

    const current = messages[index]
    const isLong = current?.description.length > 100

    if (messages.length === 0 || !isVisible) return null

    return (
        <Paper
            elevation={0}
            sx={{
                background: 'linear-gradient(to right, #fff8dc, #fff176)', // white to soft yellow
                color: '#333', // dark text for contrast
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                px: 1,
                py: 1.5,
                position: 'relative',
            }}
        >
            {/* Info Icon */}
            <IconButton size="small" onClick={() => {}} sx={{ position: 'absolute', left: 4, top: 6 }}>
                <InfoOutlined sx={{ fontSize: 18 }} />
            </IconButton>

            {/* Left Arrow */}
            {messages.length > 1 && (
                <IconButton
                    size="small"
                    onClick={() => {
                        setExpanded(false)
                        setIndex(index === 0 ? messages.length - 1 : index - 1)
                    }}
                    sx={{ mr: 2 }}
                >
                    <ArrowBackIos sx={{ fontSize: 16 }} />
                </IconButton>
            )}

            {/* Message Content */}
            <Box sx={{ flex: 1, ml: messages.length > 1 ? 0 : 5, minHeight: '55px' }}>
                <Typography fontWeight="bold">{current?.title}</Typography>

                {/* Display the message description with collapsible behavior if it's long */}
                <Collapse in={expanded || !isLong} collapsedSize={20}>
                    <Typography variant="body2">{current?.description}</Typography>
                </Collapse>

                {/* Show the "Read more" button only if the description is long */}
                {isLong && (
                    <Button
                        onClick={() => setExpanded((e) => !e)}
                        color="info"
                        variant="contained"
                        size="small"
                        sx={{ mt: 1, fontSize: 10, p: 0.5 }}
                    >
                        {expanded ? 'Show less' : 'Read more'}
                    </Button>
                )}
            </Box>

            {/* Right Arrow */}
            {messages.length > 1 && (
                <IconButton
                    size="small"
                    onClick={() => {
                        setExpanded(false)
                        setIndex(index === messages.length - 1 ? 0 : index + 1)
                    }}
                    sx={{ ml: 2 }}
                >
                    <ArrowForwardIos sx={{ fontSize: 16 }} />
                </IconButton>
            )}

            {/* Close Button */}
            <IconButton
                size="small"
                onClick={() => setIsVisible(false)}
                sx={{ position: 'absolute', right: 10, top: 5 }}
            >
                <Close sx={{ fontSize: 18 }} />
            </IconButton>
        </Paper>
    )
}
