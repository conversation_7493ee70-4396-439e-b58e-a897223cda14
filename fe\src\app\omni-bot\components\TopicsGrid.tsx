import { Close as CloseIcon } from '@mui/icons-material'
import { Box, Button, Fade, IconButton, Modal, Paper, Typography } from '@mui/material'
import React, { useState } from 'react'
import { Topic } from '../types'
import TopicsCarousel from './TopicsCarousel'

interface TopicsGridProps {
    topics: Topic[]
    onReadClick: (topicId: number) => void
}

const TopicsGrid: React.FC<TopicsGridProps> = ({ topics, onReadClick }) => {
    const [openTopicId, setOpenTopicId] = useState<number | null>(null)

    if (!Array.isArray(topics) || topics.length === 0) {
        return (
            <Box sx={{ width: '100%', textAlign: 'center', py: 4 }}>
                <Typography variant="body1" color="text.secondary">
                    No topics available.
                </Typography>
            </Box>
        )
    }

    const getTopicById = (id: number | null) => {
        if (!id) return null
        return topics.find((topic) => topic.id === id) || null
    }

    const getTopicImageUrl = (topicId: number) => {
        if (topicId === 1) {
            return 'url(/images/omni-bot/chart.png)'
        } else {
            const index = topicId - 2
            const extension = index === 0 ? '.png' : '.jpg'
            return `url(/images/omni-bot/topic-${index + 1}${extension})`
        }
    }

    const openTopic = getTopicById(openTopicId)

    return (
        <Box
            className="topics-grid"
            sx={{
                width: 'calc(100% - 10px)', // Subtract scrollbar width
                height: '100%',
                overflow: 'auto',
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                bgcolor: '#f9f9f9',
                flexGrow: 1,
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                pr: 2, // Add padding to account for scrollbar
                '&::-webkit-scrollbar': {
                    width: '8px',
                },
                '&::-webkit-scrollbar-track': {
                    background: '#f1f1f1',
                    borderRadius: '10px',
                },
                '&::-webkit-scrollbar-thumb': {
                    background: '#FFC10780',
                    borderRadius: '10px',
                },
                '&::-webkit-scrollbar-thumb:hover': {
                    background: '#FFC107',
                },
            }}
        >
            <Box
                sx={{
                    width: '100%',
                    maxWidth: { xs: '100%', sm: '100%', md: '100%', lg: '1400px', xl: '1600px' },
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    gap: '24px',
                    pt: { xs: 3, md: 4 },
                    pb: { xs: 5, md: 6 },
                    px: { xs: 2, md: 4 },
                }}
            >
                <Box
                    sx={{
                        mb: 0,
                        textAlign: 'center',
                        position: 'relative',
                        '&::after': {
                            content: '""',
                            position: 'absolute',
                            bottom: -8,
                            left: '50%',
                            transform: 'translateX(-50%)',
                            width: '80px',
                            height: '4px',
                            backgroundColor: '#FFC107',
                            borderRadius: '2px',
                        },
                    }}
                >
                    <Typography
                        variant="h4"
                        component="h1"
                        gutterBottom
                        sx={{
                            fontWeight: 'bold',
                            background: 'linear-gradient(90deg, #000000 0%, #333333 100%)',
                            WebkitBackgroundClip: 'text',
                            WebkitTextFillColor: 'transparent',
                            display: 'inline-block',
                        }}
                    >
                        Topics For You
                    </Typography>
                    <Typography variant="subtitle1" color="text.secondary" sx={{ mb: 1 }}>
                        Explore these curated topics to enhance your knowledge
                    </Typography>
                </Box>

                {/* Topics Carousel */}
                <TopicsCarousel
                    topics={topics}
                    onReadClick={(topicId) => setOpenTopicId(topicId)}
                    onTopicClick={(topicId) => setOpenTopicId(topicId)}
                />
            </Box>

            {/* Topic Detail Modal */}
            <Modal
                open={openTopic !== null}
                onClose={() => setOpenTopicId(null)}
                closeAfterTransition
                sx={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    p: 2,
                }}
            >
                <Fade in={openTopic !== null}>
                    <Paper
                        sx={{
                            width: '100%',
                            maxWidth: '800px',
                            maxHeight: '90vh',
                            overflowY: 'auto',
                            p: 4,
                            borderRadius: 3,
                            position: 'relative',
                            boxShadow: '0 10px 40px rgba(0,0,0,0.2)',
                        }}
                    >
                        {openTopic && (
                            <>
                                <Box sx={{ position: 'absolute', top: 16, right: 16, zIndex: 10 }}>
                                    <IconButton
                                        onClick={() => setOpenTopicId(null)}
                                        sx={{
                                            bgcolor: 'rgba(0,0,0,0.05)',
                                            '&:hover': {
                                                bgcolor: 'rgba(0,0,0,0.1)',
                                            },
                                        }}
                                    >
                                        <CloseIcon />
                                    </IconButton>
                                </Box>

                                <Typography variant="h4" fontWeight="bold" gutterBottom>
                                    {openTopic.title}
                                </Typography>

                                <Box
                                    sx={{
                                        width: '100%',
                                        height: '300px',
                                        borderRadius: 2,
                                        backgroundImage: getTopicImageUrl(openTopic.id),
                                        backgroundSize: 'cover',
                                        backgroundPosition: 'center',
                                        mb: 3,
                                        boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
                                        position: 'relative',
                                        '&::before': {
                                            content: '""',
                                            position: 'absolute',
                                            top: 0,
                                            left: 0,
                                            width: '100%',
                                            height: '100%',
                                            background: 'linear-gradient(to top, rgba(0,0,0,0.7), rgba(0,0,0,0.3))',
                                            borderRadius: '8px',
                                        },
                                        display: 'flex',
                                        alignItems: 'flex-end',
                                        justifyContent: 'flex-start',
                                        padding: '20px',
                                    }}
                                >
                                    <Typography
                                        variant="h5"
                                        sx={{
                                            color: 'white',
                                            fontWeight: 'bold',
                                            textShadow: '0 2px 4px rgba(0,0,0,0.7)',
                                            zIndex: 1,
                                            position: 'relative'
                                        }}
                                    >
                                        Explore {openTopic.title}
                                    </Typography>
                                </Box>

                                <Typography variant="body1" sx={{ mb: 4, whiteSpace: 'pre-line', lineHeight: 1.8 }}>
                                    {openTopic.fullContent}
                                </Typography>

                                <Box
                                    sx={{
                                        display: 'flex',
                                        justifyContent: 'center',
                                        alignItems: 'center',
                                        mt: 4,
                                    }}
                                >
                                    <Button
                                        variant="contained"
                                        disableElevation={false}
                                        onClick={() => {
                                            setOpenTopicId(null)
                                            onReadClick(openTopic.id)
                                        }}
                                        sx={{
                                            backgroundColor: '#FFC107 !important',
                                            color: 'black',
                                            borderRadius: '20px',
                                            px: 4,
                                            py: 1,
                                            height: '44px',
                                            minWidth: '250px',
                                            width: { xs: '100%', sm: '80%', md: '60%' },
                                            fontWeight: 'bold',
                                            border: '2px solid #FFC107',
                                            boxShadow: '0 4px 12px rgba(255, 193, 7, 0.4)',
                                            '&:hover': {
                                                backgroundColor: '#FFB000 !important',
                                                transform: 'translateY(-2px)',
                                                boxShadow: '0 6px 15px rgba(255, 193, 7, 0.5)',
                                            },
                                            transition: 'all 0.2s ease',
                                        }}
                                    >
                                        Ask Omnibot About This Topic
                                    </Button>
                                </Box>
                            </>
                        )}
                    </Paper>
                </Fade>
            </Modal>
        </Box>
    )
}

export default TopicsGrid
