'use client'

import { Button, Dialog, DialogActions, DialogContent, DialogContentText, DialogTitle, IconButton } from '@mui/material'
import DeleteIcon from '@mui/icons-material/Delete'

interface DeleteConfirmDialogProps {
    open: boolean
    onClose: () => void
    onConfirm: () => void
    reportCount: number
    entity?: string
}

export default function DeleteConfirmDialog({
    open,
    onClose,
    onConfirm,
    reportCount,
    entity = 'report',
}: Readonly<DeleteConfirmDialogProps>) {
    return (
        <Dialog open={open} onClose={onClose} maxWidth="xs">
            <DialogTitle>
                <IconButton edge="start" color="error" size="small" sx={{ mr: 1 }}>
                    <DeleteIcon />
                </IconButton>
                Confirm Deletion
            </DialogTitle>
            <DialogContent>
                <DialogContentText>
                    Are you sure you want to delete {reportCount} {entity}? This action cannot be undone.
                </DialogContentText>
            </DialogContent>
            <DialogActions>
                <Button onClick={onClose} color="inherit" size="small">
                    Cancel
                </Button>
                <Button onClick={onConfirm} color="error" variant="contained" size="small">
                    Delete
                </Button>
            </DialogActions>
        </Dialog>
    )
}
