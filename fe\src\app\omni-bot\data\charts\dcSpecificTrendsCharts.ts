// DC-specific Trends Charts

import dcTrendDatasetsData from './dcSpecificTrendsChartsData.json'

export const MONTH_LABELS = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun']

// Use data from JSON file
const dcTrendDatasets = dcTrendDatasetsData

export const dcSpecificTrendsCharts = [
    {
        id: 'dc_audit_accuracy_trend',
        type: 'line',
        title: 'Audit Accuracy Trend by DC (6-Month)',
        data: {
            labels: MONTH_LABELS,
            datasets: dcTrendDatasets.map((ds) => ({
                ...ds,
                fill: true,
            })),
        },
    },
]
