import { Topic } from '../../types';

// Order & Tote Errors Topic (placeholder for future implementation)
export const orderToteErrorsTopic: Topic = {
  id: 4,
  title: 'Order & Tote Errors',
  description: 'Detailed analysis of order and tote-level errors with employee metrics',
  fullContent: `Dollar General's Order & Tote Errors dashboard provides a granular view of picking and packing errors at the order, tote, and item level. This dashboard enables supervisors and managers to identify specific error patterns, track individual employee performance, and implement targeted process improvements.

  The data includes detailed error logs by zone, error type breakdowns, quantity discrepancies, order and tote-level audit results, and employee-specific error metrics.

  By analyzing order and tote-level error data, managers can identify systemic issues, implement targeted training, optimize picking processes, and improve overall accuracy and efficiency.`,
  charts: [] // No charts defined yet
};

// Order & Tote Errors Questions and Answers
export const ORDER_TOTE_ERRORS_QA = [
  {
    keywords: ["under-pick", "errors", "yesterday", "zone b"],
    answer: 'Yesterday, we had 28 under-pick errors in Zone B, which is 40% higher than our daily average for this zone. The breakdown of these errors is as follows:\n\n- Health & Beauty items: 11 errors (39.3%)\n- Seasonal merchandise: 8 errors (28.6%)\n- Food items: 5 errors (17.9%)\n- General merchandise: 4 errors (14.2%)\n\nThe primary cause was a new employee (<PERSON>, R.) who accounted for 9 of these errors. We\'ve scheduled additional training for this employee today. The second contributing factor was a system issue with the RF scanners in Zone B between 2:00 PM and 3:30 PM, which resulted in 7 errors. IT has resolved this issue. The remaining errors follow the normal distribution pattern for this zone.'
  },
  {
    keywords: ["audit", "result", "order", "12345"],
    answer: 'The audit result for order #12345 shows the following:\n\n**Order Details:**\n- Date: June 15, 2023\n- Store: #8742 (Memphis, TN)\n- Picker: Johnson, M.\n- Audit Date: June 15, 2023\n- Auditor: Wilson, P.\n\n**Audit Results:**\n- Total Items: 47\n- Correctly Picked Items: 45\n- Errors: 2\n- Accuracy Rate: 95.7%\n\n**Error Details:**\n1. Item #DG-7842 (Shampoo, 12oz): Quantity error - Ordered 3, Picked 2\n2. Item #DG-9156 (Seasonal Candle): Wrong item - Picked vanilla scent instead of cinnamon\n\n**Corrective Actions:**\n- Errors corrected before shipping\n- Feedback provided to picker\n- No systemic issues identified'
  },
  {
    keywords: ["invalid picks", "employee", "smith", "last week"],
    answer: 'Employee Smith, R. had 23 invalid picks last week, which is significantly above our threshold of 15 per week. Here\'s the breakdown:\n\n**Daily Distribution:**\n- Monday: 3 invalid picks\n- Tuesday: 4 invalid picks\n- Wednesday: 7 invalid picks\n- Thursday: 5 invalid picks\n- Friday: 4 invalid picks\n\n**Error Types:**\n- Wrong item picked: 11 (47.8%)\n- Quantity errors: 8 (34.8%)\n- Location errors: 4 (17.4%)\n\n**Contributing Factors:**\n- Smith is a new employee (2 weeks on the job)\n- Primarily working in high-complexity zones (B and C)\n- Highest error rate during afternoon shift (2:00 PM - 4:00 PM)\n\n**Corrective Actions:**\n- Additional training scheduled for next week\n- Temporarily reassigned to lower-complexity zone\n- Paired with mentor (Johnson, M.) for the next 5 shifts\n- Daily performance review implemented'
  },
  {
    keywords: ["error", "details", "tote", "rt789"],
    answer: 'Tote #RT789 was processed on June 14, 2023, and had the following error details:\n\n**Tote Information:**\n- Processing Date: June 14, 2023\n- Zone: B (Health & Beauty)\n- Picker: Garcia, L.\n- Destination: Store #5421 (Nashville, TN)\n\n**Error Summary:**\n- Total Items in Tote: 18\n- Items with Errors: 4\n- Error Rate: 22.2%\n\n**Detailed Errors:**\n1. Item #DG-3342 (Toothpaste): Quantity error - Required 4, Picked 2\n2. Item #DG-5567 (Shampoo): Wrong item - Picked conditioner instead\n3. Item #DG-7891 (Deodorant): Missing completely - Required 2, Picked 0\n4. Item #DG-2234 (Soap): Quantity error - Required 3, Picked 5\n\n**Root Causes:**\n- Similar packaging for items #DG-5567 and the conditioner\n- Location confusion for items #DG-7891 (recently relocated)\n- Scanning issues for quantity errors (scanner battery low during picking)\n\n**Corrective Actions:**\n- Errors corrected during audit\n- Picker retrained on scanning procedures\n- Location signage improved for recently relocated items'
  },
  {
    keywords: ["items", "quantity discrepancies", "greater than", "5 units"],
    answer: 'In the past 30 days, we\'ve identified 14 items with quantity discrepancies greater than 5 units. Here are the details:\n\n**High-Volume Items:**\n1. Item #DG-1122 (AA Batteries, 4-pack): 12 units over-picked\n2. Item #DG-3456 (Paper Towels, 6-roll): 9 units under-picked\n3. Item #DG-7890 (Bottled Water, 24-pack): 8 units under-picked\n4. Item #DG-2345 (Toilet Paper, 12-roll): 7 units under-picked\n5. Item #DG-5678 (Hand Sanitizer, 8oz): 7 units over-picked\n\n**Seasonal Items:**\n6. Item #DG-9012 (Sunscreen, 8oz): 11 units over-picked\n7. Item #DG-3457 (Beach Towels): 8 units over-picked\n8. Item #DG-6789 (Insect Repellent): 6 units under-picked\n\n**Promotional Items:**\n9. Item #DG-1123 (Laundry Detergent, 100oz): 10 units under-picked\n10. Item #DG-4567 (Dish Soap, 16oz): 9 units over-picked\n11. Item #DG-7891 (Cereal, Family Size): 7 units under-picked\n12. Item #DG-2346 (Soda, 12-pack): 6 units over-picked\n13. Item #DG-5679 (Chips, Family Size): 6 units under-picked\n14. Item #DG-9013 (Candy Bars): 6 units over-picked\n\n**Root Causes:**\n- Case vs. unit confusion (especially for items #DG-1122, #DG-7890)\n- Similar packaging leading to mis-picks\n- Promotional display locations causing confusion\n- Scanning issues with high-volume items\n\n**Corrective Actions:**\n- Updated picking instructions to clarify case vs. unit requirements\n- Enhanced scanner prompts for high-discrepancy items\n- Additional training for pickers handling promotional items\n- Improved signage for items with similar packaging'
  }
];
