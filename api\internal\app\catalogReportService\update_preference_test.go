package catalogReportService

import (
	"dng-module/testing/utilsTest"
	"errors"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"
)

func TestUpdatePreference_CreateFavorite(t *testing.T) {
	db, mock := utilsTest.SetupMockDB(t)
	defer utilsTest.CloseMockDB(db)

	userID, reportID := 1, 101

	// 1. Expect SELECT query first (outside transaction)
	mock.ExpectQuery(`SELECT \* FROM "user_report_preference" WHERE user_id = \$1 AND report_id = \$2 ORDER BY "user_report_preference"\."id" LIMIT \$3`).
		WithArgs(userID, reportID, 1).
		WillReturnError(gorm.ErrRecordNotFound)

	// 2. Then expect transaction Begin
	mock.ExpectBegin()

	// 3. Expect INSERT query inside transaction
	mock.ExpectQuery(`INSERT INTO "user_report_preference"`).
		WithArgs(userID, reportID, true, false, sqlmock.AnyArg(), sqlmock.AnyArg()).
		WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))

	// 4. Expect Commit
	mock.ExpectCommit()

	result, err := UpdatePreference(db, userID, reportID, true, false, "favorites")

	assert.NoError(t, err)
	assert.Equal(t, userID, result.UserID)
	assert.Equal(t, reportID, result.ReportID)
	assert.True(t, result.IsFavorite)
	assert.False(t, result.IsSaved)
}
func TestUpdatePreference_UpdateSaved(t *testing.T) {
	db, mock := utilsTest.SetupMockDB(t)
	defer utilsTest.CloseMockDB(db)

	userID, reportID := 2, 202

	mock.ExpectQuery(`SELECT \* FROM "user_report_preference" WHERE user_id = \$1 AND report_id = \$2 ORDER BY "user_report_preference"\."id" LIMIT \$3`).
		WithArgs(userID, reportID, 1).
		WillReturnRows(sqlmock.NewRows([]string{"user_id", "report_id", "is_favorite", "is_saved"}).
			AddRow(userID, reportID, false, true))

	mock.ExpectBegin()
	mock.ExpectExec(`UPDATE "user_report_preference" SET "is_saved"`).
		WithArgs(true, sqlmock.AnyArg(), userID, reportID).
		WillReturnResult(sqlmock.NewResult(0, 1))
	mock.ExpectCommit()

	result, err := UpdatePreference(db, userID, reportID, false, true, "saved-later")

	assert.NoError(t, err)
	assert.Equal(t, userID, result.UserID)
	assert.Equal(t, reportID, result.ReportID)
	assert.True(t, result.IsSaved)
}

func TestUpdatePreference_DeleteIfEmpty(t *testing.T) {
	db, mock := utilsTest.SetupMockDB(t)
	defer utilsTest.CloseMockDB(db)

	userID, reportID := 3, 303
	id := 123 // Primary key of the record

	mock.ExpectQuery(`SELECT \* FROM "user_report_preference" WHERE user_id = \$1 AND report_id = \$2 ORDER BY "user_report_preference"\."id" LIMIT \$3`).
		WithArgs(userID, reportID, 1).
		WillReturnRows(sqlmock.NewRows([]string{"id", "user_id", "report_id", "is_favorite", "is_saved"}).
			AddRow(id, userID, reportID, true, false))

	mock.ExpectBegin()

	mock.ExpectExec(`DELETE FROM "user_report_preference" WHERE "user_report_preference"\."id" = \$1`).
		WithArgs(id).
		WillReturnResult(sqlmock.NewResult(0, 1))

	mock.ExpectCommit()

	result, err := UpdatePreference(db, userID, reportID, false, false, "favorites")

	assert.NoError(t, err)
	assert.Equal(t, userID, result.UserID)
	assert.Equal(t, reportID, result.ReportID)
}

func TestUpdatePreference_DBError(t *testing.T) {
	db, mock := utilsTest.SetupMockDB(t)
	defer utilsTest.CloseMockDB(db)

	userID, reportID := 4, 404

	// DB fetch error
	mock.ExpectQuery(`SELECT \* FROM "user_report_preferences" WHERE user_id = \$1 AND report_id = \$2 ORDER BY "user_report_preferences"\."user_id" LIMIT \$3`).
		WithArgs(userID, reportID, 1).
		WillReturnError(errors.New("db failure"))

	_, err := UpdatePreference(db, userID, reportID, true, false, "favorites")

	assert.Error(t, err)
	assert.Contains(t, err.Error(), "failed to fetch user report preference")
}

func TestUpdatePreference_RecordNotFound_FavoriteFalse(t *testing.T) {
	db, mock := utilsTest.SetupMockDB(t)
	defer utilsTest.CloseMockDB(db)

	userID, reportID := 5, 505

	mock.ExpectQuery(`SELECT \* FROM "user_report_preference" WHERE user_id = \$1 AND report_id = \$2 ORDER BY "user_report_preference"\."id" LIMIT \$3`).
		WithArgs(userID, reportID, 1).
		WillReturnError(gorm.ErrRecordNotFound)

	result, err := UpdatePreference(db, userID, reportID, false, false, "favorites")

	assert.NoError(t, err)
	assert.Equal(t, 0, result.UserID) // returned empty struct
}

func TestUpdatePreference_RecordNotFound_SavedFalse(t *testing.T) {
	db, mock := utilsTest.SetupMockDB(t)
	defer utilsTest.CloseMockDB(db)

	userID, reportID := 6, 606

	mock.ExpectQuery(`SELECT \* FROM "user_report_preference" WHERE user_id = \$1 AND report_id = \$2 ORDER BY "user_report_preference"\."id" LIMIT \$3`).
		WithArgs(userID, reportID, 1).
		WillReturnError(gorm.ErrRecordNotFound)

	result, err := UpdatePreference(db, userID, reportID, false, false, "saved-later")

	assert.NoError(t, err)
	assert.Equal(t, 0, result.UserID)
}

func TestUpdatePreference_CreateSavedLater(t *testing.T) {
	db, mock := utilsTest.SetupMockDB(t)
	defer utilsTest.CloseMockDB(db)

	userID, reportID := 7, 707

	mock.ExpectQuery(`SELECT \* FROM "user_report_preference" WHERE user_id = \$1 AND report_id = \$2 ORDER BY "user_report_preference"\."id" LIMIT \$3`).
		WithArgs(userID, reportID, 1).
		WillReturnError(gorm.ErrRecordNotFound)

	mock.ExpectBegin()
	mock.ExpectQuery(`INSERT INTO "user_report_preference"`).
		WithArgs(userID, reportID, false, true, sqlmock.AnyArg(), sqlmock.AnyArg()).
		WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(2))
	mock.ExpectCommit()

	result, err := UpdatePreference(db, userID, reportID, false, true, "saved-later")

	assert.NoError(t, err)
	assert.Equal(t, userID, result.UserID)
	assert.True(t, result.IsSaved)
}
func TestUpdatePreference_UpdateFavorite(t *testing.T) {
	db, mock := utilsTest.SetupMockDB(t)
	defer utilsTest.CloseMockDB(db)

	userID, reportID := 8, 808

	// Step 1: SELECT existing preference
	mock.ExpectQuery(`SELECT \* FROM "user_report_preference" WHERE user_id = \$1 AND report_id = \$2 ORDER BY "user_report_preference"\."id" LIMIT \$3`).
		WithArgs(userID, reportID, 1).
		WillReturnRows(sqlmock.NewRows([]string{"id", "user_id", "report_id", "is_favorite", "is_saved"}).
			AddRow(1, userID, reportID, true, false))

	// Step 2: Begin transaction (for DELETE)
	mock.ExpectBegin()

	// Step 3: DELETE by ID
	mock.ExpectExec(`DELETE FROM "user_report_preference" WHERE "user_report_preference"\."id" = \$1`).
		WithArgs(1).
		WillReturnResult(sqlmock.NewResult(0, 1))

	// Step 4: Commit transaction (for DELETE)
	mock.ExpectCommit()

	// Step 5: Call the function
	result, err := UpdatePreference(db, userID, reportID, false, false, "favorites")

	assert.NoError(t, err)
	assert.True(t, result.IsFavorite)

	// Optional: check all expectations met
	if err := mock.ExpectationsWereMet(); err != nil {
		t.Errorf("there were unfulfilled expectations: %s", err)
	}
}
