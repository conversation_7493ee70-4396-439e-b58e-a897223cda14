'use client'
import React from 'react'
import { Box, Container, styled } from '@mui/material'
import Header from '@/components/common/layout/header/header'

const MainWrapper = styled('div')(() => ({
    display: 'flex',
    height: '100vh',
    width: '100%',
    overflow: 'hidden',
}))

const PageWrapper = styled('div')(({ theme }) => ({
    display: 'flex',
    flexGrow: 1,
    flexDirection: 'column',
    zIndex: 1,
    backgroundColor: 'white',
    width: '100%',
    overflow: 'hidden',
}))

interface Props {
    children: React.ReactNode
}

export default function OmnibotLayout({ children }: Readonly<Props>) {
    return (
        <MainWrapper className="omnibot-layout">
            <PageWrapper>
                <Header
                    toggleMobileSidebar={() => {}}
                    showLogo={true}
                />
                <Container
                    sx={{
                        paddingTop: '20px',
                        maxWidth: { xs: '100%', sm: '100%', md: '100%', lg: '1400px', xl: '1800px' },
                        paddingLeft: { xs: '10px', sm: '15px' },
                        paddingRight: { xs: '10px', sm: '15px' },
                        width: '100%',
                    }}
                >
                    <Box sx={{
                        height: 'calc(100vh - 84px)',
                        width: '100%',
                        mt: '64px',
                        overflow: 'hidden',
                        position: 'relative'
                    }}>
                        {children}
                    </Box>
                </Container>
            </PageWrapper>
        </MainWrapper>
    )
}
