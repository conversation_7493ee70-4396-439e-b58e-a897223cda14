apiVersion: apps/v1
kind: Deployment
metadata:
 annotations: 
  app.gitlab.com/app: {{********************}}
  app.gitlab.com/env: {{CI_ENVIRONMENT_SLUG}}
 name: {{CI_PROJECT_NAME}}
 namespace: {{KUBE_NAMESPACE}}
spec:
  replicas: {{KUBE_POD_REPLICAS}}
  selector:
    matchLabels:
      dg.app: {{CI_PROJECT_NAME}}
      dg.environment: {{CI_ENVIRONMENT_SLUG}}
  template:
    metadata:
      labels:
        # Required, for using workload identity management in AKS
        #azure.workload.identity/use: "true"        
        cattle.io/creator: {{GITLAB_USER_LOGIN}}
        app.kubernetes.io/name: {{CI_PROJECT_NAME}}
        app.kubernetes.io/instance: {{CI_PROJECT_NAME}}
        app.kubernetes.io/version: "{{VER}}"
        app.kubernetes.io/component: {{APP_TIER}}
        app.kubernetes.io/part-of: {{DOMAIN_SOLUTION}}
        app.kubernetes.io/managed-by: {{DOMAIN}}
        dg.domain: {{DOMAIN}}
        dg.environment: {{CI_ENVIRONMENT_SLUG}}
        dg.app: {{CI_PROJECT_NAME}}
        dg.version: "{{VER}}"
        dg.app-tech-stack: {{APP_TECH_STACK}}
      annotations:
        # Dapr Annotations:  https://docs.dapr.io/reference/arguments-annotations-overview/
        app.gitlab.com/app: {{********************}}
        app.gitlab.com/env: {{CI_ENVIRONMENT_SLUG}}
        dapr.io/app-id: {{CI_PROJECT_NAME}}
        dapr.io/app-port: "{{PORT_MAPPING}}"
        dapr.io/enabled: "false"
        dapr.io/enable-app-health-check: "false"
        dapr.io/app-health-check-path: "/{{INGRESS_BASE_PATH}}/health/live"
        dapr.io/sidecar-cpu-request: {{KUBE_DAPR_REQUEST_CPU}}
        dapr.io/sidecar-memory-request: {{KUBE_DAPR_REQUEST_MEMORY}}
        # Threshold 10 gives 1.5 minutes to be Live, but still frequent checks to avoid forced wait times
        dapr.io/sidecar-liveness-probe-threshold: "10"
        dapr.io/sidecar-readiness-probe-threshold: "10"
        dapr.io/log-as-json: "true"
        #dapr.io/app-protocol: "grpc"
    spec:
      securityContext:
        runAsNonRoot: true
        runAsUser: 2000
        runAsGroup: 2000
        seccompProfile: 
          type: RuntimeDefault
      serviceAccountName: "{{KUBE_NAMESPACE}}-sa"
      automountServiceAccountToken: true
      containers:
      - env:
        - name: APP_ENVIRONMENT
          value: {{DOTNET_ENVIRONMENT}}
        - name: APP_ID
          value: {{CI_PROJECT_NAME}}
        - name: APP_PORT
          value: "{{PORT_MAPPING}}"
        image: {{DOCKER_IMAGE}}
        imagePullPolicy: Always
        name: {{CI_PROJECT_NAME}}
        securityContext:
          allowPrivilegeEscalation: false
          privileged: false
        ports: 
        - containerPort: {{PORT_MAPPING}}
          name: "{{DOMAIN}}-{{PORT_MAPPING}}"
          protocol: TCP
#        livenessProbe:
#          httpGet:
#            path: "/{{INGRESS_BASE_PATH}}/health/live"
#            port: {{PORT_MAPPING}}
            # Threshold 10 gives 1.5 minutes to be Live, but still frequent checks to avoid forced wait times
#          failureThreshold: 10 
        resources:
          requests:
            cpu: {{KUBE_APP_REQUEST_CPU}}
            memory: {{KUBE_APP_REQUEST_MEMORY}}
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      schedulerName: default-scheduler
---
# Solution level Service Account
apiVersion: v1
kind: ServiceAccount
metadata:
#  annotations:
# NOTE: THIS IS FOR GKE WORKLOAD IDENTITY IF NEEDED  
#    iam.gke.io/gcp-service-account: {{WORKLOAD_IDENTITY_SERVICE_ACCOUNT}}
# NOTE: THIS IS FOR AKS WORKLOAD IDENTITY IF NEEDED  
#    azure.workload.identity/client-id: {{WORKLOAD_IDENTITY_SERVICE_ACCOUNT}}
  name: "{{KUBE_NAMESPACE}}-sa"
  namespace: {{KUBE_NAMESPACE}}
---
# Secrets Reader for DAPR
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: "{{KUBE_NAMESPACE}}-secret-reader"
  namespace: {{KUBE_NAMESPACE}}
rules:
- apiGroups: [""]
  resources: ["secrets"]
  verbs: ["get", "list"]
---
kind: RoleBinding
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: "{{KUBE_NAMESPACE}}-secret-reader-binding"
  namespace: {{KUBE_NAMESPACE}}
subjects:
- kind: ServiceAccount
  name: "{{KUBE_NAMESPACE}}-sa"
- kind: ServiceAccount
  name: default
roleRef:
  kind: Role
  name: "{{KUBE_NAMESPACE}}-secret-reader"
  apiGroup: rbac.authorization.k8s.io
