package newsAlertService

import (
	"dng-module/config"
	// newsAlertService "dng-module/internal/app/newsAlertServices"
	"dng-module/internal/model"
	"dng-module/testing/utilsTest"
	"errors"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"
)

func TestUpdateNewsAlert(t *testing.T) {
	db, mock := utilsTest.SetupMockDB(t)
	// Replace your config.DB with the mock DB for testing
	config.DB = db

	existingAlert := model.NewsAlert{
		ID:            1,
		Type:          "Info",
		Title:         "Old Title",
		Description:   "Old Description",
		StartDateTime: time.Date(2025, 5, 20, 9, 0, 0, 0, time.UTC),
		EndDateTime:   time.Date(2025, 5, 21, 18, 0, 0, 0, time.UTC),
		IsVisible:     true,
		Status:        "Active",
		UpdatedAt:     time.Now().Add(-24 * time.Hour),
	}

	updatedDTO := model.UpdateNewsAlertDTO{
		ID:            1,
		Type:          "Warning",
		Title:         "New Title",
		Description:   "New Description",
		StartDateTime: time.Date(2025, 5, 22, 9, 0, 0, 0, time.UTC),
		EndDateTime:   time.Date(2025, 5, 23, 18, 0, 0, 0, time.UTC),
		IsVisible:     false,
	}

	t.Run("success update", func(t *testing.T) {
		// Mock the SELECT (First)
		rows := sqlmock.NewRows([]string{"id", "type", "title", "description", "start_date_time", "end_date_time", "is_visible", "status", "updated_at"}).
			AddRow(
				existingAlert.ID,
				existingAlert.Type,
				existingAlert.Title,
				existingAlert.Description,
				existingAlert.StartDateTime,
				existingAlert.EndDateTime,
				existingAlert.IsVisible,
				existingAlert.Status,
				existingAlert.UpdatedAt,
			)

		mock.ExpectQuery(`SELECT \* FROM "news_alerts" WHERE "news_alerts"."id" = \$1 ORDER BY "news_alerts"."id" LIMIT \$\d+`).
			WithArgs(existingAlert.ID, 1).
			WillReturnRows(rows)

		// Mock the Save (UPDATE)
		mock.ExpectBegin()
		mock.ExpectExec(`UPDATE "news_alerts" SET .* WHERE "id" = \$1`).
			WithArgs(
				updatedDTO.Type,          // $1
				updatedDTO.Title,         // $2
				updatedDTO.Description,   // $3
				updatedDTO.StartDateTime, // $4
				updatedDTO.EndDateTime,   // $5
				updatedDTO.IsVisible,     // $6
				sqlmock.AnyArg(),         // $7 status
				sqlmock.AnyArg(),         // $8 created_by
				sqlmock.AnyArg(),         // $9 created_at
				sqlmock.AnyArg(),         // $10 updated_at
				existingAlert.ID,         // $11 id
			).
			WillReturnResult(sqlmock.NewResult(1, 1))
		mock.ExpectCommit()

		alert, err := UpdateNewsAlert(updatedDTO)
		assert.NoError(t, err)
		assert.NotNil(t, alert)
		assert.Equal(t, updatedDTO.Title, alert.Title)
		assert.Equal(t, updatedDTO.Type, alert.Type)
	})

	t.Run("no changes detected", func(t *testing.T) {
		// The DTO matches existingAlert exactly (same fields)

		noChangeDTO := model.UpdateNewsAlertDTO{
			ID:            existingAlert.ID,
			Type:          existingAlert.Type,
			Title:         existingAlert.Title,
			Description:   existingAlert.Description,
			StartDateTime: existingAlert.StartDateTime,
			EndDateTime:   existingAlert.EndDateTime,
			IsVisible:     existingAlert.IsVisible,
		}

		// Mock the SELECT (First)
		rows := sqlmock.NewRows([]string{"id", "type", "title", "description", "start_date_time", "end_date_time", "is_visible", "status", "updated_at"}).
			AddRow(
				existingAlert.ID,
				existingAlert.Type,
				existingAlert.Title,
				existingAlert.Description,
				existingAlert.StartDateTime,
				existingAlert.EndDateTime,
				existingAlert.IsVisible,
				existingAlert.Status,
				existingAlert.UpdatedAt,
			)
		mock.ExpectQuery(`SELECT \* FROM "news_alerts" WHERE "news_alerts"."id" = \$1 ORDER BY "news_alerts"."id" LIMIT \$\d+`).
			WithArgs(existingAlert.ID, 1).
			WillReturnRows(rows)

		alert, err := UpdateNewsAlert(noChangeDTO)
		assert.Nil(t, alert)
		assert.EqualError(t, err, "no changes detected")
	})

	t.Run("record not found", func(t *testing.T) {
		mock.ExpectQuery(`SELECT \* FROM "news_alerts" WHERE "news_alerts"."id" = \$1 ORDER BY "news_alerts"."id" LIMIT \$\d+`).
			WithArgs(999, 1).
			WillReturnError(gorm.ErrRecordNotFound)

		alert, err := UpdateNewsAlert(model.UpdateNewsAlertDTO{ID: 999})
		assert.Nil(t, alert)
		assert.EqualError(t, err, "news alert not found")
	})

	t.Run("db error on select", func(t *testing.T) {
		mock.ExpectQuery(`SELECT \* FROM "news_alerts" WHERE "news_alerts"."id" = \$1 ORDER BY "news_alerts"."id" LIMIT \$\d+`).
			WithArgs(existingAlert.ID, 1).
			WillReturnError(errors.New("db error"))

		alert, err := UpdateNewsAlert(updatedDTO)
		assert.Nil(t, alert)
		assert.EqualError(t, err, "db error")
	})

	t.Run("db error on save", func(t *testing.T) {
		// Mock the SELECT (First)
		rows := sqlmock.NewRows([]string{"id", "type", "title", "description", "start_date_time", "end_date_time", "is_visible", "status", "updated_at"}).
			AddRow(
				existingAlert.ID,
				existingAlert.Type,
				existingAlert.Title,
				existingAlert.Description,
				existingAlert.StartDateTime,
				existingAlert.EndDateTime,
				existingAlert.IsVisible,
				existingAlert.Status,
				existingAlert.UpdatedAt,
			)
		mock.ExpectQuery(`SELECT \* FROM "news_alerts" WHERE "news_alerts"."id" = \$1 ORDER BY "news_alerts"."id" LIMIT \$\d+`).
			WithArgs(existingAlert.ID, 1).
			WillReturnRows(rows)

		// Mock the Save (UPDATE) failure
		mock.ExpectBegin()
		mock.ExpectExec(`UPDATE "news_alerts" SET .* WHERE "id" = \$1`).
			WithArgs(
				updatedDTO.Type,          // $1
				updatedDTO.Title,         // $2
				updatedDTO.Description,   // $3
				updatedDTO.StartDateTime, // $4
				updatedDTO.EndDateTime,   // $5
				updatedDTO.IsVisible,     // $6
				sqlmock.AnyArg(),         // $7 status
				sqlmock.AnyArg(),         // $8 created_by
				sqlmock.AnyArg(),         // $9 created_at
				sqlmock.AnyArg(),         // $10 updated_at
				existingAlert.ID,         // $11 id
			).
			WillReturnError(errors.New("update error"))
		mock.ExpectRollback()

		alert, err := UpdateNewsAlert(updatedDTO)
		assert.Nil(t, alert)
		assert.EqualError(t, err, "update error")
	})
}

func TestUpdateNewsAlert_IndividualFieldChanges(t *testing.T) {
	db, mock := utilsTest.SetupMockDB(t)
	config.DB = db

	existingAlert := model.NewsAlert{
		ID:            1,
		Type:          "Info",
		Title:         "Old Title",
		Description:   "Old Description",
		StartDateTime: time.Now().Add(-1 * time.Hour),
		EndDateTime:   time.Now().Add(1 * time.Hour),
		IsVisible:     true,
		Status:        "Active",
	}

	testCases := []struct {
		name        string
		updateField func(*model.UpdateNewsAlertDTO)
	}{
		{
			name: "change type only",
			updateField: func(dto *model.UpdateNewsAlertDTO) {
				dto.Type = "Warning"
			},
		},
		{
			name: "change title only",
			updateField: func(dto *model.UpdateNewsAlertDTO) {
				dto.Title = "New Title"
			},
		},
		{
			name: "change description only",
			updateField: func(dto *model.UpdateNewsAlertDTO) {
				dto.Description = "New Description"
			},
		},
		{
			name: "change visibility only",
			updateField: func(dto *model.UpdateNewsAlertDTO) {
				dto.IsVisible = false
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			updatedDTO := model.UpdateNewsAlertDTO{
				ID:            existingAlert.ID,
				Type:          existingAlert.Type,
				Title:         existingAlert.Title,
				Description:   existingAlert.Description,
				StartDateTime: existingAlert.StartDateTime,
				EndDateTime:   existingAlert.EndDateTime,
				IsVisible:     existingAlert.IsVisible,
			}

			// Apply the specific field change
			tc.updateField(&updatedDTO)

			// Mock the SELECT
			rows := sqlmock.NewRows([]string{"id", "type", "title", "description", "start_date_time", "end_date_time", "is_visible", "status"}).
				AddRow(
					existingAlert.ID,
					existingAlert.Type,
					existingAlert.Title,
					existingAlert.Description,
					existingAlert.StartDateTime,
					existingAlert.EndDateTime,
					existingAlert.IsVisible,
					existingAlert.Status,
				)

			mock.ExpectQuery(`SELECT \* FROM "news_alerts" WHERE .*`).
				WithArgs(existingAlert.ID, 1).
				WillReturnRows(rows)

			// Mock the Save
			mock.ExpectBegin()
			mock.ExpectExec(`UPDATE "news_alerts" SET .* WHERE "id" = \$1`).
				WithArgs(
					sqlmock.AnyArg(), // type
					sqlmock.AnyArg(), // title
					sqlmock.AnyArg(), // description
					sqlmock.AnyArg(), // start time
					sqlmock.AnyArg(), // end time
					sqlmock.AnyArg(), // is_visible
					sqlmock.AnyArg(), // status
					sqlmock.AnyArg(), // created_by
					sqlmock.AnyArg(), // created_at
					sqlmock.AnyArg(), // updated_at
					existingAlert.ID, // id
				).
				WillReturnResult(sqlmock.NewResult(1, 1))
			mock.ExpectCommit()

			alert, err := UpdateNewsAlert(updatedDTO)
			assert.NoError(t, err)
			assert.NotNil(t, alert)
		})
	}
}
func TestUpdateNewsAlert_TimeEdgeCases(t *testing.T) {
	db, mock := utilsTest.SetupMockDB(t)
	config.DB = db

	now := time.Now().UTC()
	testCases := []struct {
		name           string
		startTime      time.Time
		endTime        time.Time
		expectedStatus string
	}{
		{
			name:           "start time equals now",
			startTime:      now,
			endTime:        now.Add(1 * time.Hour),
			expectedStatus: "active",
		},
		{
			name:           "end time equals now",
			startTime:      now.Add(-1 * time.Hour),
			endTime:        now,
			expectedStatus: "inactive", // ← updated
		},
		{
			name:           "start and end equal now",
			startTime:      now,
			endTime:        now,
			expectedStatus: "inactive", // ← updated
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			existingAlert := model.NewsAlert{
				ID:            1,
				StartDateTime: time.Now().Add(-24 * time.Hour),
				EndDateTime:   time.Now().Add(-12 * time.Hour),
				Status:        "Expired",
			}

			updatedDTO := model.UpdateNewsAlertDTO{
				ID:            1,
				StartDateTime: tc.startTime,
				EndDateTime:   tc.endTime,
				Type:          "Info",
				Title:         "Title",
				Description:   "Description",
				IsVisible:     true,
			}

			// Mock the SELECT
			rows := sqlmock.NewRows([]string{"id", "type", "title", "description", "start_date_time", "end_date_time", "is_visible", "status"}).
				AddRow(
					existingAlert.ID,
					"Info",
					"Title",
					"Description",
					existingAlert.StartDateTime,
					existingAlert.EndDateTime,
					true,
					existingAlert.Status,
				)

			mock.ExpectQuery(`SELECT \* FROM "news_alerts" WHERE .*`).
				WithArgs(existingAlert.ID, 1).
				WillReturnRows(rows)

			// Mock the Save - verify status
			mock.ExpectBegin()
			mock.ExpectExec(`UPDATE "news_alerts" SET .* WHERE "id" = \$1`).
				WithArgs(
					sqlmock.AnyArg(),  // type
					sqlmock.AnyArg(),  // title
					sqlmock.AnyArg(),  // description
					tc.startTime,      // start time
					tc.endTime,        // end time
					sqlmock.AnyArg(),  // is_visible
					tc.expectedStatus, // status
					sqlmock.AnyArg(),  // created_by
					sqlmock.AnyArg(),  // created_at
					sqlmock.AnyArg(),  // updated_at
					existingAlert.ID,  // id
				).
				WillReturnResult(sqlmock.NewResult(1, 1))
			mock.ExpectCommit()

			alert, err := UpdateNewsAlert(updatedDTO)
			assert.NoError(t, err)
			assert.Equal(t, tc.expectedStatus, alert.Status)
		})
	}
}
