package catalogReportService

import (
	"dng-module/internal/model"
	"dng-module/testing/utilsTest"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"
)

func TestGetSearchOptions(t *testing.T) {
	db, mock := utilsTest.SetupMockDB(t)
	defer utilsTest.CloseMockDB(db)

	req := model.GetSearchOptionsRequest{
		UserID:   1,
		Query:    "Revenue",
		RoleName: "Admin,Viewer",
	}

	// Mock user fetch (regex adjusted to match GORM's actual query)
	mock.ExpectQuery(`SELECT (.+) FROM "users" WHERE id = \$1 ORDER BY "users"\."id" LIMIT \$2`).
		WithArgs(req.UserID, 1).
		WillReturnRows(sqlmock.NewRows([]string{"email"}).AddRow("<EMAIL>"))

	// Mock domain access (regex adjusted and IN clause handled correctly)
	mock.ExpectQuery(`SELECT uda\.domain_id FROM user_domain_access AS uda JOIN roles r ON r\.id = uda\.role_id WHERE \(uda\.user_id = \$1 AND r\.can_view = \$2\) AND r\.role_name IN \(\$3\)`).
		WithArgs(req.UserID, true, req.RoleName).
		WillReturnRows(sqlmock.NewRows([]string{"domain_id"}).AddRow(1))

	// Mock report fetch (adjusted regex to match LIKE usage)
	mock.ExpectQuery(`SELECT \* FROM "reports" WHERE domain_id IN \(\$1\) AND \(LOWER\(name\) ILIKE \$2 OR LOWER\(category\) ILIKE \$3 OR LOWER\(source_system\) ILIKE \$4\)`).
		WithArgs(1, "%revenue%", "%revenue%", "%revenue%").
		WillReturnRows(sqlmock.NewRows([]string{"id", "name", "category", "domain_id", "source_system"}).
			AddRow(101, "Revenue Report", "Finance", 1, "SAP"))

	// Mock domain preload
	mock.ExpectQuery(`SELECT \* FROM "domains" WHERE "domains"\."id" = \$1`).
		WithArgs(1).
		WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).AddRow(1, "Finance"))

	// Execute function
	result, err := GetSearchOptions(db, req)

	// Assert
	assert.NoError(t, err)
	assert.Len(t, result, 1)
	assert.Equal(t, "Revenue Report", result[0].Name)
	assert.Equal(t, "Finance", result[0].DomainName)
	assert.Equal(t, "SAP", result[0].SourceSystem)
}

func TestGetSearchOptions_UserNotFound(t *testing.T) {
	db, mock := utilsTest.SetupMockDB(t)
	defer utilsTest.CloseMockDB(db)

	req := model.GetSearchOptionsRequest{
		UserID:   2,
		Query:    "Finance",
		RoleName: "Admin",
	}

	// User fetch fails
	mock.ExpectQuery(`SELECT (.+) FROM "users" WHERE id = \$1 ORDER BY "users"\."id" LIMIT \$2`).
		WithArgs(req.UserID, 1).
		WillReturnError(gorm.ErrRecordNotFound)

	// Domain access
	mock.ExpectQuery(`SELECT uda\.domain_id FROM user_domain_access AS uda JOIN roles r ON r\.id = uda\.role_id WHERE \(uda\.user_id = \$1 AND r\.can_view = \$2\) AND r\.role_name IN \(\$3\)`).
		WithArgs(req.UserID, true, req.RoleName).
		WillReturnRows(sqlmock.NewRows([]string{"domain_id"}).AddRow(1))

	// Reports fetch
	mock.ExpectQuery(`SELECT \* FROM "reports" WHERE domain_id IN \(\$1\) AND \(LOWER\(name\) ILIKE \$2 OR LOWER\(category\) ILIKE \$3 OR LOWER\(source_system\) ILIKE \$4\)`).
		WithArgs(1, "%finance%", "%finance%", "%finance%").
		WillReturnRows(sqlmock.NewRows([]string{"id", "name", "category", "domain_id", "source_system"}).
			AddRow(201, "Finance Report", "Finance", 1, "Oracle"))

	// Domain preload
	mock.ExpectQuery(`SELECT \* FROM "domains" WHERE "domains"\."id" = \$1`).
		WithArgs(1).
		WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).AddRow(1, "Finance"))

	result, err := GetSearchOptions(db, req)

	assert.NoError(t, err)
	assert.Len(t, result, 1)
	assert.Equal(t, "Finance Report", result[0].Name)
}

func TestGetSearchOptions_DomainAccessError(t *testing.T) {
	db, mock := utilsTest.SetupMockDB(t)
	defer utilsTest.CloseMockDB(db)

	req := model.GetSearchOptionsRequest{
		UserID:   3,
		Query:    "HR",
		RoleName: "Viewer",
	}

	mock.ExpectQuery(`SELECT (.+) FROM "users" WHERE id = \$1 ORDER BY "users"\."id" LIMIT \$2`).
		WithArgs(req.UserID, 1).
		WillReturnRows(sqlmock.NewRows([]string{"email"}).AddRow("<EMAIL>"))

	mock.ExpectQuery(`SELECT uda\.domain_id FROM user_domain_access AS uda JOIN roles r ON r\.id = uda\.role_id WHERE \(uda\.user_id = \$1 AND r\.can_view = \$2\) AND r\.role_name IN \(\$3\)`).
		WithArgs(req.UserID, true, req.RoleName).
		WillReturnError(assert.AnError)

	result, err := GetSearchOptions(db, req)

	assert.Error(t, err)
	assert.Nil(t, result)
}

func TestGetSearchOptions_NoAccessibleDomains(t *testing.T) {
	db, mock := utilsTest.SetupMockDB(t)
	defer utilsTest.CloseMockDB(db)

	req := model.GetSearchOptionsRequest{
		UserID:   4,
		Query:    "HR",
		RoleName: "Viewer",
	}

	mock.ExpectQuery(`SELECT (.+) FROM "users" WHERE id = \$1 ORDER BY "users"\."id" LIMIT \$2`).
		WithArgs(req.UserID, 1).
		WillReturnRows(sqlmock.NewRows([]string{"email"}).AddRow("<EMAIL>"))

	mock.ExpectQuery(`SELECT uda\.domain_id FROM user_domain_access AS uda JOIN roles r ON r\.id = uda\.role_id WHERE \(uda\.user_id = \$1 AND r\.can_view = \$2\) AND r\.role_name IN \(\$3\)`).
		WithArgs(req.UserID, true, req.RoleName).
		WillReturnRows(sqlmock.NewRows([]string{"domain_id"})) // empty

	result, err := GetSearchOptions(db, req)

	assert.NoError(t, err)
	assert.Len(t, result, 0)
}

func TestGetSearchOptions_ReportFetchError(t *testing.T) {
	db, mock := utilsTest.SetupMockDB(t)
	defer utilsTest.CloseMockDB(db)

	req := model.GetSearchOptionsRequest{
		UserID:   5,
		Query:    "Ops",
		RoleName: "Admin",
	}

	mock.ExpectQuery(`SELECT (.+) FROM "users" WHERE id = \$1 ORDER BY "users"\."id" LIMIT \$2`).
		WithArgs(req.UserID, 1).
		WillReturnRows(sqlmock.NewRows([]string{"email"}).AddRow("<EMAIL>"))

	mock.ExpectQuery(`SELECT uda\.domain_id FROM user_domain_access AS uda JOIN roles r ON r\.id = uda\.role_id WHERE \(uda\.user_id = \$1 AND r\.can_view = \$2\) AND r\.role_name IN \(\$3\)`).
		WithArgs(req.UserID, true, req.RoleName).
		WillReturnRows(sqlmock.NewRows([]string{"domain_id"}).AddRow(3))

	mock.ExpectQuery(`SELECT \* FROM "reports" WHERE domain_id IN \(\$1\) AND \(LOWER\(name\) ILIKE \$2 OR LOWER\(category\) ILIKE \$3 OR LOWER\(source_system\) ILIKE \$4\)`).
		WithArgs(3, "%ops%", "%ops%", "%ops%").
		WillReturnError(assert.AnError)

	result, err := GetSearchOptions(db, req)

	assert.Error(t, err)
	assert.Nil(t, result)
}
