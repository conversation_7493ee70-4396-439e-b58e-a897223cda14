import React from 'react'
// MUI imports
import { List, ListItem, ListItemButton, ListItemIcon, ListItemText, styled, useTheme } from '@mui/material'
import Link from 'next/link'
import { usePathname, useSearchParams } from 'next/navigation'

type NavGroup = {
    [x: string]: any
    id?: string
    navlabel?: boolean
    subheader?: string
    title?: string
    icon?: any
    href?: any
    onClick?: React.MouseEvent<HTMLButtonElement, MouseEvent>
}

interface ItemType {
    item: NavGroup
    onClick: (event: React.MouseEvent<HTMLElement>) => void
    pathDirect: string
    isCollapsed: boolean // New prop for collapsed state
}

const NavItem = ({ item, pathDirect, onClick, isCollapsed }: ItemType) => {
    const Icon = item.icon
    const theme = useTheme()
    const itemIcon = <Icon stroke={1.5} size="1.3rem" />

    const searchParams = useSearchParams()
    // Extract pathname from current URL
    const currentPath = usePathname()
    const currentView = searchParams.get('view') ?? ''

    // Extract pathname & 'view' param from item.href
    const itemHref = item.href ?? ''
    const itemPath = itemHref.split('?')[0] // Extract only the path
    const itemSearchParams = new URLSearchParams(itemHref.split('?')[1] ?? '') // Parse query params safely
    const itemView = itemSearchParams.get('view') ?? '' // Get 'view' param

    // Compare only the path and 'view' param
    const isSelected = currentPath === itemPath && currentView === itemView

    const ListItemStyled = styled(ListItem)(() => ({
        padding: 0,
        '.MuiButtonBase-root': {
            whiteSpace: 'nowrap',
            marginBottom: '2px',
            padding: '4px 8px', // Reduced padding here
            borderRadius: '8px',
            //backgroundColor: level > 1 ? 'transparent !important' : 'inherit',
            color: theme.palette.text.primary,
            paddingLeft: '8px', // Adjust padding for alignment
            '&:hover': {
                backgroundColor: theme.palette.primary.light,
            },
            '&.Mui-selected': {
                backgroundColor: theme.palette.primary.main,
                '&:hover': {
                    backgroundColor: theme.palette.primary.main,
                },
            },
        },
    }))

    return (
        <List component="div" disablePadding key={item.id}>
            <ListItemStyled>
                <ListItemButton
                    component={Link}
                    href={item.href}
                    disabled={item.disabled}
                    selected={isSelected}
                    target={item.external ? '_blank' : ''}
                    onClick={onClick}
                    sx={{
                        justifyContent: isCollapsed ? 'center' : 'flex-start',
                        padding: '4px 8px', // Apply reduced padding here as well
                    }}
                >
                    <ListItemIcon
                        sx={{
                            minWidth: isCollapsed ? 'auto' : '36px',
                            p: '2px 0', // Adjust icon padding for better alignment
                            justifyContent: 'center',
                        }}
                    >
                        {itemIcon}
                    </ListItemIcon>
                    {!isCollapsed && (
                        <ListItemText
                            slotProps={{
                                primary: { fontWeight: 400 },
                            }}
                            primary={item.title}
                            sx={{
                                ml: 1, // Reduced spacing between icon and text
                                display: isCollapsed ? 'none' : 'block',
                            }}
                        />
                    )}
                </ListItemButton>
            </ListItemStyled>
        </List>
    )
}

export default NavItem
