'use client'
import { Box, Button, Typography } from '@mui/material'
import React from 'react'
import { ExplorationQuestion } from '../types'

interface ExplorationQuestionsProps {
    questions: ExplorationQuestion[]
    onQuestionClick: (question: string) => void
}

const ExplorationQuestions: React.FC<ExplorationQuestionsProps> = ({ questions, onQuestionClick }) => {
    return (
        <Box sx={{ mt: 2, mb: 3 }}>
            <Typography variant="h6" sx={{ mb: 1, fontWeight: 'medium', color: 'text.primary' }}>
                Explore these questions:
            </Typography>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                {questions.map((q, index) => (
                    <Button
                        key={crypto.randomUUID()}
                        variant="outlined"
                        color="primary"
                        size="medium"
                        onClick={() => onQuestionClick(q.question)}
                        sx={{
                            justifyContent: 'flex-start',
                            textAlign: 'left',
                            textTransform: 'none',
                            fontWeight: 'normal',
                            borderColor: 'rgba(255, 193, 7, 0.5)',
                            color: 'text.primary',
                            '&:hover': {
                                borderColor: '#FFC107',
                                backgroundColor: 'rgba(255, 193, 7, 0.08)',
                            },
                            padding: '8px 16px',
                            borderRadius: '8px',
                        }}
                    >
                        {q.question}
                    </Button>
                ))}
            </Box>
        </Box>
    )
}

export default ExplorationQuestions
