'use client'
import React from 'react'
import dynamic from 'next/dynamic'
import { Box, Typography, Paper } from '@mui/material'

// Dynamically import ApexCharts to avoid SSR issues
const Chart = dynamic(() => import('react-apexcharts'), { ssr: false })

const PromotionsChart: React.FC = () => {
    // Chart options
    const options = {
        chart: {
            id: 'promotions-effectiveness',
            toolbar: {
                show: false
            },
            fontFamily: 'Arial, sans-serif',
            stacked: true
        },
        colors: ['#FFC107', '#FF9800', '#FF5722', '#F44336'],
        xaxis: {
            categories: ['5%', '10%', '15%', '20%', '25%', '30%'],
            title: {
                text: 'Discount Percentage',
                style: {
                    color: '#616161',
                    fontSize: '12px',
                    fontFamily: 'Arial, sans-serif',
                }
            },
            labels: {
                style: {
                    colors: '#616161',
                    fontSize: '12px',
                    fontFamily: 'Arial, sans-serif',
                }
            }
        },
        yaxis: {
            title: {
                text: 'Revenue Impact',
                style: {
                    color: '#616161',
                    fontSize: '12px',
                    fontFamily: 'Arial, sans-serif',
                }
            },
            labels: {
                formatter: function(value: number) {
                    return '$' + value + 'k'
                },
                style: {
                    colors: '#616161',
                    fontSize: '12px',
                    fontFamily: 'Arial, sans-serif',
                }
            }
        },
        dataLabels: {
            enabled: false
        },
        legend: {
            position: 'top',
            horizontalAlign: 'right',
            fontSize: '12px',
            fontFamily: 'Arial, sans-serif',
            labels: {
                colors: '#616161'
            }
        },
        tooltip: {
            theme: 'light',
            y: {
                formatter: function(value: number) {
                    return '$' + value + 'k'
                }
            }
        },
        grid: {
            borderColor: '#f1f1f1',
            padding: {
                top: 0,
                right: 0,
                bottom: 0,
                left: 0
            }
        },
        plotOptions: {
            bar: {
                horizontal: false,
                columnWidth: '70%',
                borderRadius: 4
            },
        }
    }

    // Chart series data
    const series = [
        {
            name: 'Limited-Time Offers',
            data: [45, 75, 100, 85, 60, 40]
        },
        {
            name: 'Personalized Promotions',
            data: [40, 65, 85, 70, 50, 35]
        },
        {
            name: 'Generic Promotions',
            data: [30, 50, 65, 55, 40, 25]
        },
        {
            name: 'Profit Margin',
            data: [25, 20, 15, 10, 5, 0]
        }
    ]

    return (
        <Paper 
            elevation={0}
            sx={{
                p: 2,
                borderRadius: 3,
                border: '1px solid',
                borderColor: 'divider',
                backgroundColor: 'white',
                mb: 3
            }}
        >
            <Typography variant="h6" gutterBottom sx={{ fontWeight: 'bold', color: '#424242' }}>
                Promotion Effectiveness by Discount Level
            </Typography>
            <Typography variant="body2" sx={{ color: 'text.secondary', mb: 2 }}>
                Revenue impact of different promotion types across discount percentages
            </Typography>
            <Box sx={{ height: 350 }}>
                <Chart 
                    options={options as any}
                    series={series}
                    type="bar"
                    height="100%"
                    width="100%"
                />
            </Box>
        </Paper>
    )
}

export default PromotionsChart
