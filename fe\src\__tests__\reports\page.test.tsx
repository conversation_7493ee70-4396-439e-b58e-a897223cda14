import { render, screen } from '@testing-library/react'
import Report from '@/app/reports/page'

// Mock dependencies
jest.mock('@/components/common/layout/GenericLayout', () => ({ children }: any) => (
    <div data-testid="layout">{children}</div>
))
jest.mock('@/components/common/utility/PageTitle', () => ({ title }: any) => <div data-testid="title">{title}</div>)
jest.mock('@/app/reports/reportSearch/components/ReportsHeader', () => () => (
    <div data-testid="reports-header">ReportsHeader</div>
))
jest.mock('@/app/reports/components/ReportTable', () => ({ view }: any) => <div data-testid="report-table">{view}</div>)
jest.mock('@/utils/helper', () => ({
    getReportView: jest.fn(() => 'mockView'),
}))

describe('Report Page', () => {
    it('renders layout, title, header, and report table with correct view', () => {
        render(<Report searchParams={{ view: 'active' }} />)

        expect(screen.getByTestId('layout')).toBeInTheDocument()
        expect(screen.getByTestId('title')).toHaveTextContent('Reports Catalog')
        expect(screen.getByTestId('reports-header')).toBeInTheDocument()
        expect(screen.getByTestId('report-table')).toHaveTextContent('mockView')
    })
})
