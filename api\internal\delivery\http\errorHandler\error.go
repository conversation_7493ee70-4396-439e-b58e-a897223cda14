package errorHandler

import (
	"dng-module/internal/utils"
	"net/http"

	"github.com/gin-gonic/gin"
)

// ErrorHandler handles HTTP errors by returning appropriate status codes
// The frontend will handle displaying the error pages
type <PERSON>rror<PERSON>and<PERSON> struct{}

// NewErrorHandler creates a new error handler
func NewErrorHandler() *ErrorHandler {
	return &ErrorHandler{}
}

// Handle404 handles 404 Not Found errors
func (h *ErrorHandler) Handle404(c *gin.Context) {
	utils.Logger.Infof("404 Not Found: %s", c.Request.URL.Path)
	c.AbortWithStatus(http.StatusNotFound)
}

// Handle500 handles 500 Internal Server Error
func (h *ErrorHandler) Handle500(c *gin.Context) {
	utils.Logger.Error("500 Internal Server Error")
	c.AbortWithStatus(http.StatusInternalServerError)
}

// Handle403 handles 403 Forbidden errors
func (h *ErrorHandler) Handle403(c *gin.Context) {
	utils.Logger.Infof("403 Forbidden: %s", c.Request.URL.Path)
	c.AbortWithStatus(http.StatusForbidden)
}

// HandleError is a generic error handler that can be used for any HTTP error
func (h *ErrorHandler) HandleError(c *gin.Context, statusCode int) {
	utils.Logger.Infof("Error %d: %s", statusCode, c.Request.URL.Path)
	c.AbortWithStatus(statusCode)
}

// NoRoute is a handler for 404 errors that can be registered with Gin
func (h *ErrorHandler) NoRoute(c *gin.Context) {
	h.Handle404(c)
}

// NoMethod is a handler for 405 Method Not Allowed errors that can be registered with Gin
func (h *ErrorHandler) NoMethod(c *gin.Context) {
	utils.Logger.Infof("405 Method Not Allowed: %s %s", c.Request.Method, c.Request.URL.Path)
	c.AbortWithStatus(http.StatusMethodNotAllowed)
}
