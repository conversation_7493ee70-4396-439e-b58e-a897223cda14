'use client'

import React from 'react'
import { CssBaseline } from '@mui/material'
import { ThemeProvider } from '@mui/material/styles'
import { AppRouterCacheProvider } from '@mui/material-nextjs/v15-appRouter'
import { SessionProvider } from 'next-auth/react'
import ReduxProvider from '@/store/provider'
import { ApiStatusProvider } from '@/contexts/ApiStatusContext'
import { theme } from '@/theme'

export default function LayoutControl({ children }: Readonly<{ children: React.ReactNode }>) {
    return (
        <AppRouterCacheProvider options={{ enableCssLayer: true }}>
            <ThemeProvider theme={theme}>
                <SessionProvider>
                    <ReduxProvider>
                        <ApiStatusProvider>
                            <CssBaseline />
                            {children}
                        </ApiStatusProvider>
                    </ReduxProvider>
                </SessionProvider>
            </ThemeProvider>
        </AppRouterCacheProvider>
    )
}
