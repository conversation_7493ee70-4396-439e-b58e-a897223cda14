import ReportForm from '@/app/reports/reportCards/components/ReportForm'
import { Report } from '@/app/reports/types'
import Loader from '@/components/common/utility/Loader'
import { ADMIN_ROLE, SOURCE_OPTIONS, SUPER_ADMIN } from '@/utils/constants'
import { checkRole, createPBILink, downloadBlob } from '@/utils/helper'
import { snackbar } from '@/utils/toast'
import {
    Bookmark,
    BookmarkBorder,
    Favorite,
    FavoriteBorder,
    ModeEditOutlined,
    MoreVert,
    OpenInNew,
    PictureAsPdfOutlined,
} from '@mui/icons-material'
import { Box, IconButton, ListItemIcon, ListItemText, Menu, MenuItem, styled, Tooltip } from '@mui/material'
import { useSession } from 'next-auth/react'
import React, { useCallback, useMemo, useState } from 'react'
import { downloadPBIService, lookerEmbedUrlService, lookerPDFApi, updatePreferenceService } from '../../services'
import { setRefreshFlag } from '@/app/reports/slice'
import { useAppDispatch } from '@/store/hooks'

const StyledBox = styled(Box)({
    /*width: '100px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'flex-end',
    marginLeft: 'auto',*/
})

const isExportEnabled = process.env.NEXT_PUBLIC_IS_EXPORT_ENABLED === 'true'
const isPreferenceEnabled = process.env.NEXT_PUBLIC_IS_PREFERENCE_ENABLED === 'true'
// const isEditEnabled = process.env.NEXT_PUBLIC_IS_EDIT_ENABLED === 'true'

const TableActions: React.FC<{ row: Report }> = ({ row }) => {
    const [isLookerLoading, setIsLookerLoading] = useState(false)
    const [isPBILoading, setIsPBILoading] = useState(false)
    const [isExportLoading, setIsExportLoading] = useState(false)
    const [isFavorite, setIsFavorite] = useState(row.is_favorite)
    const [isSaved, setIsSaved] = useState(row.is_saved)
    const [isFavoriteLoading, setIsFavoriteLoading] = useState(false)
    const [isSavedLoading, setIsSavedLoading] = useState(false)
    const [editAction, setEditAction] = useState(false)
    const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null)
    const open = Boolean(anchorEl)
    const dispatch = useAppDispatch()

    const isPowerBI = useMemo(
        () => row.source_system === SOURCE_OPTIONS.PowerBI && row.group_id,
        [row.source_system, row.group_id],
    )
    const isLooker = useMemo(() => row.source_system === SOURCE_OPTIONS.Looker, [row.source_system])

    const { data: session } = useSession()
    const isAdmin = checkRole(session?.user, ADMIN_ROLE) || checkRole(session?.user, SUPER_ADMIN)

    const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
        event.stopPropagation()
        setAnchorEl(event.currentTarget)
    }

    const handleMenuClose = (event: unknown) => {
        setAnchorEl(null)
    }

    const handlePBIExport = useCallback(
        async (e: React.MouseEvent) => {
            e.stopPropagation()
            handleMenuClose(e)
            if (isPBILoading) return

            setIsPBILoading(true)
            try {
                await downloadPBIService(row.report_id)
            } catch (error) {
                console.error('Error exporting PBI:', error)
            } finally {
                setIsPBILoading(false)
            }
        },
        [row.report_id, isPBILoading],
    )

    const handleLookerExport = useCallback(
        async (e: React.MouseEvent) => {
            e.stopPropagation()
            handleMenuClose(e)
            if (isExportLoading) return

            setIsExportLoading(true)
            try {
                const blob = await lookerPDFApi(row.report_id)
                downloadBlob(blob, row.report_id + '.pdf')
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Unknown error'
                snackbar.error(`Error: ${errorMessage}`)
                console.error('Error exporting Looker PDF:', error)
            } finally {
                setIsExportLoading(false)
            }
        },
        [row.report_id, isExportLoading],
    )

    const handleTogglePreference = useCallback(
        async (
            e: React.MouseEvent,
            preferenceType: 'favorites' | 'saved-later',
            currentStatus: boolean,
            setStatus: React.Dispatch<React.SetStateAction<boolean>>,
            setLoading: React.Dispatch<React.SetStateAction<boolean>>,
        ) => {
            e.stopPropagation()
            const preferenceLabel = preferenceType === 'favorites' ? 'favorites' : 'saved'
            try {
                setLoading(true)
                const status = await updatePreferenceService(row.id, !currentStatus, preferenceType, session)
                if (status === 200) {
                    setStatus((prev) => !prev)
                    const message = !currentStatus
                        ? `Report has been added to ${preferenceLabel} successfully.`
                        : `Report has been removed from ${preferenceLabel} successfully.`
                    snackbar.success(message)
                } else {
                    snackbar.error(`Failed to update ${preferenceType} status. Please try again.`)
                }
            } catch (error) {
                snackbar.error(`An error occurred while updating ${preferenceType} status.`)
                console.log(error)
            } finally {
                dispatch(setRefreshFlag(true))
                setLoading(false)
            }
        },
        [row.id, session],
    )

    const handleToggleFavorite = (e: React.MouseEvent) => {
        handleTogglePreference(e, 'favorites', isFavorite, setIsFavorite, setIsFavoriteLoading)
    }

    const handleToggleSaved = (e: React.MouseEvent) => {
        handleTogglePreference(e, 'saved-later', isSaved, setIsSaved, setIsSavedLoading)
    }

    const handleOpenEditForm = (e: React.MouseEvent) => {
        e.stopPropagation()
        handleMenuClose(e)
        setEditAction(true)
    }

    const handleOpenPBILink = (e: React.MouseEvent) => {
        e.stopPropagation()
        handleMenuClose(e)
        window.open(createPBILink(row.report_id, row.group_id), '_blank', 'noopener,noreferrer')
    }

    const handleOpenLookerLink = useCallback(
        async (e: React.MouseEvent) => {
            e.stopPropagation()
            handleMenuClose(e)
            if (isLookerLoading) return

            setIsLookerLoading(true)
            try {
                const url = await lookerEmbedUrlService(row.report_id)
                window.open(url, '_blank', 'noopener,noreferrer')
            } catch (error) {
                console.error('Error fetching Looker URL:', error)
            } finally {
                setIsLookerLoading(false)
            }
        },
        [row.report_id, isLookerLoading],
    )

    const getIcon = (isLoading: boolean, isActive: boolean, ActiveIcon: JSX.Element, InactiveIcon: JSX.Element) => {
        if (isLoading) return <Loader color="info" size={20} />
        return isActive ? ActiveIcon : InactiveIcon
    }

    return (
        <StyledBox>
            {isPreferenceEnabled && (
                <>
                    <Tooltip title={isFavorite ? 'Remove from favorites' : 'Add to favorites'} arrow>
                        <IconButton onClick={handleToggleFavorite} size="small" disabled={isFavoriteLoading}>
                            {getIcon(
                                isFavoriteLoading,
                                isFavorite,
                                <Favorite color="error" fontSize="small" />,
                                <FavoriteBorder color="secondary" fontSize="small" />,
                            )}
                        </IconButton>
                    </Tooltip>
                    <Tooltip title={isSaved ? 'Remove from saved' : 'Save for later'} arrow>
                        <IconButton onClick={handleToggleSaved} size="small" disabled={isSavedLoading}>
                            {getIcon(
                                isSavedLoading,
                                isSaved,
                                <Bookmark color="info" fontSize="small" />,
                                <BookmarkBorder color="secondary" fontSize="small" />,
                            )}
                        </IconButton>
                    </Tooltip>
                </>
            )}

            <Tooltip title="More options" arrow>
                <IconButton
                    onClick={handleMenuOpen}
                    size="small"
                    aria-controls={open ? 'action-menu' : undefined}
                    aria-haspopup="true"
                    aria-expanded={open ? 'true' : undefined}
                >
                    <MoreVert color="secondary" fontSize="small" />
                </IconButton>
            </Tooltip>

            <Menu
                id="action-menu"
                anchorEl={anchorEl}
                open={open}
                onClose={(e) => handleMenuClose(e)}
                onClick={(e) => e.stopPropagation()}
                transformOrigin={{ horizontal: 'right', vertical: 'top' }}
                anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
            >
                {isAdmin && (
                    <MenuItem onClick={handleOpenEditForm}>
                        <ListItemIcon>
                            <ModeEditOutlined color="secondary" fontSize="small" />
                        </ListItemIcon>
                        <ListItemText>Edit Report</ListItemText>
                    </MenuItem>
                )}

                {isPowerBI && (
                    <>
                        {isExportEnabled && (
                            <MenuItem onClick={handlePBIExport} disabled={isPBILoading}>
                                <ListItemIcon>
                                    {isPBILoading ? (
                                        <Loader />
                                    ) : (
                                        <PictureAsPdfOutlined color="secondary" fontSize="small" />
                                    )}
                                </ListItemIcon>
                                <ListItemText>Export PDF</ListItemText>
                            </MenuItem>
                        )}
                        <MenuItem onClick={handleOpenPBILink}>
                            <ListItemIcon>
                                <OpenInNew fontSize="small" />
                            </ListItemIcon>
                            <ListItemText>View Report</ListItemText>
                        </MenuItem>
                    </>
                )}

                {isLooker && (
                    <>
                        {isExportEnabled && (
                            <MenuItem onClick={handleLookerExport} disabled={isExportLoading}>
                                <ListItemIcon>
                                    {isExportLoading ? (
                                        <Loader />
                                    ) : (
                                        <PictureAsPdfOutlined color="secondary" fontSize="small" />
                                    )}
                                </ListItemIcon>
                                <ListItemText>Export PDF</ListItemText>
                            </MenuItem>
                        )}
                        <MenuItem onClick={handleOpenLookerLink} disabled={isLookerLoading}>
                            <ListItemIcon>
                                {isLookerLoading ? (
                                    <Loader size={20} />
                                ) : (
                                    <OpenInNew color="secondary" fontSize="small" />
                                )}
                            </ListItemIcon>
                            <ListItemText>View Report</ListItemText>
                        </MenuItem>
                    </>
                )}
            </Menu>

            <ReportForm
                open={editAction}
                onClose={(event, reason) => {
                    if (event && 'stopPropagation' in event) {
                        event.stopPropagation() // Prevent bubbling when closing
                    }
                    setEditAction(false)
                }}
                report={row}
            />
        </StyledBox>
    )
}

export default TableActions
