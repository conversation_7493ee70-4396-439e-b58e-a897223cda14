import * as helper from '@/utils/helper'
import { Session } from 'next-auth'

describe('helper utility functions', () => {
    describe('normalizeDate', () => {
        it('returns ISO date string for valid date', () => {
            const date = new Date('2024-05-14T10:00:00Z')
            expect(helper.normalizeDate(date)).toMatch(/\d{4}-\d{2}-\d{2}/)
        })
        it('returns empty string for null', () => {
            expect(helper.normalizeDate(null)).toBe('')
        })
    })

    describe('checkIsAdmin', () => {
        const baseUser: Session['user'] = {
            id: '1',
            name: 'Test User',
            user_id: '1',
            role_id: 1,
            role_name: '',
            priority: 1,
            roles: [],
        }
        it('returns true for ADMIN_ROLE', () => {
            expect(helper.checkIsAdmin({ ...baseUser, role_name: 'Admin' })).toBe(true)
        })
        it('returns true for SUPER_ADMIN', () => {
            expect(helper.checkIsAdmin({ ...baseUser, role_name: 'Super Admin' })).toBe(true)
        })
        it('returns false for other roles', () => {
            expect(helper.checkIsAdmin({ ...baseUser, role_name: 'User' })).toBe(false)
        })
        it('returns false for undefined user', () => {
            expect(helper.checkIsAdmin(undefined)).toBe(false)
        })
    })

    describe('checkRole', () => {
        const baseUser = {
            id: '1',
            name: 'Test User',
            user_id: '1',
            role_id: 1,
            role_name: '',
            priority: 1,
            roles: [],
        }
        it('returns true if user has the role', () => {
            expect(helper.checkRole({ ...baseUser, role_name: 'Admin' }, 'Admin')).toBe(true)
        })
        it('returns false if user does not have the role', () => {
            expect(helper.checkRole({ ...baseUser, role_name: 'User' }, 'Admin')).toBe(false)
        })
    })

    describe('formatDateTime', () => {
        it('formats date string as expected', () => {
            const date = '2024-05-14T10:00:00Z'
            expect(helper.formatDateTime(date)).toContain('2024')
        })
    })
})
