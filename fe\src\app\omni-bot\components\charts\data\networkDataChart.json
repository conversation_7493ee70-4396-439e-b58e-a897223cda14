{"salesOptions": {"chart": {"id": "sales-performance", "toolbar": {"show": false}, "fontFamily": "<PERSON>l, sans-serif"}, "colors": ["#4CAF50", "#8BC34A", "#CDDC39", "#FFC107"], "xaxis": {"categories": ["Southern", "Western", "Midwest", "Northeast"], "labels": {"style": {"colors": "#616161", "fontSize": "12px", "fontFamily": "<PERSON>l, sans-serif"}}}, "yaxis": {"title": {"text": "Percentage Above Target", "style": {"color": "#616161", "fontSize": "12px", "fontFamily": "<PERSON>l, sans-serif"}}, "labels": {"formatter": "function(value){return value+'%'}", "style": {"colors": "#616161", "fontSize": "12px", "fontFamily": "<PERSON>l, sans-serif"}}}, "dataLabels": {"enabled": true, "formatter": "function(val){return val+'%'}", "style": {"fontSize": "12px", "fontFamily": "<PERSON>l, sans-serif", "colors": ["#333"]}}, "plotOptions": {"bar": {"borderRadius": 4, "columnWidth": "60%"}}, "legend": {"show": false}, "tooltip": {"theme": "light", "y": {"formatter": "function(value){return value+'% above target'}"}}, "grid": {"borderColor": "#f1f1f1", "row": {"colors": ["transparent", "transparent"], "opacity": 0.5}}}, "inventoryOptions": {"chart": {"id": "inventory-turnover", "toolbar": {"show": false}, "fontFamily": "<PERSON>l, sans-serif"}, "colors": ["#2196F3"], "xaxis": {"categories": ["Western", "Southern", "Midwest", "Northeast", "Network Avg"], "labels": {"style": {"colors": "#616161", "fontSize": "12px", "fontFamily": "<PERSON>l, sans-serif"}}}, "yaxis": {"title": {"text": "Turns per Year", "style": {"color": "#616161", "fontSize": "12px", "fontFamily": "<PERSON>l, sans-serif"}}, "labels": {"style": {"colors": "#616161", "fontSize": "12px", "fontFamily": "<PERSON>l, sans-serif"}}}, "dataLabels": {"enabled": true, "style": {"fontSize": "12px", "fontFamily": "<PERSON>l, sans-serif", "colors": ["#333"]}}, "stroke": {"curve": "smooth", "width": 2}, "markers": {"size": 5, "colors": ["#2196F3"], "strokeWidth": 0, "hover": {"size": 7}}, "fill": {"type": "gradient", "gradient": {"shade": "light", "type": "vertical", "shadeIntensity": 0.1, "opacityFrom": 0.7, "opacityTo": 0.2, "stops": [0, 100]}}, "legend": {"show": false}, "tooltip": {"theme": "light"}, "grid": {"borderColor": "#f1f1f1", "row": {"colors": ["transparent", "transparent"], "opacity": 0.5}}}, "salesSeries": [{"name": "Sales Performance", "data": [4.7, 3.8, 2.5, 1.8]}], "inventorySeries": [{"name": "Inventory Turnover", "data": [13.2, 12.8, 12.1, 11.5, 12.4]}]}