import { render, screen } from '@testing-library/react'
import '@testing-library/jest-dom'
import PageTitle from '@/components/common/utility/PageTitle'

describe('PageTitle component', () => {
    it('renders the title text', () => {
        render(<PageTitle title="Dashboard" />)
        expect(screen.getByText('Dashboard')).toBeInTheDocument()
    })

    it('applies default variant and component props', () => {
        render(<PageTitle title="Default Title" />)
        const heading = screen.getByText('Default Title')
        expect(heading.tagName.toLowerCase()).toBe('h6') // default 'component'
        expect(heading).toHaveClass('MuiTypography-h6')  // default 'variant'
    })

    it('applies custom variant and component', () => {
        render(<PageTitle title="Big Heading" variant="h4" component="h1" />)
        const heading = screen.getByText('Big Heading')
        expect(heading.tagName.toLowerCase()).toBe('h1')
        expect(heading).toHaveClass('MuiTypography-h4')
    })

    it('merges custom sx styles', () => {
        render(<PageTitle title="Styled Title" sx={{ color: 'red' }} />)
        const heading = screen.getByText('Styled Title')
        expect(heading).toHaveStyle('color: red')
    })
})
