package catalogReportService

import (
	// "dng-module/internal/app/catalogReportService"
	"dng-module/internal/model"
	"dng-module/testing/utilsTest"
	"errors"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gorm.io/gorm"
)

func setupMock(t *testing.T) (*gorm.DB, sqlmock.Sqlmock) {
	db, mock := utilsTest.SetupMockDB(t)
	return db, mock
}

func TestGetFilterOptions_UserNotFound(t *testing.T) {
	db, mock := setupMock(t)
	defer utilsTest.CloseMockDB(db)
	// defer utilsTest.CloseDB(db)

	mock.ExpectQuery(`SELECT .* FROM "users" WHERE email = \$1 ORDER BY "users"\."id" LIMIT \$[0-9]+`).
		WithArgs("<EMAIL>", 1).
		WillReturnError(gorm.ErrRecordNotFound)

	result, err := GetFilterOptions(db, "<EMAIL>")
	assert.Nil(t, result)
	assert.Error(t, err)
}

func TestGetFilterOptions_DomainAccessError(t *testing.T) {
	db, mock := setupMock(t)
	defer utilsTest.CloseMockDB(db)

	mock.ExpectQuery(`SELECT .* FROM "users" WHERE email = \$1 ORDER BY "users"\."id" LIMIT LIMIT \$[0-9]+`).
		WithArgs("<EMAIL>", 1).
		WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))

	mock.ExpectQuery("SELECT .* FROM user_domain_access .*").
		WillReturnError(errors.New("domain access error"))

	result, err := GetFilterOptions(db, "<EMAIL>")
	assert.Nil(t, result)
	assert.Error(t, err)
}

func TestGetFilterOptions_NoDomainAccess(t *testing.T) {
	db, mock := setupMock(t)
	defer utilsTest.CloseMockDB(db)

	mock.ExpectQuery(`SELECT .* FROM "users" WHERE email = \$1 ORDER BY "users"\."id" LIMIT \$[0-9]+`).
		WithArgs("<EMAIL>", 1).
		WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))

	mock.ExpectQuery("SELECT .* FROM user_domain_access .*").
		WillReturnRows(sqlmock.NewRows([]string{"domain_id", "role_id", "is_super_admin", "can_view"}))

	result, err := GetFilterOptions(db, "<EMAIL>")
	assert.NoError(t, err)
	assert.Equal(t, 0, len(result["domains"].([]model.Domain)))
	assert.Equal(t, 0, len(result["source_system"].([]string)))
	assert.Equal(t, 0, len(result["category"].([]string)))
}
func TestGetFilterOptions_SuperAdmin(t *testing.T) {
	db, mock := setupMock(t)
	defer utilsTest.CloseMockDB(db)

	mock.ExpectQuery(`SELECT .* FROM "users" WHERE email = \$1 ORDER BY "users"\."id" LIMIT \$[0-9]+`).
		WithArgs("<EMAIL>", 1).
		WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))

	mock.ExpectQuery(`SELECT .* FROM user_domain_access .*`).
		WillReturnRows(sqlmock.NewRows([]string{"domain_id", "role_id", "is_super_admin", "can_view"}).
			AddRow(1, 1, true, true))

	mock.ExpectQuery(`SELECT .* FROM "domains"`).
		WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).
			AddRow(1, "Finance").AddRow(2, "HR"))

	mock.ExpectQuery(`SELECT DISTINCT "source_system" FROM "reports"`).
		WillReturnRows(sqlmock.NewRows([]string{"source_system"}).
			AddRow("SAP").AddRow("Oracle"))

	mock.ExpectQuery(`SELECT DISTINCT "category" FROM "reports"`).
		WillReturnRows(sqlmock.NewRows([]string{"category"}).
			AddRow("Revenue").AddRow("Cost"))

	result, err := GetFilterOptions(db, "<EMAIL>")
	assert.NoError(t, err)
	assert.Len(t, result["domains"], 2)
	assert.Len(t, result["source_system"], 2)
	assert.Len(t, result["category"], 2)
}

func TestGetFilterOptions_CanViewDomains(t *testing.T) {
	db, mock := setupMock(t)
	defer utilsTest.CloseMockDB(db)

	mock.ExpectQuery(`SELECT .* FROM "users" WHERE email = \$1 ORDER BY "users"\."id" LIMIT .*`).
		WithArgs("<EMAIL>", 1).
		WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))

	mock.ExpectQuery(`SELECT .* FROM user_domain_access .*`).
		WillReturnRows(sqlmock.NewRows([]string{"domain_id", "role_id", "is_super_admin", "can_view"}).
			AddRow(10, 2, false, true))

	mock.ExpectQuery(`SELECT .* FROM "domains" WHERE id IN .*`).
		WithArgs(10).
		WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).
			AddRow(10, "Finance"))

	mock.ExpectQuery(`SELECT DISTINCT "source_system" FROM "reports" WHERE domain_id IN .*`).
		WillReturnRows(sqlmock.NewRows([]string{"source_system"}).AddRow("ServiceNow"))

	mock.ExpectQuery(`SELECT DISTINCT "category" FROM "reports" WHERE domain_id IN .*`).
		WillReturnRows(sqlmock.NewRows([]string{"category"}).AddRow("Incidents"))

	result, err := GetFilterOptions(db, "<EMAIL>")
	require.NoError(t, err)
	require.NotNil(t, result)

	assert.Len(t, result["domains"], 1)
	assert.Len(t, result["source_system"], 1)
	assert.Len(t, result["category"], 1)
}

func TestGetFilterOptions_NoViewableDomains(t *testing.T) {
	db, mock := setupMock(t)
	defer utilsTest.CloseMockDB(db)

	mock.ExpectQuery(`SELECT .* FROM "users" WHERE email = \$1 ORDER BY "users"\."id" LIMIT \$[0-9]+`).
		WithArgs("<EMAIL>", 1).
		WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))

	mock.ExpectQuery("SELECT .* FROM user_domain_access .*").
		WillReturnRows(sqlmock.NewRows([]string{"domain_id", "role_id", "is_super_admin", "can_view"}).
			AddRow(10, 2, false, false)) // can_view = false

	result, err := GetFilterOptions(db, "<EMAIL>")
	assert.NoError(t, err)
	assert.Len(t, result["domains"], 0)
	assert.Len(t, result["source_system"], 0)
	assert.Len(t, result["category"], 0)
}

func TestGetFilterOptions_DomainFetchError(t *testing.T) {
	db, mock := setupMock(t)
	defer utilsTest.CloseMockDB(db)

	// Mock user found
	mock.ExpectQuery(`SELECT .* FROM "users" WHERE email = .*`).
		WithArgs("<EMAIL>").
		WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))

	// Mock user domain access with can_view true
	mock.ExpectQuery("SELECT .* FROM user_domain_access .*").
		WillReturnRows(sqlmock.NewRows([]string{"domain_id", "role_id", "is_super_admin", "can_view"}).
			AddRow(10, 2, false, true))

	// Simulate error when fetching domains
	mock.ExpectQuery("SELECT .* FROM \"domains\" WHERE .*").
		WithArgs(10).
		WillReturnError(errors.New("domain fetch error"))

	result, err := GetFilterOptions(db, "<EMAIL>")
	assert.Nil(t, result)
	assert.Error(t, err)
}

func TestGetFilterOptions_SourceSystemsFetchError(t *testing.T) {
	db, mock := setupMock(t)
	defer utilsTest.CloseMockDB(db)

	mock.ExpectQuery(`SELECT .* FROM "users" WHERE email = .*`).
		WithArgs("<EMAIL>").
		WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))

	mock.ExpectQuery("SELECT .* FROM user_domain_access .*").
		WillReturnRows(sqlmock.NewRows([]string{"domain_id", "role_id", "is_super_admin", "can_view"}).
			AddRow(10, 2, false, true))

	mock.ExpectQuery("SELECT .* FROM \"domains\" WHERE .*").
		WithArgs(10).
		WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).AddRow(10, "Finance"))

	mock.ExpectQuery("SELECT DISTINCT .* FROM \"reports\" WHERE domain_id IN .*").
		WillReturnError(errors.New("source systems fetch error"))

	result, err := GetFilterOptions(db, "<EMAIL>")
	assert.Nil(t, result)
	assert.Error(t, err)
}

func TestGetFilterOptions_CategoriesFetchError(t *testing.T) {
	db, mock := setupMock(t)
	defer utilsTest.CloseMockDB(db)

	mock.ExpectQuery(`SELECT .* FROM "users" WHERE email = .*`).
		WithArgs("<EMAIL>").
		WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))

	mock.ExpectQuery("SELECT .* FROM user_domain_access .*").
		WillReturnRows(sqlmock.NewRows([]string{"domain_id", "role_id", "is_super_admin", "can_view"}).
			AddRow(10, 2, false, true))

	mock.ExpectQuery("SELECT .* FROM \"domains\" WHERE .*").
		WithArgs(10).
		WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).AddRow(10, "Finance"))

	mock.ExpectQuery("SELECT DISTINCT .* FROM \"reports\" WHERE domain_id IN .*").
		WillReturnRows(sqlmock.NewRows([]string{"source_system"}).AddRow("ServiceNow"))

	mock.ExpectQuery("SELECT DISTINCT .* FROM \"reports\" WHERE domain_id IN .*").
		WillReturnError(errors.New("categories fetch error"))

	result, err := GetFilterOptions(db, "<EMAIL>")
	assert.Nil(t, result)
	assert.Error(t, err)
}

func TestGetFilterOptions_MultipleAccessEntries(t *testing.T) {
	db, mock := setupMock(t)
	defer utilsTest.CloseMockDB(db)

	mock.ExpectQuery(`SELECT .* FROM "users" WHERE email = .*`).
		WithArgs("<EMAIL>", 1). // include LIMIT argument
		WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))

	mock.ExpectQuery("SELECT .* FROM user_domain_access .*").
		WillReturnRows(sqlmock.NewRows([]string{"domain_id", "role_id", "is_super_admin", "can_view"}).
			AddRow(10, 2, false, true).
			AddRow(11, 3, false, false).
			AddRow(12, 1, true, true)) // one super admin, so super admin path

	mock.ExpectQuery(`SELECT .* FROM "domains"`).
		WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).
			AddRow(10, "Finance").
			AddRow(11, "HR").
			AddRow(12, "Admin"))

	mock.ExpectQuery(`SELECT DISTINCT "source_system" FROM "reports"`).
		WillReturnRows(sqlmock.NewRows([]string{"source_system"}).
			AddRow("SAP").AddRow("Oracle"))

	mock.ExpectQuery(`SELECT DISTINCT "category" FROM "reports"`).
		WillReturnRows(sqlmock.NewRows([]string{"category"}).
			AddRow("Revenue").AddRow("Cost"))

	result, err := GetFilterOptions(db, "<EMAIL>")
	assert.NoError(t, err)
	assert.Len(t, result["domains"], 3)
	assert.Len(t, result["source_system"], 2)
	assert.Len(t, result["category"], 2)
}
