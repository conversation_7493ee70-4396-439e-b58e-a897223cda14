import {
    AutoAwesomeOutlined,
    BookmarkBorderOutlined,
    Home,
    StarOutlineOutlined,
    SvgIconComponent,
    TableChartOutlined,
} from '@mui/icons-material'

// Define the type for menu items
interface BaseMenuItem {
    id: string
    title: string
    icon: SvgIconComponent
    href: string
}

interface SubheaderItem {
    id: string
    navlabel: true
    subheader: string
}

// Union type for all menu items
type MenuItem = BaseMenuItem | SubheaderItem

// Define the type for chat data
interface ChatDataItem {
    title: string
    chats: string[]
}

export const ReportsItems: MenuItem[] = [
    {
        id: '0',
        title: 'Homepage',
        href: '/',
        icon: Home,
    },
    {
        id: '1',
        title: 'Reports Catalog',
        icon: TableChartOutlined,
        href: '/reports',
    },
    {
        id: '2',
        title: 'Favorites',
        icon: StarOutlineOutlined,
        href: '/reports?view=favorites',
    },
    {
        id: '3',
        title: 'Saved for later',
        icon: BookmarkBorderOutlined,
        href: '/reports?view=saved-later',
    },
]

export const aiAnalyticsItems: BaseMenuItem[] = [
    {
        id: '0',
        title: 'Homepage',
        href: '/',
        icon: Home,
    },
    {
        id: '1',
        title: 'AI Analytics',
        icon: AutoAwesomeOutlined,
        href: '/ai-analytics',
    },
]

export const generalSidebarItems: MenuItem[] = [
    {
        id: '0',
        title: 'Homepage',
        href: '/',
        icon: Home,
    },
    {
        id: '1',
        title: 'Report Catalog',
        href: '/reports',
        icon: TableChartOutlined,
    },
    {
        id: '2',
        title: 'Omni Bot',
        href: '/omni-bot',
        icon: AutoAwesomeOutlined,
    },
    {
        id: '3',
        title: 'Data Catalog',
        href: '/data-catalog',
        icon: TableChartOutlined,
    },
]

export const CHAT_DATA: ChatDataItem[] = [
    {
        title: "Today's Chats",
        chats: ['Data Availability', 'Inventory Trend', 'All In Margin (AIM)'],
    },
    {
        title: "Yesterday's Chats",
        chats: ['Data Availability', 'Inventory Trend', 'All In Margin (AIM)', 'Inventory Build', 'Sample'],
    },
]
