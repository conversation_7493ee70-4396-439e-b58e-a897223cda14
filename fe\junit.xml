<?xml version="1.0" encoding="UTF-8"?>
<testsuites name="jest tests" tests="73" failures="4" errors="0" time="38.387">
  <testsuite name="authOptions.jwt callback" errors="0" failures="1" skipped="0" timestamp="2025-05-22T09:21:47" time="4.167" tests="4">
    <testcase classname="authOptions.jwt callback should set tokens on initial sign in" name="authOptions.jwt callback should set tokens on initial sign in" time="0.043">
    </testcase>
    <testcase classname="authOptions.jwt callback should return token if not expired" name="authOptions.jwt callback should return token if not expired" time="0.001">
    </testcase>
    <testcase classname="authOptions.jwt callback should refresh token if expired" name="authOptions.jwt callback should refresh token if expired" time="0.006">
      <failure>Error: expect(received).toBe(expected) // Object.is equality

Expected: &quot;new-access-token&quot;
Received: &quot;old-access-token&quot;
    at Object.toBe (C:\Users\<USER>\OneDrive - Dollar General Corporation\Desktop\Projects\master\src\__tests__\authentication\authOptions.test.ts:104:37)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</failure>
    </testcase>
    <testcase classname="authOptions.jwt callback should set error if refresh fails" name="authOptions.jwt callback should set error if refresh fails" time="0.001">
    </testcase>
  </testsuite>
  <testsuite name="axiosInstance" errors="0" failures="3" skipped="0" timestamp="2025-05-22T09:21:47" time="4.416" tests="3">
    <testcase classname="axiosInstance attaches access token to request headers" name="axiosInstance attaches access token to request headers" time="0.023">
      <failure>TypeError: Cannot read properties of undefined (reading &apos;fulfilled&apos;)
    at Object.fulfilled (C:\Users\<USER>\OneDrive - Dollar General Corporation\Desktop\Projects\master\src\__tests__\utils\axiosInstance.test.ts:15:82)
    at Promise.then.completed (C:\Users\<USER>\OneDrive - Dollar General Corporation\Desktop\Projects\master\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\OneDrive - Dollar General Corporation\Desktop\Projects\master\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\OneDrive - Dollar General Corporation\Desktop\Projects\master\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (C:\Users\<USER>\OneDrive - Dollar General Corporation\Desktop\Projects\master\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\OneDrive - Dollar General Corporation\Desktop\Projects\master\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\OneDrive - Dollar General Corporation\Desktop\Projects\master\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\OneDrive - Dollar General Corporation\Desktop\Projects\master\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\OneDrive - Dollar General Corporation\Desktop\Projects\master\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\OneDrive - Dollar General Corporation\Desktop\Projects\master\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\OneDrive - Dollar General Corporation\Desktop\Projects\master\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\OneDrive - Dollar General Corporation\Desktop\Projects\master\node_modules\jest-runner\build\runTest.js:444:34)
    at Object.worker (C:\Users\<USER>\OneDrive - Dollar General Corporation\Desktop\Projects\master\node_modules\jest-runner\build\testWorker.js:106:12)</failure>
    </testcase>
    <testcase classname="axiosInstance retries request after 401 and session refresh" name="axiosInstance retries request after 401 and session refresh" time="0.001">
      <failure>TypeError: Cannot read properties of undefined (reading &apos;rejected&apos;)
    at Object.rejected (C:\Users\<USER>\OneDrive - Dollar General Corporation\Desktop\Projects\master\src\__tests__\utils\axiosInstance.test.ts:28:83)
    at Promise.then.completed (C:\Users\<USER>\OneDrive - Dollar General Corporation\Desktop\Projects\master\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\OneDrive - Dollar General Corporation\Desktop\Projects\master\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\OneDrive - Dollar General Corporation\Desktop\Projects\master\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (C:\Users\<USER>\OneDrive - Dollar General Corporation\Desktop\Projects\master\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\OneDrive - Dollar General Corporation\Desktop\Projects\master\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\OneDrive - Dollar General Corporation\Desktop\Projects\master\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\OneDrive - Dollar General Corporation\Desktop\Projects\master\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\OneDrive - Dollar General Corporation\Desktop\Projects\master\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\OneDrive - Dollar General Corporation\Desktop\Projects\master\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\OneDrive - Dollar General Corporation\Desktop\Projects\master\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\OneDrive - Dollar General Corporation\Desktop\Projects\master\node_modules\jest-runner\build\runTest.js:444:34)
    at Object.worker (C:\Users\<USER>\OneDrive - Dollar General Corporation\Desktop\Projects\master\node_modules\jest-runner\build\testWorker.js:106:12)</failure>
    </testcase>
    <testcase classname="axiosInstance returns error if refresh fails" name="axiosInstance returns error if refresh fails" time="0.005">
      <failure>Error: expect(received).toBe(expected) // Object.is equality

Expected: 401
Received: undefined
    at Object.toBe (C:\Users\<USER>\OneDrive - Dollar General Corporation\Desktop\Projects\master\src\__tests__\utils\axiosInstance.test.ts:43:26)
    at Promise.then.completed (C:\Users\<USER>\OneDrive - Dollar General Corporation\Desktop\Projects\master\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\OneDrive - Dollar General Corporation\Desktop\Projects\master\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\OneDrive - Dollar General Corporation\Desktop\Projects\master\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (C:\Users\<USER>\OneDrive - Dollar General Corporation\Desktop\Projects\master\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\OneDrive - Dollar General Corporation\Desktop\Projects\master\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\OneDrive - Dollar General Corporation\Desktop\Projects\master\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\OneDrive - Dollar General Corporation\Desktop\Projects\master\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\OneDrive - Dollar General Corporation\Desktop\Projects\master\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\OneDrive - Dollar General Corporation\Desktop\Projects\master\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\OneDrive - Dollar General Corporation\Desktop\Projects\master\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\OneDrive - Dollar General Corporation\Desktop\Projects\master\node_modules\jest-runner\build\runTest.js:444:34)
    at Object.worker (C:\Users\<USER>\OneDrive - Dollar General Corporation\Desktop\Projects\master\node_modules\jest-runner\build\testWorker.js:106:12)</failure>
    </testcase>
  </testsuite>
  <testsuite name="MessageBanner Component" errors="0" failures="0" skipped="0" timestamp="2025-05-22T09:21:48" time="9.532" tests="5">
    <testcase classname="MessageBanner Component renders alert type messages only" name="MessageBanner Component renders alert type messages only" time="0.134">
    </testcase>
    <testcase classname="MessageBanner Component cycles to next and previous alerts" name="MessageBanner Component cycles to next and previous alerts" time="0.285">
    </testcase>
    <testcase classname="MessageBanner Component hides the banner when close icon is clicked" name="MessageBanner Component hides the banner when close icon is clicked" time="0.098">
    </testcase>
    <testcase classname="MessageBanner Component shows read more and expands/collapses description if long" name="MessageBanner Component shows read more and expands/collapses description if long" time="0.051">
    </testcase>
    <testcase classname="MessageBanner Component does not render if there are no alerts of type alert" name="MessageBanner Component does not render if there are no alerts of type alert" time="0.003">
    </testcase>
  </testsuite>
  <testsuite name="AlertsCarousel Component" errors="0" failures="0" skipped="0" timestamp="2025-05-22T09:21:51" time="9.161" tests="5">
    <testcase classname="AlertsCarousel Component renders News and Events sections" name="AlertsCarousel Component renders News and Events sections" time="0.314">
    </testcase>
    <testcase classname="AlertsCarousel Component displays the first news item by default" name="AlertsCarousel Component displays the first news item by default" time="0.074">
    </testcase>
    <testcase classname="AlertsCarousel Component cycles to next news item on clicking forward button" name="AlertsCarousel Component cycles to next news item on clicking forward button" time="0.393">
    </testcase>
    <testcase classname="AlertsCarousel Component displays the event title" name="AlertsCarousel Component displays the event title" time="0.074">
    </testcase>
    <testcase classname="AlertsCarousel Component expands and collapses long description if needed" name="AlertsCarousel Component expands and collapses long description if needed" time="0.164">
    </testcase>
  </testsuite>
  <testsuite name="ChatInterface" errors="0" failures="0" skipped="0" timestamp="2025-05-22T09:21:57" time="5.161" tests="4">
    <testcase classname="ChatInterface renders initial bot message" name="ChatInterface renders initial bot message" time="0.189">
    </testcase>
    <testcase classname="ChatInterface sends a user message and shows fallback response" name="ChatInterface sends a user message and shows fallback response" time="1.446">
    </testcase>
    <testcase classname="ChatInterface shows chart when chart button is clicked" name="ChatInterface shows chart when chart button is clicked" time="0.74">
    </testcase>
    <testcase classname="ChatInterface calls onClose when back arrow is clicked" name="ChatInterface calls onClose when back arrow is clicked" time="0.289">
    </testcase>
  </testsuite>
  <testsuite name="TableActions" errors="0" failures="0" skipped="0" timestamp="2025-05-22T09:21:51" time="11.266" tests="1">
    <testcase classname="TableActions renders favorite and saved buttons" name="TableActions renders favorite and saved buttons" time="0.477">
    </testcase>
  </testsuite>
  <testsuite name="DataCatalogPage" errors="0" failures="0" skipped="0" timestamp="2025-05-22T09:22:00" time="3.287" tests="4">
    <testcase classname="DataCatalogPage should render the DataCatalogPage component with mocked GenericLayout" name="DataCatalogPage should render the DataCatalogPage component with mocked GenericLayout" time="0.568">
    </testcase>
    <testcase classname="DataCatalogPage should render the DataCatalog component" name="DataCatalogPage should render the DataCatalog component" time="0.259">
    </testcase>
    <testcase classname="DataCatalogPage should highlight the selected card and display its details" name="DataCatalogPage should highlight the selected card and display its details" time="0.24">
    </testcase>
    <testcase classname="DataCatalogPage should display the link to Collibra" name="DataCatalogPage should display the link to Collibra" time="0.137">
    </testcase>
  </testsuite>
  <testsuite name="ReportForm" errors="0" failures="0" skipped="0" timestamp="2025-05-22T09:22:03" time="4.496" tests="1">
    <testcase classname="ReportForm renders dialog with form fields" name="ReportForm renders dialog with form fields" time="1.153">
    </testcase>
  </testsuite>
  <testsuite name="ReportTable" errors="0" failures="0" skipped="0" timestamp="2025-05-22T09:22:03" time="6.755" tests="1">
    <testcase classname="ReportTable renders without crashing" name="ReportTable renders without crashing" time="1.455">
    </testcase>
  </testsuite>
  <testsuite name="Login Page" errors="0" failures="0" skipped="0" timestamp="2025-05-22T09:22:07" time="1.858" tests="1">
    <testcase classname="Login Page renders login page contents" name="Login Page renders login page contents" time="0.117">
    </testcase>
  </testsuite>
  <testsuite name="Omnibot Page" errors="0" failures="0" skipped="0" timestamp="2025-05-22T09:22:04" time="6.736" tests="5">
    <testcase classname="Omnibot Page renders the topics heading" name="Omnibot Page renders the topics heading" time="0.821">
    </testcase>
    <testcase classname="Omnibot Page renders the floating new chat button" name="Omnibot Page renders the floating new chat button" time="0.264">
    </testcase>
    <testcase classname="Omnibot Page renders the topics grid" name="Omnibot Page renders the topics grid" time="0.504">
    </testcase>
    <testcase classname="Omnibot Page shows a modal with topic details when a topic is clicked" name="Omnibot Page shows a modal with topic details when a topic is clicked" time="1.799">
    </testcase>
    <testcase classname="Omnibot Page renders a text input field with the correct placeholder in chat mode" name="Omnibot Page renders a text input field with the correct placeholder in chat mode" time="0.981">
    </testcase>
  </testsuite>
  <testsuite name="Report Page" errors="0" failures="0" skipped="0" timestamp="2025-05-22T09:22:09" time="1.695" tests="1">
    <testcase classname="Report Page renders layout, title, header, and report table with correct view" name="Report Page renders layout, title, header, and report table with correct view" time="0.009">
    </testcase>
  </testsuite>
  <testsuite name="ExplorationQuestions" errors="0" failures="0" skipped="0" timestamp="2025-05-22T09:22:10" time="1.895" tests="2">
    <testcase classname="ExplorationQuestions renders all questions" name="ExplorationQuestions renders all questions" time="0.324">
    </testcase>
    <testcase classname="ExplorationQuestions calls onQuestionClick when a question is clicked" name="ExplorationQuestions calls onQuestionClick when a question is clicked" time="0.047">
    </testcase>
  </testsuite>
  <testsuite name="ChatSections" errors="0" failures="0" skipped="0" timestamp="2025-05-22T09:22:11" time="1.733" tests="1">
    <testcase classname="ChatSections renders section titles and chats" name="ChatSections renders section titles and chats" time="0.211">
    </testcase>
  </testsuite>
  <testsuite name="TopicsGrid" errors="0" failures="0" skipped="0" timestamp="2025-05-22T09:22:11" time="3.142" tests="4">
    <testcase classname="TopicsGrid renders topics and heading" name="TopicsGrid renders topics and heading" time="0.401">
    </testcase>
    <testcase classname="TopicsGrid shows modal with topic details and closes it" name="TopicsGrid shows modal with topic details and closes it" time="0.75">
    </testcase>
    <testcase classname="TopicsGrid calls onReadClick when &quot;Ask Omnibot About This Topic&quot; is clicked" name="TopicsGrid calls onReadClick when &quot;Ask Omnibot About This Topic&quot; is clicked" time="0.497">
    </testcase>
    <testcase classname="TopicsGrid shows &quot;No topics available&quot; if topics is empty" name="TopicsGrid shows &quot;No topics available&quot; if topics is empty" time="0.003">
    </testcase>
  </testsuite>
  <testsuite name="Auth Page" errors="0" failures="0" skipped="0" timestamp="2025-05-22T09:22:13" time="2.069" tests="2">
    <testcase classname="Auth Page redirects to home if authenticated" name="Auth Page redirects to home if authenticated" time="0.002">
    </testcase>
    <testcase classname="Auth Page renders login page if not authenticated" name="Auth Page renders login page if not authenticated" time="0.117">
    </testcase>
  </testsuite>
  <testsuite name="OmnibotSidebar" errors="0" failures="0" skipped="0" timestamp="2025-05-22T09:22:11" time="5.119" tests="5">
    <testcase classname="OmnibotSidebar renders chats and explore button" name="OmnibotSidebar renders chats and explore button" time="0.639">
    </testcase>
    <testcase classname="OmnibotSidebar calls onChatSelect when a chat is clicked" name="OmnibotSidebar calls onChatSelect when a chat is clicked" time="0.06">
    </testcase>
    <testcase classname="OmnibotSidebar shows rename dialog and calls onRenameChat" name="OmnibotSidebar shows rename dialog and calls onRenameChat" time="1.44">
    </testcase>
    <testcase classname="OmnibotSidebar shows delete dialog and calls onDeleteChat" name="OmnibotSidebar shows delete dialog and calls onDeleteChat" time="1.149">
    </testcase>
    <testcase classname="OmnibotSidebar renders settings and sign out buttons" name="OmnibotSidebar renders settings and sign out buttons" time="0.049">
    </testcase>
  </testsuite>
  <testsuite name="DeleteConfirmDialog" errors="0" failures="0" skipped="0" timestamp="2025-05-22T09:22:15" time="1.605" tests="1">
    <testcase classname="DeleteConfirmDialog renders and calls onConfirm" name="DeleteConfirmDialog renders and calls onConfirm" time="0.193">
    </testcase>
  </testsuite>
  <testsuite name="helper utility functions" errors="0" failures="0" skipped="0" timestamp="2025-05-22T09:22:16" time="0.734" tests="9">
    <testcase classname="helper utility functions normalizeDate returns ISO date string for valid date" name="helper utility functions normalizeDate returns ISO date string for valid date" time="0.002">
    </testcase>
    <testcase classname="helper utility functions normalizeDate returns empty string for null" name="helper utility functions normalizeDate returns empty string for null" time="0.004">
    </testcase>
    <testcase classname="helper utility functions checkIsAdmin returns true for ADMIN_ROLE" name="helper utility functions checkIsAdmin returns true for ADMIN_ROLE" time="0.01">
    </testcase>
    <testcase classname="helper utility functions checkIsAdmin returns true for SUPER_ADMIN" name="helper utility functions checkIsAdmin returns true for SUPER_ADMIN" time="0.001">
    </testcase>
    <testcase classname="helper utility functions checkIsAdmin returns false for other roles" name="helper utility functions checkIsAdmin returns false for other roles" time="0.001">
    </testcase>
    <testcase classname="helper utility functions checkIsAdmin returns false for undefined user" name="helper utility functions checkIsAdmin returns false for undefined user" time="0">
    </testcase>
    <testcase classname="helper utility functions checkRole returns true if user has the role" name="helper utility functions checkRole returns true if user has the role" time="0">
    </testcase>
    <testcase classname="helper utility functions checkRole returns false if user does not have the role" name="helper utility functions checkRole returns false if user does not have the role" time="0.001">
    </testcase>
    <testcase classname="helper utility functions formatDateTime formats date string as expected" name="helper utility functions formatDateTime formats date string as expected" time="0.003">
    </testcase>
  </testsuite>
  <testsuite name="AuthLogin Component" errors="0" failures="0" skipped="0" timestamp="2025-05-22T09:22:15" time="1.652" tests="3">
    <testcase classname="AuthLogin Component renders login button and subtitle" name="AuthLogin Component renders login button and subtitle" time="0.123">
    </testcase>
    <testcase classname="AuthLogin Component calls signIn on click" name="AuthLogin Component calls signIn on click" time="0.094">
    </testcase>
    <testcase classname="AuthLogin Component disables button while loading" name="AuthLogin Component disables button while loading" time="0.036">
    </testcase>
  </testsuite>
  <testsuite name="TopicCard" errors="0" failures="0" skipped="0" timestamp="2025-05-22T09:22:16" time="1.484" tests="2">
    <testcase classname="TopicCard renders title and description" name="TopicCard renders title and description" time="0.033">
    </testcase>
    <testcase classname="TopicCard calls onReadClick when button is clicked" name="TopicCard calls onReadClick when button is clicked" time="0.046">
    </testcase>
  </testsuite>
  <testsuite name="BulkUploadReports" errors="0" failures="0" skipped="0" timestamp="2025-05-22T09:22:16" time="1.546" tests="1">
    <testcase classname="BulkUploadReports renders upload button" name="BulkUploadReports renders upload button" time="0.085">
    </testcase>
  </testsuite>
  <testsuite name="ChatMessage" errors="0" failures="0" skipped="0" timestamp="2025-05-22T09:22:17" time="1.448" tests="3">
    <testcase classname="ChatMessage renders user message" name="ChatMessage renders user message" time="0.014">
    </testcase>
    <testcase classname="ChatMessage renders bot message" name="ChatMessage renders bot message" time="0.006">
    </testcase>
    <testcase classname="ChatMessage renders markdown headings and bullets" name="ChatMessage renders markdown headings and bullets" time="0.027">
    </testcase>
  </testsuite>
  <testsuite name="Landing Loading" errors="0" failures="0" skipped="0" timestamp="2025-05-22T09:22:18" time="0.474" tests="1">
    <testcase classname="Landing Loading renders Loader" name="Landing Loading renders Loader" time="0.008">
    </testcase>
  </testsuite>
  <testsuite name="DownloadTemplate" errors="0" failures="0" skipped="0" timestamp="2025-05-22T09:22:18" time="1.412" tests="1">
    <testcase classname="DownloadTemplate renders download button" name="DownloadTemplate renders download button" time="0.024">
    </testcase>
  </testsuite>
  <testsuite name="FeatureCard" errors="0" failures="0" skipped="0" timestamp="2025-05-22T09:22:18" time="1.511" tests="3">
    <testcase classname="FeatureCard renders with internal link when title is not &quot;Self-Service&quot;" name="FeatureCard renders with internal link when title is not &quot;Self-Service&quot;" time="0.063">
    </testcase>
    <testcase classname="FeatureCard renders with external link when title is &quot;Self-Service&quot;" name="FeatureCard renders with external link when title is &quot;Self-Service&quot;" time="0.011">
    </testcase>
    <testcase classname="FeatureCard renders the correct icon image" name="FeatureCard renders the correct icon image" time="0.01">
    </testcase>
  </testsuite>
</testsuites>