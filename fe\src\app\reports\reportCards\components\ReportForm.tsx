'use client'

import { setRefreshFlag } from '@/app/reports/slice'
import { Report } from '@/app/reports/types'
import { SingleDatePicker } from '@/components/common/utility/DatePicker'
import { useAppDispatch, useAppSelector } from '@/store/hooks'
import { SOURCE_OPTIONS } from '@/utils/constants'
import { snackbar } from '@/utils/toast'
import {
    Box,
    Button,
    Checkbox,
    CircularProgress,
    Dialog,
    DialogActions,
    DialogContent,
    DialogTitle,
    Grid2 as Grid,
    IconButton,
    InputAdornment,
    MenuItem,
    TextField,
} from '@mui/material'
import { useSession } from 'next-auth/react'
import { ChangeEvent, useMemo, useState } from 'react'
import { getLookerReportMetaData, getPBIReportMetaData, upsertReport } from '../../services'
import ClearIcon from '@mui/icons-material/Clear'
import { Form, Formik } from 'formik'
import { ErrorBox, ReportSchema } from '@/app/reports/components/Utils'

interface ReportFormProps {
    open: boolean
    onClose: (event?: React.SyntheticEvent | object, reason?: 'backdropClick' | 'escapeKeyDown') => void
    report?: Report
}

export default function ReportForm({ open, onClose, report }: Readonly<ReportFormProps>) {
    const { filterOptions } = useAppSelector((store) => store.filterOptions) ?? {}
    const { domains = [] } = filterOptions
    const { data } = useSession()
    const dispatch = useAppDispatch()
    const [reportUrl, setReportUrl] = useState('')
    const [loading, setLoading] = useState(false)
    const [date, setDate] = useState<Date | null>(report?.published_on ? new Date(report.published_on) : null)
    const [hasUrl, setHasUrl] = useState(false)

    const initialValues = useMemo(
        () => ({
            source_system: report?.source_system ?? SOURCE_OPTIONS.PowerBI,
            name: report?.name ?? '',
            domain_id: report?.domain_id?.toString() ?? '',
            category: report?.category ?? '',
            description: report?.description ?? '',
            report_id: report?.report_id ?? '',
            group_id: report?.group_id ?? '',
        }),
        [report],
    )

    const buttonLabel = useMemo(() => {
        if (loading) return <CircularProgress size={25} color="inherit" />
        return report ? 'Update' : 'Submit'
    }, [loading, report])

    const handleFetch = async (setFieldValue: any, setTouched: any) => {
        if (!reportUrl.trim()) {
            snackbar.error('URL is empty!')
            return
        }

        if (reportUrl.includes('pbi_source=linkShare')) {
            snackbar.error('Please provide a direct Power BI report link, not a shared link.')
            snackbar.error(<ErrorBox />)
            return
        }

        const powerBiRegex = /groups\/([^/]+)\/reports\/([^?/]+)/
        const lookerRegex = /\/embed\/dashboards\/(\d+)/
        const powerBiMatch = RegExp(powerBiRegex).exec(reportUrl)
        const lookerMatch = RegExp(lookerRegex).exec(reportUrl)

        setLoading(true)
        try {
            if (powerBiMatch) {
                const [, groupId, reportId] = powerBiMatch
                const response = await getPBIReportMetaData(reportId, groupId)
                setFieldValue('source_system', SOURCE_OPTIONS.PowerBI)
                setFieldValue('name', response.data.name)
                setFieldValue('report_id', response.data.id)
                setFieldValue('group_id', groupId)
                setFieldValue('description', response.data.description)
                setTouched({ name: false, report_id: false, group_id: false })
            } else if (lookerMatch) {
                const dashboardId = lookerMatch[1]
                const response = await getLookerReportMetaData(dashboardId)
                setFieldValue('source_system', SOURCE_OPTIONS.Looker)
                setFieldValue('name', response.data.title)
                setFieldValue('report_id', response.data.id)
                setFieldValue('description', response.data.description)
                setFieldValue('group_id', '')
                setTouched({ name: false, report_id: false, group_id: false })
            } else {
                snackbar.error('Invalid report URL!')
            }
        } catch (err: any) {
            snackbar.error(err.message)
        } finally {
            setLoading(false)
        }
    }

    const handleSubmit = async (values: typeof initialValues) => {
        setLoading(true)
        try {
            const payload: Omit<Report, 'id'> & { id?: string } = {
                ...values,
                domain_id: Number(values.domain_id),
                group_id: values.group_id || '',
                ...(report?.id ? { id: report?.id } : {}),
                published_on: date ? new Date(date.setHours(12, 0, 0, 0)) : null,
                created_by: data?.user?.email ?? 'Unknown',
                is_favorite: report?.is_favorite ?? false,
                is_saved: report?.is_saved ?? false,
                created_at: report?.updated_at ?? new Date().toISOString(),
                updated_at: report?.updated_at ?? new Date().toISOString(),
            }

            await upsertReport(payload)
            snackbar.success(`Report ${report?.id ? 'updated' : 'added'} successfully`)
            onClose()
            dispatch(setRefreshFlag(true))
        } catch (error: any) {
            snackbar.error(`Failed to ${report ? 'update' : 'add'} report: ${error.message}`)
        } finally {
            setLoading(false)
        }
    }

    const handleToggleUrl = (e: ChangeEvent<HTMLInputElement>, resetForm: any) => {
        setHasUrl(e.target.checked)
        resetForm({ values: initialValues })
        setReportUrl('')
    }

    return (
        <Dialog
            open={open}
            onClose={(event, reason) => onClose(event, reason)}
            maxWidth="md"
            onClick={(e) => e.stopPropagation()}
        >
            <DialogTitle>{report ? 'Edit Report' : 'Add Report'}</DialogTitle>
            <Formik
                initialValues={initialValues}
                validationSchema={ReportSchema}
                onSubmit={handleSubmit}
                enableReinitialize
            >
                {({ values, errors, touched, handleChange, setFieldValue, setTouched, resetForm }) => (
                    <Form>
                        <DialogContent>
                            <Grid container spacing={2} sx={{ mt: 0 }}>
                                <Grid size={{ xs: 12 }}>
                                    <Checkbox
                                        sx={{ pl: 0 }}
                                        checked={hasUrl}
                                        color="secondary"
                                        onChange={(e) => handleToggleUrl(e, resetForm)}
                                        slotProps={{
                                            input: {
                                                'aria-label': 'controlled',
                                            },
                                        }}
                                    />
                                    I have a report URL
                                    {hasUrl && (
                                        <Box display="flex" gap={1}>
                                            <TextField
                                                name="report_url"
                                                label="Report URL"
                                                placeholder="Please input report URL to fetch report metadata"
                                                fullWidth
                                                size="small"
                                                value={reportUrl}
                                                onChange={(e) => setReportUrl(e.target.value)}
                                                color="secondary"
                                                slotProps={{
                                                    input: {
                                                        endAdornment: reportUrl && (
                                                            <InputAdornment position="end">
                                                                <IconButton
                                                                    size="small"
                                                                    onClick={() => {
                                                                        setReportUrl('')
                                                                        resetForm()
                                                                    }}
                                                                    edge="end"
                                                                >
                                                                    <ClearIcon fontSize="small" />
                                                                </IconButton>
                                                            </InputAdornment>
                                                        ),
                                                    },
                                                }}
                                            />
                                            <Button
                                                onClick={() => handleFetch(setFieldValue, setTouched)}
                                                variant="outlined"
                                                color="success"
                                                loading={loading}
                                                size="small"
                                                sx={{ minWidth: '110px', height: '37px', fontWeight: 400 }}
                                            >
                                                Fetch Details
                                            </Button>
                                        </Box>
                                    )}
                                </Grid>

                                <Grid size={{ xs: 12, sm: 6 }}>
                                    <TextField
                                        select
                                        fullWidth
                                        size="small"
                                        name="source_system"
                                        label="Source System *"
                                        disabled={hasUrl}
                                        value={values.source_system}
                                        onChange={handleChange}
                                        error={!!(touched.source_system && errors.source_system)}
                                        helperText={touched.source_system && errors.source_system}
                                        color="secondary"
                                    >
                                        {Object.values(SOURCE_OPTIONS).map((option) => (
                                            <MenuItem key={option} value={option}>
                                                {option}
                                            </MenuItem>
                                        ))}
                                    </TextField>
                                </Grid>

                                <Grid size={{ xs: 12, sm: 6 }}>
                                    <TextField
                                        fullWidth
                                        size="small"
                                        name="name"
                                        label="Name *"
                                        value={values.name}
                                        onChange={handleChange}
                                        error={!!(touched.name && errors.name)}
                                        helperText={touched.name && errors.name}
                                        color="secondary"
                                    />
                                </Grid>

                                <Grid size={{ xs: 12, sm: 6 }}>
                                    <TextField
                                        select
                                        fullWidth
                                        size="small"
                                        name="domain_id"
                                        label="Domain *"
                                        value={values.domain_id}
                                        onChange={handleChange}
                                        error={!!(touched.domain_id && errors.domain_id)}
                                        helperText={touched.domain_id && errors.domain_id}
                                        color="secondary"
                                    >
                                        {domains.map((domain) => (
                                            <MenuItem key={domain.id} value={domain.id}>
                                                {domain.name}
                                            </MenuItem>
                                        ))}
                                    </TextField>
                                </Grid>

                                <Grid size={{ xs: 12, sm: 6 }}>
                                    <TextField
                                        fullWidth
                                        size="small"
                                        name="category"
                                        label="Category *"
                                        value={values.category}
                                        onChange={handleChange}
                                        error={!!(touched.category && errors.category)}
                                        helperText={touched.category && errors.category}
                                        color="secondary"
                                    />
                                </Grid>

                                <Grid size={{ xs: 12 }}>
                                    <TextField
                                        fullWidth
                                        size="small"
                                        multiline
                                        rows={2}
                                        name="description"
                                        label="Description"
                                        value={values.description}
                                        onChange={handleChange}
                                        error={!!(touched.description && errors.description)}
                                        helperText={touched.description && errors.description}
                                        color="secondary"
                                    />
                                </Grid>

                                <Grid size={{ xs: 12, sm: 6 }}>
                                    <TextField
                                        fullWidth
                                        size="small"
                                        name="report_id"
                                        label="Report ID *"
                                        value={values.report_id}
                                        disabled={hasUrl}
                                        onChange={handleChange}
                                        color="secondary"
                                        error={!!(touched.report_id && errors.report_id)}
                                        helperText={touched.report_id && errors.report_id}
                                    />
                                </Grid>

                                {values.source_system === SOURCE_OPTIONS.PowerBI && (
                                    <Grid size={{ xs: 12, sm: 6 }}>
                                        <TextField
                                            fullWidth
                                            size="small"
                                            name="group_id"
                                            label="Group ID *"
                                            value={values.group_id}
                                            disabled={hasUrl}
                                            onChange={handleChange}
                                            color="secondary"
                                            error={!!(touched.group_id && errors.group_id)}
                                            helperText={touched.group_id && errors.group_id}
                                        />
                                    </Grid>
                                )}

                                <Grid size={{ xs: 12, sm: 12 }}>
                                    <SingleDatePicker
                                        name="published_on"
                                        value={date}
                                        onChange={setDate}
                                        label="Report Published"
                                    />
                                </Grid>
                            </Grid>
                        </DialogContent>

                        <DialogActions
                            sx={{ position: 'sticky', bottom: 0, zIndex: 1, backgroundColor: 'background.paper' }}
                        >
                            <Button onClick={onClose} variant="outlined" color="error" size="small" disabled={loading}>
                                Cancel
                            </Button>
                            <Button type="submit" variant="outlined" color="secondary" size="small" disabled={loading}>
                                {buttonLabel}
                            </Button>
                        </DialogActions>
                    </Form>
                )}
            </Formik>
        </Dialog>
    )
}
