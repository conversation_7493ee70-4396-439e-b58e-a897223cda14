'use client'

import React, { ReactNode } from 'react'
import { useApiStatus } from '@/contexts/ApiStatusContext'

interface ApiStatusWrapperProps {
  children: ReactNode
  fallback?: ReactNode
  skipCheck?: boolean
}

/**
 * A wrapper component that shows an error page when the API is unavailable
 *
 * @param children - The content to render when the API is available
 * @param fallback - Optional custom fallback UI when API is unavailable
 * @param skipCheck - If true, will not check API status and always render children
 */
const ApiStatusWrapper: React.FC<ApiStatusWrapperProps> = ({
  children,
  fallback,
  skipCheck = false
}) => {
  const { isApiAvailable } = useApiStatus()

  // If skipCheck is true or the API is available, render the children
  if (skipCheck || isApiAvailable) {
    return <>{children}</>
  }

  // Otherwise, redirect to the API error page
  if (typeof window !== 'undefined') {
    window.location.href = '/error/api';
    return null;
  }

  // This will only render during SSR or if the redirect fails
  return <>{fallback || <div>API is unavailable</div>}</>
}

export default ApiStatusWrapper
