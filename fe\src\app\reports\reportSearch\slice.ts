import { IFilterOptions } from '@/app/reports/reportSearch/types'
import { createAsyncThunk, createSlice } from '@reduxjs/toolkit'
import { Domain } from '../types'

// Add a new state for domains
interface FilterOptionsState {
    filterOptions: IFilterOptions
    domains: Domain[]
    loading: 'idle' | 'pending' | 'succeeded' | 'failed'
    error: string | null
}

const initialState: FilterOptionsState = {
    filterOptions: {
        category: [],
        domains: [],
        source_system: [],
    },
    domains: [],
    loading: 'idle',
    error: null,
}

// Async thunk for fetching filter options
export const fetchFilterOptions = createAsyncThunk(
    'filterOptions/fetchFilterOptions',
    async (arg: string, { rejectWithValue }) => {
        try {
            // Import your service function
            const { getFilterOptionsService } = await import('@/app/reports/services')

            // Call the service
            return await getFilterOptionsService()
        } catch (error) {
            return rejectWithValue(error instanceof Error ? error.message : 'An error occurred')
        }
    },
)

// Async thunk for fetching all domains
export const fetchAllDomains = createAsyncThunk('filterOptions/fetchAllDomains', async (_, { rejectWithValue }) => {
    try {
        // Import your service function
        const { getAllDomainsService } = await import('@/app/reports/services')

        // Call the service
        const domains = await getAllDomainsService()

        return domains.data
    } catch (error) {
        console.log('error in fetchAllDomains', error)
        return rejectWithValue(error instanceof Error ? error.message : 'An error occurred')
    }
})

// Create the filter options slice
const filterOptionsSlice = createSlice({
    name: 'filterOptions',
    initialState,
    reducers: {
        // No additional reducers needed as this is primarily for fetching data
    },
    extraReducers: (builder) => {
        builder
            .addCase(fetchFilterOptions.pending, (state) => {
                state.loading = 'pending'
            })
            .addCase(fetchFilterOptions.fulfilled, (state, action) => {
                state.loading = 'succeeded'
                state.filterOptions = action.payload
            })
            .addCase(fetchFilterOptions.rejected, (state, action) => {
                state.loading = 'failed'
                state.error = action.payload as string
            })
            .addCase(fetchAllDomains.pending, (state) => {
                state.loading = 'pending'
            })
            .addCase(fetchAllDomains.fulfilled, (state, action) => {
                state.loading = 'succeeded'
                state.domains = action.payload
            })
            .addCase(fetchAllDomains.rejected, (state, action) => {
                state.loading = 'failed'
                state.error = action.payload as string
            })
    },
})

export default filterOptionsSlice.reducer
