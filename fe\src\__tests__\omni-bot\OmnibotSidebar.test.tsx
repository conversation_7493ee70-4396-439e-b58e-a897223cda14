import '@testing-library/jest-dom'
import { fireEvent, render, screen, waitFor } from '@testing-library/react'
import OmnibotSidebar from '../../app/omni-bot/components/OmnibotSidebar'
import { Chat } from '../../app/omni-bot/types'

const chats: Chat[] = [
    { id: 'c1', title: 'Chat 1' },
    { id: 'c2', title: 'Chat 2' },
    { id: 'c3', title: 'Chat 3' },
    { id: 'c4', title: 'Chat 4' },
]

describe('OmnibotSidebar', () => {
    it('renders chats and explore button', () => {
        render(
            <OmnibotSidebar
                isMobile={false}
                isOpen={true}
                onClose={() => {}}
                onChatSelect={() => {}}
                onNewChat={() => {}}
                onExplore={() => {}}
                chats={chats}
            />,
        )
        expect(screen.getByText('EXPLORE AI DNG')).toBeInTheDocument()
        expect(screen.getByText('Chat 1')).toBeInTheDocument()
        expect(screen.getByText('Chat 4')).toBeInTheDocument()
    })

    it('calls onChatSelect when a chat is clicked', () => {
        const onChatSelect = jest.fn()
        render(
            <OmnibotSidebar
                isMobile={false}
                isOpen={true}
                onClose={() => {}}
                onChatSelect={onChatSelect}
                onNewChat={() => {}}
                onExplore={() => {}}
                chats={chats}
            />,
        )
        fireEvent.click(screen.getByText('Chat 2'))
        expect(onChatSelect).toHaveBeenCalledWith('Chat 2')
    })

    it('shows rename dialog and calls onRenameChat', async () => {
        const onRenameChat = jest.fn()
        render(
            <OmnibotSidebar
                isMobile={false}
                isOpen={true}
                onClose={() => {}}
                onChatSelect={() => {}}
                onNewChat={() => {}}
                onExplore={() => {}}
                onRenameChat={onRenameChat}
                chats={chats}
            />,
        )
        // Find all icon buttons (the three-dot menu for each chat)
        const iconButtons = screen
            .getAllByRole('button')
            .filter(
                (btn) =>
                    btn.className.includes('MuiIconButton-root') &&
                    btn.querySelector('svg[data-testid="MoreVertIcon"]'),
            )
        fireEvent.click(iconButtons[0])
        // Click Rename
        await waitFor(() => {
            expect(screen.getByText(/Rename/i)).toBeInTheDocument()
        })
        fireEvent.click(screen.getByText(/Rename/i))
        // Change title and save
        const input = screen.getByLabelText(/Chat Name/i)
        fireEvent.change(input, { target: { value: 'New Title' } })
        fireEvent.click(screen.getByRole('button', { name: /Save/i }))
        expect(onRenameChat).toHaveBeenCalledWith('c1', 'New Title')
    })

    it('shows delete dialog and calls onDeleteChat', async () => {
        const onDeleteChat = jest.fn()
        render(
            <OmnibotSidebar
                isMobile={false}
                isOpen={true}
                onClose={() => {}}
                onChatSelect={() => {}}
                onNewChat={() => {}}
                onExplore={() => {}}
                onDeleteChat={onDeleteChat}
                chats={chats}
            />,
        )
        // Find all icon buttons (the three-dot menu for each chat)
        const iconButtons = screen
            .getAllByRole('button')
            .filter(
                (btn) =>
                    btn.className.includes('MuiIconButton-root') &&
                    btn.querySelector('svg[data-testid="MoreVertIcon"]'),
            )
        fireEvent.click(iconButtons[0])
        // Click Delete
        await waitFor(() => {
            expect(screen.getByText(/Delete/i)).toBeInTheDocument()
        })
        fireEvent.click(screen.getByText(/Delete/i))
        // Confirm delete
        fireEvent.click(screen.getByRole('button', { name: /Delete/i }))
        expect(onDeleteChat).toHaveBeenCalledWith('c1')
    })

    it('renders settings and sign out buttons', () => {
        render(
            <OmnibotSidebar
                isMobile={false}
                isOpen={true}
                onClose={() => {}}
                onChatSelect={() => {}}
                onNewChat={() => {}}
                onExplore={() => {}}
                chats={chats}
            />,
        )
        expect(screen.getByText('Settings')).toBeInTheDocument()
        expect(screen.getByText('Sign out')).toBeInTheDocument()
    })
})
