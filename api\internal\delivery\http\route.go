package http

import (
	"dng-module/config"
	"dng-module/internal/delivery/http/catalogReportHandler"
	"dng-module/internal/delivery/http/errorHandler"
	"dng-module/internal/delivery/http/healthHandler"
	newsAlertServiceHandler "dng-module/internal/delivery/http/newsAlertServiceHandler"
	"dng-module/internal/delivery/http/userHandler"
	"dng-module/internal/utils"
	"net/http"
	"time"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
)

func SetupRoutes(router *gin.Engine, cfg config.Config) {
	router.Use(cors.New(cors.Config{
		AllowOrigins:     []string{"*"}, // or restrict to ["http://localhost:3000"] etc.
		AllowMethods:     []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
		AllowHeaders:     []string{"Origin", "Content-Type", "Authorization"},
		ExposeHeaders:    []string{"Content-Length"},
		AllowCredentials: true,
		MaxAge:           12 * time.Hour,
	}))

	const alertsPath = "/alerts"

	// Health check endpoints - no authentication required
	router.GET("/health/live", healthHandler.HealthLiveHandler)
	router.HEAD("/health/live", healthHandler.HealthLiveHandler)
	router.GET("/health/ready", healthHandler.HealthReadyHandler)
	router.HEAD("/health/ready", healthHandler.HealthReadyHandler)
	router.GET("/health/info", healthHandler.HealthInfoHandler)

	// Test error endpoints - no authentication required
	// These endpoints return appropriate status codes for the frontend to handle
	errHandler := errorHandler.NewErrorHandler()
	router.GET("/test/error/404", func(c *gin.Context) {
		errHandler.Handle404(c)
	})
	router.GET("/test/error/500", func(c *gin.Context) {
		errHandler.Handle500(c)
	})
	router.GET("/test/error/403", func(c *gin.Context) {
		errHandler.Handle403(c)
	})
	router.GET("/test/error/:code", func(c *gin.Context) {
		code := c.Param("code")
		statusCode := http.StatusInternalServerError
		switch code {
		case "400":
			statusCode = http.StatusBadRequest
		case "401":
			statusCode = http.StatusUnauthorized
		case "403":
			statusCode = http.StatusForbidden
		case "404":
			statusCode = http.StatusNotFound
		case "500":
			statusCode = http.StatusInternalServerError
		}
		errHandler.HandleError(c, statusCode)
	})

	// API endpoints - require authentication
	api := router.Group("/dng/api")
	api.Use(utils.JWTMiddleware())

	{
		// Reports
		api.POST("/reports/upsert", catalogReportHandler.CreateReportHandler)        // Create or update a report
		api.POST("/reports", catalogReportHandler.GetReportsHandler)                 // Fetch reports (use GET for fetching)
		api.POST("/reports/search", catalogReportHandler.GetSearchOptionsHandler)    // Get search options
		api.POST("/reports/user-preferences", catalogReportHandler.UpdateIsFavorite) // Update user preferences
		api.GET("/reports/filter-options", catalogReportHandler.GetFilterOptionsHandler)
		api.DELETE("/reports", catalogReportHandler.DeleteReportsHandler)

		//PowerBI and looker
		api.GET("/reports/pbi/embed-token", func(c *gin.Context) {
			catalogReportHandler.GetPowerBIEmbedToken(c, cfg)
		})
		api.GET("/reports/pbi/metadata", func(c *gin.Context) {
			catalogReportHandler.GetPowerBIMetaHandler(c, cfg)
		})

		api.GET("/reports/looker/metadata", func(c *gin.Context) {
			catalogReportHandler.LookerPDFHandler(c, cfg)
		})

		api.POST("/reports/looker/render-pdf", func(c *gin.Context) {
			catalogReportHandler.LookerPDFHandler(c, cfg)
		})
		// api.GET("/reports/looker/metadata", catalogReportHandler.GetLookerMetaHandler)
		// api.POST("/reports/looker/render-pdf", catalogReportHandler.LookerPDFHandler)
		api.GET("/reports/looker/looker-embed", func(c *gin.Context) {
			catalogReportHandler.LookerEmbedHandler(c, cfg)
		})

		api.GET("/reports/tableau/token", func(c *gin.Context) {
			catalogReportHandler.TableauTokenHandler(c, cfg)
		})
		// api.GET("/reports/looker/looker-embed", catalogReportHandler.LookerEmbedHandler)
		// api.GET("/reports/tableau/token", catalogReportHandler.TableauTokenHandler)

		// Report Templates & Downloads
		api.POST("/reports/bulk-insert", func(c *gin.Context) {
			catalogReportHandler.BulkCreateReportsHandler(c, cfg)
		})
		api.GET("/reports/bulk-insert-errors", catalogReportHandler.DownloadErrorFileHandler)    // Endpoint to download the error file generated during bulk upload
		api.GET("/reports/download-template", catalogReportHandler.DownloadExcelTemplateHandler) // Download Excel template

		// Users
		api.POST("/user/upsert", userHandler.PostUserUpsertHandler) // Create or update user

		// Domains
		api.POST("/domains", userHandler.GetAllDomainsHandler) // Fetch all domains

		api.POST(alertsPath, newsAlertServiceHandler.CreateNewsAlertHandler)
		api.DELETE(alertsPath, newsAlertServiceHandler.DeleteNewsAlertsHandler)
		api.PUT(alertsPath, newsAlertServiceHandler.UpdateNewsAlertHandler)
		api.GET(alertsPath, newsAlertServiceHandler.GetNewsAlertsHandler)

		api.GET("/alerts/visible", newsAlertServiceHandler.GetVisibleNewsAlertsHandler)

	}
}
