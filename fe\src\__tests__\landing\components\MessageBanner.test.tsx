import { render, screen, fireEvent } from '@testing-library/react'
import MessageBanner from '@/app/(landing)/components/MessageBanner'
import { IAlert } from '@/app/alerts/types'

const alerts: IAlert[] = [
    {
        id: 1,
        type: 'alert',
        title: 'Urgent Alert',
        description: 'This is an important alert message.',
        start_date_time: new Date(),
        end_date_time: null,
        is_visible: true,
        status: 'active',
        created_by: 'Admin',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
    },
    {
        id: 2,
        type: 'alert',
        title: 'Second Alert',
        description: 'Second alert message goes here.',
        start_date_time: new Date(),
        end_date_time: null,
        is_visible: true,
        status: 'active',
        created_by: 'Admin',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
    },
    {
        id: 3,
        type: 'news',
        title: 'Ignore This',
        description: 'This is a news item and should not show up.',
        start_date_time: new Date(),
        end_date_time: null,
        is_visible: true,
        status: 'active',
        created_by: 'Admin',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
    },
]

describe('MessageBanner Component', () => {
    it('renders alert type messages only', () => {
        render(<MessageBanner alerts={alerts} />)

        expect(screen.getByText('Urgent Alert')).toBeInTheDocument()
        expect(screen.queryByText('Ignore This')).not.toBeInTheDocument()
    })

    it('cycles to next and previous alerts', () => {
        render(<MessageBanner alerts={alerts} />)

        // Initially shows first alert
        expect(screen.getByText('Urgent Alert')).toBeInTheDocument()

        const nextButton = screen.getAllByRole('button').find((btn) => btn.innerHTML.includes('ArrowForwardIos'))!
        fireEvent.click(nextButton)

        expect(screen.getByText('Second Alert')).toBeInTheDocument()

        const prevButton = screen.getAllByRole('button').find((btn) => btn.innerHTML.includes('ArrowBackIos'))!
        fireEvent.click(prevButton)

        expect(screen.getByText('Urgent Alert')).toBeInTheDocument()
    })

    it('hides the banner when close icon is clicked', () => {
        render(<MessageBanner alerts={alerts} />)

        const closeBtn = screen.getAllByRole('button').find((btn) => btn.innerHTML.includes('CloseIcon'))!
        fireEvent.click(closeBtn)

        expect(screen.queryByText('Urgent Alert')).not.toBeInTheDocument()
    })

    it('shows read more and expands/collapses description if long', () => {
        const longDescriptionAlert = {
            ...alerts[0],
            description: 'This is a very long description '.repeat(10), // > 100 chars
        }

        render(<MessageBanner alerts={[longDescriptionAlert]} />)

        const readMore = screen.getByText('Read more')
        expect(readMore).toBeInTheDocument()

        fireEvent.click(readMore)
        expect(screen.getByText('Show less')).toBeInTheDocument()
    })

    it('does not render if there are no alerts of type alert', () => {
        const noAlerts: IAlert[] = [
            {
                ...alerts[0],
                type: 'news',
            },
        ]
        render(<MessageBanner alerts={noAlerts} />)
        expect(screen.queryByText('Urgent Alert')).not.toBeInTheDocument()
    })
})
