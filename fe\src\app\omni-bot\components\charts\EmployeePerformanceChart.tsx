'use client'
import React, { useState } from 'react'
import dynamic from 'next/dynamic'
import { Box, Typography, Paper, ToggleButtonGroup, ToggleButton } from '@mui/material'

// Dynamically import ApexCharts to avoid SSR issues
const Chart = dynamic(() => import('react-apexcharts'), { ssr: false })

const EmployeePerformanceChart: React.FC = () => {
    const [chartType, setChartType] = useState<'employees' | 'errors'>('employees')

    // Chart options for Employee Audit Accuracy
    const employeeOptions = {
        chart: {
            id: 'employee-audit-accuracy',
            toolbar: {
                show: false
            },
            fontFamily: 'Arial, sans-serif',
            animations: {
                enabled: true,
                easing: 'easeinout',
                speed: 800
            }
        },
        colors: ['#FFC107'],
        plotOptions: {
            bar: {
                horizontal: false,
                columnWidth: '70%',
                borderRadius: 4,
                distributed: true,
                dataLabels: {
                    position: 'top'
                }
            }
        },
        dataLabels: {
            enabled: true,
            formatter: function(val: number) {
                return val.toFixed(1) + '%';
            },
            style: {
                fontSize: '10px',
                colors: ['#333']
            },
            offsetY: -20
        },
        xaxis: {
            categories: ['<PERSON>, <PERSON>.', '<PERSON>, R.', '<PERSON>, <PERSON>.', '<PERSON>, <PERSON>.', '<PERSON>, <PERSON>.', '<PERSON>, S.', '<PERSON>, A.', '<PERSON>, L.', 'Wilson, P.', '<PERSON>, C.'],
            labels: {
                style: {
                    colors: '#616161',
                    fontSize: '11px',
                    fontFamily: 'Arial, sans-serif',
                    rotate: -45
                }
            },
            title: {
                text: 'Employees',
                style: {
                    color: '#616161',
                    fontSize: '12px',
                    fontFamily: 'Arial, sans-serif',
                }
            }
        },
        yaxis: {
            title: {
                text: 'Audit Accuracy (%)',
                style: {
                    color: '#616161',
                    fontSize: '12px',
                    fontFamily: 'Arial, sans-serif',
                    fontWeight: 'bold'
                }
            },
            labels: {
                formatter: function(val: number) {
                    return val.toFixed(1) + '%';
                },
                style: {
                    colors: '#616161',
                    fontSize: '12px',
                    fontFamily: 'Arial, sans-serif',
                }
            },
            min: 84,
            max: 100
        },
        tooltip: {
            theme: 'light',
            y: {
                formatter: function(val: number) {
                    return val.toFixed(1) + '%';
                }
            }
        },
        legend: {
            show: false
        },
        grid: {
            borderColor: '#f1f1f1',
            row: {
                colors: ['transparent', 'transparent'],
                opacity: 0.5
            }
        },
        annotations: {
            yaxis: [
                {
                    y: 95,
                    yAxisIndex: 0,
                    borderColor: '#FFC107',
                    borderWidth: 2,
                    label: {
                        borderColor: '#FFC107',
                        style: {
                            color: '#000',
                            background: '#FFC107',
                            fontSize: '10px',
                            fontFamily: 'Arial, sans-serif',
                            fontWeight: 'bold'
                        },
                        text: 'Target: 95%'
                    }
                }
            ]
        }
    };

    // Chart options for Error Rates by Shift and Zone
    const errorOptions = {
        chart: {
            id: 'error-rates-shift-zone',
            type: 'heatmap',
            toolbar: {
                show: false
            },
            fontFamily: 'Arial, sans-serif',
            animations: {
                enabled: true,
                easing: 'easeinout',
                speed: 800
            }
        },
        dataLabels: {
            enabled: true,
            formatter: function(val: number) {
                return val.toFixed(1) + '%';
            },
            style: {
                fontSize: '10px',
                colors: ['#000']
            }
        },
        colors: ['#FFC107'],
        xaxis: {
            categories: ['Zone A', 'Zone B', 'Zone C', 'Zone D', 'Zone E'],
            labels: {
                style: {
                    colors: '#616161',
                    fontSize: '12px',
                    fontFamily: 'Arial, sans-serif',
                }
            },
            title: {
                text: 'Zones',
                style: {
                    color: '#616161',
                    fontSize: '12px',
                    fontFamily: 'Arial, sans-serif',
                }
            }
        },
        yaxis: {
            categories: ['Morning', 'Evening', 'Night'],
            reversed: true,
            labels: {
                style: {
                    colors: '#616161',
                    fontSize: '12px',
                    fontFamily: 'Arial, sans-serif',
                }
            },
            title: {
                text: 'Shifts',
                style: {
                    color: '#616161',
                    fontSize: '12px',
                    fontFamily: 'Arial, sans-serif',
                    fontWeight: 'bold'
                }
            }
        },
        tooltip: {
            theme: 'light',
            y: {
                formatter: function(val: number) {
                    return val.toFixed(1) + '% error rate';
                }
            }
        },
        legend: {
            position: 'top',
            horizontalAlign: 'left',
            fontSize: '11px',
            fontFamily: 'Arial, sans-serif',
            offsetY: 5,
            offsetX: 10,
            labels: {
                colors: '#616161'
            },
            markers: {
                width: 12,
                height: 12,
                radius: 2
            },
            itemMargin: {
                horizontal: 15,
                vertical: 5
            }
        },
        plotOptions: {
            heatmap: {
                colorScale: {
                    ranges: [
                        {
                            from: 0,
                            to: 2.5,
                            color: '#00E396',
                            name: 'Low'
                        },
                        {
                            from: 2.5,
                            to: 4.0,
                            color: '#FFC107',
                            name: 'Medium'
                        },
                        {
                            from: 4.0,
                            to: 10.0,
                            color: '#FF4560',
                            name: 'High'
                        }
                    ]
                }
            }
        }
    };

    // Chart series data for Employee Audit Accuracy
    const employeeSeries = [
        {
            name: 'Audit Accuracy',
            data: [97.8, 96.5, 95.2, 94.7, 93.1, 91.8, 90.5, 89.2, 87.6, 85.3]
        }
    ];

    // Chart series data for Error Rates by Shift and Zone
    const errorSeries = [
        {
            name: 'Morning Shift',
            data: [
                { x: 'Zone A', y: 2.1 },
                { x: 'Zone B', y: 2.4 },
                { x: 'Zone C', y: 3.2 },
                { x: 'Zone D', y: 4.5 },
                { x: 'Zone E', y: 1.9 }
            ]
        },
        {
            name: 'Evening Shift',
            data: [
                { x: 'Zone A', y: 2.3 },
                { x: 'Zone B', y: 2.7 },
                { x: 'Zone C', y: 3.5 },
                { x: 'Zone D', y: 4.8 },
                { x: 'Zone E', y: 2.2 }
            ]
        },
        {
            name: 'Night Shift',
            data: [
                { x: 'Zone A', y: 3.1 },
                { x: 'Zone B', y: 3.8 },
                { x: 'Zone C', y: 5.2 },
                { x: 'Zone D', y: 7.3 },
                { x: 'Zone E', y: 3.5 }
            ]
        }
    ];

    const handleChartTypeChange = (
        event: React.MouseEvent<HTMLElement>,
        newType: 'employees' | 'errors' | null,
    ) => {
        if (newType !== null) {
            setChartType(newType);
        }
    };

    return (
        <Paper 
            elevation={0}
            sx={{
                p: 2,
                borderRadius: 3,
                border: '1px solid',
                borderColor: 'divider',
                backgroundColor: 'white',
                mb: 3
            }}
        >
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Box>
                    <Typography variant="h6" gutterBottom sx={{ fontWeight: 'bold', color: '#424242' }}>
                        {chartType === 'employees' ? 'Employee Audit Accuracy (Current Month)' : 'Error Rates by Shift and Zone'}
                    </Typography>
                    <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                        {chartType === 'employees' 
                            ? 'Individual employee performance compared to 95% target' 
                            : 'Heatmap showing error rates across shifts and zones'}
                    </Typography>
                </Box>
                <ToggleButtonGroup
                    value={chartType}
                    exclusive
                    onChange={handleChartTypeChange}
                    size="small"
                    sx={{ height: 36 }}
                >
                    <ToggleButton value="employees" sx={{ textTransform: 'none' }}>
                        Employee Accuracy
                    </ToggleButton>
                    <ToggleButton value="errors" sx={{ textTransform: 'none' }}>
                        Error Heatmap
                    </ToggleButton>
                </ToggleButtonGroup>
            </Box>
            <Box sx={{ height: 450 }}>
                <Chart 
                    options={chartType === 'employees' ? employeeOptions as any : errorOptions as any}
                    series={chartType === 'employees' ? employeeSeries : errorSeries}
                    type={chartType === 'employees' ? 'bar' : 'heatmap'}
                    height="100%"
                    width="100%"
                />
            </Box>
            <Box sx={{ mt: 2, p: 1, borderTop: '1px solid #f0f0f0' }}>
                <Typography variant="body2" sx={{ color: '#000', fontSize: '13px', fontWeight: 'bold', mb: 1 }}>
                    Key Insights:
                </Typography>
                <Box component="ul" sx={{ m: 0, pl: 2, color: 'text.secondary', fontSize: '12px' }}>
                    {chartType === 'employees' ? (
                        <>
                            <li>5 employees exceed the 95% accuracy target, while 5 are below target</li>
                            <li>Top performer (Mark Johnson) is 2.8 percentage points above target</li>
                            <li>Lowest performer (Carlos Martinez) is 9.7 percentage points below target</li>
                        </>
                    ) : (
                        <>
                            <li>Zone D (Packing) consistently has the highest error rates across all shifts</li>
                            <li>Night shift has significantly higher error rates in all zones</li>
                            <li>Zone D during night shift has the highest error rate (7.3%)</li>
                        </>
                    )}
                </Box>
            </Box>
        </Paper>
    )
}

export default EmployeePerformanceChart
