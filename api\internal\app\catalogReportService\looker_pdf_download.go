package catalogReportService

import (
	"bytes"
	"dng-module/config"
	"dng-module/internal/utils"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"
)

// const authBearerPrefix    = "Bearer "

func GetLookerAccessToken(cfg config.Config) (string, error) {
	baseURL := cfg.Dapr.SecretKeys["LOOKERSDK_BASE_URL"]
	clientID := cfg.Dapr.SecretKeys["LOOKER_CLIENT_ID"]
	clientSecret := cfg.Dapr.SecretKeys["LOOKER_CLIENT_SECRET"]

	url := fmt.Sprintf("%s/api/4.0/login?client_id=%s&client_secret=%s", baseURL, clientID, clientSecret)

	resp, err := http.Post(url, "application/json", nil)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return "", errors.New("failed to authenticate with Look<PERSON>")
	}

	var result map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return "", err
	}
	accessToken, ok := result["access_token"].(string)
	if !ok {
		return "", errors.New("invalid access token format")
	}
	return accessToken, nil
}

func CreateRenderTask(dashboardID, accessToken string, cfg config.Config) (string, error) {

	baseURL := cfg.Dapr.SecretKeys["LOOKERSDK_BASE_URL"]

	url := fmt.Sprintf("%s/api/4.0/render_tasks/dashboards/%s/pdf?width=1060&height=800&long_tables=true",
		strings.TrimRight(baseURL, "/"), dashboardID)

	bodyData := map[string]interface{}{
		"pdf_paper_size": "letter",
		"pdf_landscape":  true,
	}
	jsonBody, _ := json.Marshal(bodyData)

	fmt.Println("Requesting Looker render task at:", url)
	fmt.Println("Payload:", string(jsonBody))

	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonBody))
	if err != nil {
		return "", fmt.Errorf("failed to create request: %w", err)
	}
	req.Header.Set("Authorization", authBearerPrefix+accessToken)
	req.Header.Set("Content-Type", "application/json")

	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		return "", fmt.Errorf("http error: %w", err)
	}
	defer resp.Body.Close()

	body, _ := io.ReadAll(resp.Body)
	fmt.Println("Response status:", resp.StatusCode)
	fmt.Println("Response body:", string(body))

	if resp.StatusCode != http.StatusAccepted && resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("failed to create render task: %s", string(body))
	}

	var result map[string]interface{}
	if err := json.Unmarshal(body, &result); err != nil {
		return "", fmt.Errorf("failed to parse render task response: %w", err)
	}

	taskID := ""
	// Looker may return `id` as string or number:
	if id, ok := result["id"].(string); ok {
		taskID = id
	} else if idFloat, ok := result["id"].(float64); ok {
		taskID = fmt.Sprintf("%.0f", idFloat)
	} else {
		return "", errors.New("invalid or missing task ID in Looker response")
	}

	if taskID == "" {
		return "", errors.New("empty task ID from Looker")
	}

	fmt.Println("Created render task with ID:", taskID)
	return taskID, nil
}

func CheckRenderStatus(taskID, accessToken string, cfg config.Config) error {
	// baseURL := cfg.Dapr.SecretKeys["LOOKERSDK_BASE_URL"]

	baseURL := strings.TrimRight(cfg.Dapr.SecretKeys["LOOKERSDK_BASE_URL"], "/")
	url := fmt.Sprintf("%s/api/4.0/render_tasks/%s", baseURL, taskID)

	timeout := time.After(60 * time.Second)
	ticker := time.NewTicker(2 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-timeout:
			return errors.New("timeout: render task did not complete within 60 seconds")

		case <-ticker.C:
			req, err := http.NewRequest("GET", url, nil)
			if err != nil {
				return fmt.Errorf("failed to create request: %w", err)
			}
			req.Header.Set("Authorization", authBearerPrefix+accessToken)

			resp, err := http.DefaultClient.Do(req)
			if err != nil {
				return fmt.Errorf("failed to get render task status: %w", err)
			}
			body, _ := io.ReadAll(resp.Body)
			resp.Body.Close()

			if resp.StatusCode != http.StatusOK {
				return fmt.Errorf("unexpected response: %s", string(body))
			}

			var result struct {
				Status       string `json:"status"`
				StatusDetail string `json:"status_detail"`
			}
			if err := json.Unmarshal(body, &result); err != nil {
				return fmt.Errorf("failed to parse response: %w", err)
			}

			fmt.Printf("Render task %s status: %s, detail: %s\n", taskID, result.Status, result.StatusDetail)

			switch result.Status {
			case "success":
				return nil
			case "failure":
				return errors.New("render task failed")
			}
			// If status is still "enqueued_for_query" or "running", continue polling
		}
	}
}

func DownloadPDF(taskID, accessToken string, cfg config.Config) ([]byte, error) {
	baseURL := strings.TrimRight(cfg.Dapr.SecretKeys["LOOKERSDK_BASE_URL"], "/")
	url := fmt.Sprintf("%s/api/4.0/render_tasks/%s/results", baseURL, taskID)

	req, _ := http.NewRequest("GET", url, nil)
	req.Header.Set("Authorization", authBearerPrefix+accessToken)

	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, errors.New("failed to download PDF")
	}
	utils.Logger.Info("Fetching pdf")
	return io.ReadAll(resp.Body)
}
