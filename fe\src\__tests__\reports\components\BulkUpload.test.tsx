import BulkUploadReports from '@/app/reports/components/BulkUpload'
import { render, screen } from '@testing-library/react'

jest.mock('@/app/reports/services', () => ({
    bulkUploadService: jest.fn(() => Promise.resolve({ data: { error_count: 0 } })),
    bulkInsertErrorsService: jest.fn(),
}))
jest.mock('@/utils/helper', () => ({
    downloadBlob: jest.fn(),
}))
jest.mock('next-auth/react', () => ({
    useSession: () => ({ data: { user: { email: '<EMAIL>' } } }),
}))

describe('BulkUploadReports', () => {
    it('renders upload button', () => {
        render(<BulkUploadReports uploadResponse={null} setUploadResponse={jest.fn()} />)
        expect(screen.getByText(/Upload Excel File/i)).toBeInTheDocument()
    })
})
