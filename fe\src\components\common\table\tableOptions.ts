import { type MaterialReactTableProps } from 'material-react-table'

export const defaultMRTOptions: Partial<MaterialReactTableProps<any>> = {
    enableColumnActions: false,
    //layoutMode: 'grid-no-grow',
    //enablePagination: true,
    /*enableColumnFilters: true,
    enableGlobalFilter: false,
    enableSorting: true,
    enableRowNumbers: false,
    enableHiding: false,
    enableFullScreenToggle: false,
    enableDensityToggle: false, // Keep it if you want to let the user toggle
    positionPagination: 'bottom',
    */
    initialState: {
        density: 'compact', // <- 👈 Dense (can be 'comfortable' | 'compact' | 'spacious')
        showGlobalFilter: true,
        columnPinning: {
            left: ['mrt-row-expand', 'mrt-row-select'],
            right: ['mrt-row-actions'],
        },
    },
    muiTableHeadCellProps: {
        sx: { fontWeight: 'bold' },
    },
    muiTableContainerProps: {
        sx: { paddingTop: '0.5rem' },
    },
}
