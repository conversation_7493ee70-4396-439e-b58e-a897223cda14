package utils

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"

	"github.com/sirupsen/logrus"
)

// Logger is the global log instance
var Logger = logrus.New()

func InitLogger() {
	// Set log format to JSON
	Logger.SetFormatter(&logrus.JSONFormatter{})

	// Set log level from env variable
	logLevel := strings.ToLower(os.Getenv("LOG_LEVEL"))
	switch logLevel {
	case "debug":
		Logger.SetLevel(logrus.DebugLevel)
	case "info":
		Logger.SetLevel(logrus.InfoLevel)
	case "warn":
		Logger.SetLevel(logrus.WarnLevel)
	case "error":
		Logger.SetLevel(logrus.ErrorLevel)
	default:
		Logger.SetLevel(logrus.InfoLevel) // Default level
		Logger.Warn("Invalid LOG_LEVEL provided, defaulting to 'info'")
	}

	// Ensure logs directory exists
	logFilePath := "logs/app.log"
	err := os.MkdirAll("logs", 0755)
	if err != nil {
		fmt.Println("Error creating logs directory:", err)
		return
	}

	absPath, err := filepath.Abs(logFilePath)
	if err == nil {
		fmt.Println("Log file will be created at:", absPath)
	}

	// Output logs to both file and console
	file, err := os.OpenFile(logFilePath, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
	if err != nil {
		Logger.SetOutput(os.Stdout)
		Logger.Warn("Failed to log to file, using default stdout")
	} else {
		Logger.SetOutput(file)
	}
}
