import { Box, <PERSON>ton, Card, CardContent, CardMedia, Typography } from '@mui/material'
import React from 'react'

interface TopicCardProps {
    title: string
    description?: string
    onReadClick: () => void
}

const TopicCard: React.FC<TopicCardProps> = ({ title, description, onReadClick }) => {
    return (
        <Card
            sx={{
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                borderRadius: 3, // Increased border radius
                transition: 'all 0.3s ease-in-out',
                boxShadow: '0 5px 15px rgba(0,0,0,0.05)', // Consistent shadow with other cards
                minHeight: '250px', // Minimum height to ensure consistency
                overflow: 'hidden', // Prevent content overflow
                '&:hover': {
                    transform: 'translateY(-5px)',
                    boxShadow: '0 8px 25px rgba(0,0,0,0.1)', // Enhanced shadow on hover
                },
            }}
        >
            {/* Image Placeholder */}
            <CardMedia
                component="div"
                sx={{
                    height: 100, // Reduced height
                    backgroundColor: '#f5f5f5', // Lighter background
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                }}
            >
                <Typography variant="body2" color="text.secondary">
                    Image Placeholder
                </Typography>
            </CardMedia>

            <CardContent
                sx={{
                    flexGrow: 1,
                    display: 'flex',
                    flexDirection: 'column',
                    p: 2.5, // Increased padding
                    pb: 1, // Reduced bottom padding
                }}
            >
                <Box>
                    <Typography
                        variant="h6"
                        component="h2"
                        gutterBottom
                        fontWeight="bold"
                        sx={{
                            display: '-webkit-box',
                            WebkitLineClamp: 1,
                            WebkitBoxOrient: 'vertical',
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                        }}
                    >
                        {title}
                    </Typography>
                    {description && (
                        <Typography
                            variant="body2"
                            color="text.secondary"
                            sx={{
                                display: '-webkit-box',
                                WebkitLineClamp: 3, // Allow 3 lines of text
                                WebkitBoxOrient: 'vertical',
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                                mb: 2, // Added bottom margin
                            }}
                        >
                            {description}
                        </Typography>
                    )}
                </Box>

                <Box
                    sx={{
                        mt: 'auto',
                        pt: 1.5,
                        width: '100%',
                        display: 'flex',
                        justifyContent: 'center',
                        borderTop: '1px solid rgba(0,0,0,0.05)', // Added separator
                    }}
                >
                    <Button
                        variant="contained"
                        size="small"
                        fullWidth
                        disableElevation={false}
                        onClick={onReadClick}
                        sx={{
                            backgroundColor: '#FFC107 !important',
                            color: 'black',
                            borderRadius: '16px',
                            px: 2,
                            py: 0.5,
                            height: '36px',
                            maxWidth: '90%',
                            fontWeight: 'bold',
                            border: '2px solid #FFC107',
                            boxShadow: '0 2px 6px rgba(255, 193, 7, 0.4)',
                            transition: 'all 0.2s ease',
                            '&:hover': {
                                backgroundColor: '#FFB000 !important',
                                transform: 'translateY(-2px)',
                                boxShadow: '0 4px 8px rgba(255, 193, 7, 0.5)',
                            },
                        }}
                    >
                        Read Now
                    </Button>
                </Box>
            </CardContent>
        </Card>
    )
}

export default TopicCard
