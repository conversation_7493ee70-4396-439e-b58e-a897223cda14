import { ReportIcon } from '@/app/reports/components/Utils'
import { Report as BaseReport, Domain } from '@/app/reports/types'
import { Box } from '@mui/material'
import { format, parseISO } from 'date-fns'
import { capitalize } from 'lodash'
import { MRT_ColumnDef } from 'material-react-table'

interface Report extends BaseReport {
    domainName: string
}
type RowData = Domain | Report

export function getReportTableColumns(
    toggleSubRow: (reportId: string) => void,
    renderDiscCell: (isSubRow: boolean, value: string) => React.ReactNode,
    renderActionsCell: (isSubRow: boolean, row: Report) => React.ReactNode,
): MRT_ColumnDef<RowData>[] {
    return [
        {
            accessorKey: 'name',
            header: 'Report Name',
            maxSize: 30,
            Cell: ({ row, cell }) => {
                const isParent = 'report' in row.original
                const sourceSystem = (row.original as Report).source_system
                const report = row.original as Report

                const handleClick = () => {
                    if (!isParent) {
                        toggleSubRow(report.id)
                    }
                }
                return (
                    <Box
                        onClick={isParent ? undefined : handleClick}
                        sx={{
                            fontWeight: isParent ? 'bold' : 'normal',
                            cursor: isParent ? 'default' : 'pointer',
                            display: 'flex',
                            alignItems: 'center',
                        }}
                    >
                        {sourceSystem && <ReportIcon sourceSystem={sourceSystem} />}
                        {capitalize(cell.getValue<string>())}
                    </Box>
                )
            },
        },
        {
            accessorKey: 'category',
            header: 'Category',
            maxSize: 30,
            Cell: ({ row, cell }) => {
                const isParent = row.original.hasOwnProperty('report')
                return isParent ? '' : cell.getValue<string>() || '-'
            },
        },
        {
            accessorKey: 'description',
            header: 'Description',
            enableSorting: false,
            maxSize: 30,
            Cell: ({ row, cell }) => {
                const isSubRow = !('report' in row.original)
                return renderDiscCell(isSubRow, cell.getValue<string>())
            },
        },
        {
            accessorKey: 'published_on',
            header: 'Published On',
            enableSorting: false,
            maxSize: 30,
            Cell: ({ row, cell }) => {
                const publishedOn = cell.getValue<string>()
                if (!publishedOn) return ''
                const date = parseISO(publishedOn)
                const isSubRow = !('report' in row.original)
                return isSubRow ? format(date, 'MMM dd, yyyy') : ''
            },
        },
        {
            id: 'actions',
            header: 'Actions',
            maxSize: 30,
            Cell: ({ row }) => {
                const isSubRow = !('report' in row.original)
                return renderActionsCell(isSubRow, row.original as Report)
            },
            muiTableHeadCellProps: {
                style: {
                    paddingLeft: 15,
                },
            },
        },
    ]
}
