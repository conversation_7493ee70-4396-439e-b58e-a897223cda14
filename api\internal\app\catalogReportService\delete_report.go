package catalogReportService

import (
	"dng-module/config"
	"dng-module/internal/model"
	"dng-module/internal/utils"
	"errors"
	"fmt"
	"strings"
)

func DeleteReports(userID int, reportIDs []int) error {
	// First, get the user's email for better log readability
	var user model.User
	err := config.DB.Select("email").Where("id = ?", userID).First(&user).Error
	if err != nil {
		utils.Logger.Error(fmt.Sprintf("Failed to retrieve user information: %v", err))
		// Fall back to using user ID if email retrieval fails
		user.Email = fmt.Sprintf("UserID:%d", userID)
	}

	utils.Logger.Info(fmt.Sprintf("User %s requested deletion of %d reports", user.Email, len(reportIDs)))

	if len(reportIDs) == 0 {
		utils.Logger.Warn("Delete request failed: No report IDs provided")
		return errors.New("no report IDs provided")
	}

	// Step 1: Fetch user roles and domain access
	type AccessInfo struct {
		DomainID     int
		CanEdit      bool
		IsSuperAdmin bool
	}

	var accessInfos []AccessInfo
	err = config.DB.
		Table("user_domain_access").
		Select("user_domain_access.domain_id, r.can_edit, r.is_super_admin").
		Joins("JOIN roles r ON user_domain_access.role_id = r.id").
		Where("user_domain_access.user_id = ?", userID).
		Scan(&accessInfos).Error
	if err != nil {
		utils.Logger.Error(fmt.Sprintf("Failed to verify user permissions: %v", err))
		return err
	}

	isSuperAdmin := false
	domainEditAccess := make(map[int]bool)
	for _, access := range accessInfos {
		if access.IsSuperAdmin {
			isSuperAdmin = true
			break
		}
		if access.CanEdit {
			domainEditAccess[access.DomainID] = true
		}
	}

	// Step 2: Fetch reports by IDs
	var reports []model.Report
	err = config.DB.Where("id IN ?", reportIDs).Find(&reports).Error
	if err != nil {
		utils.Logger.Error(fmt.Sprintf("Failed to retrieve report information: %v", err))
		return err
	}
	if len(reports) == 0 {
		utils.Logger.Warn(fmt.Sprintf("No reports found matching IDs: %v", reportIDs))
		return errors.New("no matching reports found")
	}

	// Step 3: Check if user has permission for each report
	var unauthorized []int
	for _, report := range reports {
		if !isSuperAdmin && !domainEditAccess[report.DomainID] {
			unauthorized = append(unauthorized, report.ID)
		}
	}

	if len(unauthorized) > 0 {
		utils.Logger.Warn(fmt.Sprintf("Access denied: User %s attempted to delete %d unauthorized reports",
			user.Email, len(unauthorized)))
		return errors.New("permission denied: you cannot delete one or more selected reports")
	}

	// Step 4: Perform deletion in transaction
	tx := config.DB.Begin()
	if tx.Error != nil {
		utils.Logger.Error(fmt.Sprintf("Database transaction failed: %v", tx.Error))
		return tx.Error
	}

	if err := tx.Where("report_id IN ?", reportIDs).Delete(&model.UserReportPreference{}).Error; err != nil {
		utils.Logger.Error(fmt.Sprintf("Failed to delete report preferences: %v", err))
		tx.Rollback()
		return err
	}

	if err := tx.Where("id IN ?", reportIDs).Delete(&model.Report{}).Error; err != nil {
		utils.Logger.Error(fmt.Sprintf("Failed to delete reports: %v", err))
		tx.Rollback()
		return err
	}

	if err := tx.Commit().Error; err != nil {
		utils.Logger.Error(fmt.Sprintf("Failed to complete report deletion: %v", err))
		return err
	}

	// Create a readable list of report IDs
	reportIDsStr := strings.Trim(strings.Replace(fmt.Sprint(reportIDs), " ", ", ", -1), "[]")
	utils.Logger.Info(fmt.Sprintf("User %s successfully deleted %d reports (IDs: %s)",
		user.Email, len(reports), reportIDsStr))

	return nil
}
