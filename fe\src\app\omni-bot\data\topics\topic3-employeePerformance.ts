import { Topic } from '../../types';
import { employeePerformanceCharts } from '../charts';

// Employee Performance Topic
export const employeePerformanceTopic: Topic = {
  id: 3,
  title: 'Employee Performance',
  description: 'Detailed analysis of shift and employee performance metrics with daily audit completion',
  fullContent: `Dollar General's Employee Performance dashboard provides a comprehensive view of individual and shift-level performance metrics. This dashboard enables managers to identify top performers, address training needs, and optimize staffing across shifts and zones.

  The data includes employee-level audit accuracy, daily and weekly audit completion rates, trailer accuracy trends, picking error analysis, time-of-day error patterns, and shift/zone performance breakdowns.

  By analyzing employee performance data, managers can implement targeted coaching, recognize high performers, identify systemic issues, and optimize staffing allocations to improve overall operational efficiency and accuracy.`,
  charts: employeePerformanceCharts
};

// Employee Performance Questions and Answers
export const EMPLOYEE_PERFORMANCE_QA = [
  {
    keywords: ["employees", "highest", "lowest", "audit accuracy", "month"],
    answer: 'For the current month, the employees with the highest audit accuracy are:\n\n1. <PERSON>: 97.8%\n2. <PERSON>: 96.5%\n3. <PERSON>: 95.2%\n\nThe employees with the lowest audit accuracy are:\n\n1. <PERSON>: 85.3%\n2. <PERSON>: 87.6%\n3. <PERSON> <PERSON>: 89.2%\n\nThe top performers exceed our target of 95%, while the bottom three are significantly below target. We\'ve implemented a peer mentoring program where our top performers are coaching those with lower accuracy rates. <PERSON> <PERSON> is new (2 months on the job) and is still in the learning curve phase.'
  },
  {
    keywords: ["percentage", "audits", "completed", "week"],
    answer: 'For the current week (Week 4), we have completed 96.7% of our scheduled audits, which is above our target of 95%. This represents 580 completed audits out of 600 scheduled. The remaining 20 audits are scheduled for completion by end of day Friday.\n\nThis is an improvement over Week 3, where we completed only 91.2% of scheduled audits. The improvement is attributed to the new audit scheduling system implemented last week, which better balances the workload across shifts and accounts for staffing levels.'
  },
  {
    keywords: ["trend", "trailer accuracy", "past", "weeks"],
    answer: 'The trend for trailer accuracy over the past 4 weeks shows steady improvement:\n\n- Week 1: 92.3%\n- Week 2: 93.1%\n- Week 3: 94.5%\n- Week 4: 95.8%\n\nThis represents a 3.5 percentage point improvement over the 4-week period. The improvement is attributed to the implementation of barcode scanning verification at each stage of the loading process and enhanced manifest reconciliation protocols. We\'re now just 1.2 percentage points below our target of 97%, and if the current trend continues, we should reach the target within the next 2 weeks.'
  },
  {
    keywords: ["items", "most frequent", "picking errors"],
    answer: 'The items with the most frequent picking errors are:\n\n1. Small Health & Beauty items (28% of errors): Particularly similar-sized items with different SKUs\n2. Seasonal merchandise (22% of errors): Likely due to frequent planogram changes\n3. Apparel items (17% of errors): Particularly size variations of the same style\n4. Similar packaged food items (15% of errors): Especially different flavors of the same product\n5. Small hardware items (11% of errors): Particularly fasteners and small tools\n\nThe remaining 7% is distributed across various categories. We\'ve implemented enhanced visual cues on picking instructions and improved lighting in the picking areas for small items to address the top categories, which account for 50% of all picking errors.'
  },
  {
    keywords: ["time of day", "highest error rates"],
    answer: 'Our error rate analysis by time of day shows clear patterns:\n\n- 6:00 AM - 8:00 AM: 3.2% error rate\n- 8:00 AM - 12:00 PM: 2.1% error rate\n- 12:00 PM - 2:00 PM: 4.7% error rate\n- 2:00 PM - 6:00 PM: 2.5% error rate\n- 6:00 PM - 10:00 PM: 3.8% error rate\n- 10:00 PM - 6:00 AM: 5.3% error rate\n\nThe highest error rates occur during the overnight shift (10:00 PM - 6:00 AM) and during the post-lunch period (12:00 PM - 2:00 PM). The overnight shift issues are likely due to fatigue and reduced supervision, while the post-lunch spike may be related to the "post-lunch dip" in alertness. We\'ve implemented additional quality checks during these high-error periods and are testing rotating breaks during the overnight shift to combat fatigue.'
  },
  {
    keywords: ["breakdown", "errors", "shift", "zone"],
    answer: 'The breakdown of errors by shift and zone shows the following patterns:\n\n**Morning Shift (6:00 AM - 2:00 PM):**\n- Zone A (Receiving): 2.1% error rate\n- Zone B (Storage): 2.4% error rate\n- Zone C (Picking): 3.2% error rate\n- Zone D (Packing): 4.5% error rate\n- Zone E (Shipping): 1.9% error rate\n\n**Evening Shift (2:00 PM - 10:00 PM):**\n- Zone A (Receiving): 2.3% error rate\n- Zone B (Storage): 2.7% error rate\n- Zone C (Picking): 3.5% error rate\n- Zone D (Packing): 4.8% error rate\n- Zone E (Shipping): 2.2% error rate\n\n**Night Shift (10:00 PM - 6:00 AM):**\n- Zone A (Receiving): 3.1% error rate\n- Zone B (Storage): 3.8% error rate\n- Zone C (Picking): 5.2% error rate\n- Zone D (Packing): 7.3% error rate\n- Zone E (Shipping): 3.5% error rate\n\nZone D (Packing) consistently has the highest error rates across all shifts, with the night shift having the highest overall error rates. We\'ve implemented targeted training for Zone D employees and are piloting enhanced lighting and ergonomic workstations in this zone to address the high error rates.'
  }
];
