import { fireEvent, render, screen } from '@testing-library/react'
import DeleteConfirmDialog from '../../../app/reports/reportCards/components/DeleteConfirmDialog'

describe('DeleteConfirmDialog', () => {
    it('renders and calls onConfirm', () => {
        const onConfirm = jest.fn()
        render(<DeleteConfirmDialog open={true} onClose={jest.fn()} onConfirm={onConfirm} reportCount={2} />)
        expect(screen.getByText(/Confirm Deletion/i)).toBeInTheDocument()
        // Find all elements with text "Delete" and click the button
        const deleteButtons = screen.getAllByText(/Delete/i)
        // The button should have role "button"
        const deleteButton = deleteButtons.find((el) => el.tagName === 'BUTTON')
        expect(deleteButton).toBeDefined()
        fireEvent.click(deleteButton!)
        expect(onConfirm).toHaveBeenCalled()
    })
})
