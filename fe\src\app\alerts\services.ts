import { deleteRequest, getRequest, postRequest, putRequest } from '@/utils/axiosInstance'
import { BACKEND_URL } from '@/utils/constants'
import { IAlert } from './types'

export const getAlertsService = async (params?: string) => {
    try {
        const url = params ? `${BACKEND_URL}/alerts?${params}` : `${BACKEND_URL}/alerts`
        const response = await getRequest<{ data: IAlert[]; totalCount: number }>(url)
        return response.data
    } catch (error) {
        console.error('Error fetching alerts:', error)
        throw error
    }
}

export const createAlertsService = async (data: IAlert) => {
    try {
        const response = await postRequest<IAlert>(BACKEND_URL + '/alerts', data)
        return response.data
    } catch (error) {
        console.error('Error creating alert:', error)
        throw error
    }
}

export const editAlertsService = async (data: IAlert) => {
    try {
        const response = await putRequest<IAlert>(BACKEND_URL + '/alerts', data)
        return response.data
    } catch (error) {
        console.error('Error editing alert:', error)
        throw error
    }
}

export const deleteAlertsService = async (ids: string[]) => {
    try {
        const response = await deleteRequest(BACKEND_URL + `/alerts?ids=${ids.join(',')}`)
        return { message: 'Successfully deleted', data: response }
    } catch (error) {
        console.error('Error deleting alert:', error)
        throw error
    }
}

export async function fetchVisibleEvents(access_token: string): Promise<IAlert[]> {
    try {
        const response = await getRequest<{ data: IAlert[] }>(`${BACKEND_URL}/alerts/visible`, {
            headers: {
                Authorization: `Bearer ${access_token}`,
            },
        })

        if (response.status !== 200) {
            throw new Error(`Failed to fetch: ${response.statusText || 'Unknown error'}`)
        }

        return response.data.data
    } catch (error) {
        console.log('error getting visible records', error)
        return []
    }
}
