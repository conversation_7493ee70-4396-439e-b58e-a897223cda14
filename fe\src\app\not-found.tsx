'use client'

import { Box, Button, Typography } from '@mui/material'
import { ErrorOutline as ErrorIcon, HomeOutlined as HomeIcon } from '@mui/icons-material'
import Link from 'next/link'

export default function NotFoundPage() {
    return (
        <Box
            sx={{
                minHeight: '100vh',
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'center',
                alignItems: 'center',
                textAlign: 'center',
                px: 2,
                backgroundColor: '#f9f9f9',
            }}
        >
            {/* Icon */}
            <ErrorIcon sx={{ fontSize: '4rem', color: 'error.main', mb: 2 }} />

            {/* Title */}
            <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
                Oops! Page Not Found
            </Typography>

            {/* Subtitle */}
            <Typography variant="body1" sx={{ color: 'text.secondary', mb: 3 }}>
                The page you are looking for doesn’t exist or has been moved.
            </Typography>

            {/* Home Button */}
            <Link href="/" passHref>
                <Button
                    variant="contained"
                    startIcon={<HomeIcon />}
                    sx={{
                        px: 4,
                        py: 1,
                        borderRadius: 20,
                        textTransform: 'none',
                    }}
                >
                    Go to Homepage
                </Button>
            </Link>
        </Box>
    )
}
