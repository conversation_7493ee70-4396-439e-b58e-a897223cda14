'use client'

import AccountTreeIcon from '@mui/icons-material/AccountTree'
import ArrowForwardIcon from '@mui/icons-material/ArrowForward'
import FolderIcon from '@mui/icons-material/Folder'
import PeopleIcon from '@mui/icons-material/People'
import SearchIcon from '@mui/icons-material/Search'
import ShieldIcon from '@mui/icons-material/Shield'
import TimelineIcon from '@mui/icons-material/Timeline'
import ViewListIcon from '@mui/icons-material/ViewList'
import { Box, Container, Grid2 as Grid, Link, Paper, Typography } from '@mui/material'
import { useState } from 'react'

export default function DataCatalog() {
    const [selectedCard, setSelectedCard] = useState(1)

    return (
        <Paper sx={{ mt: 1 }}>
            <Container maxWidth="lg" sx={{ py: 3 }}>
                {/* Header Section */}
                <Box sx={{ textAlign: 'center', mb: 4 }}>
                    <Typography variant="h4" gutterBottom fontWeight="medium" color="text.primary">
                        Why Choose Our Data Catalog?
                    </Typography>
                    <Typography variant="body1" color="text.secondary" sx={{ maxWidth: 800, mx: 'auto' }}>
                        Empower your organization with a modern data catalog that brings clarity and efficiency to your
                        data management.
                    </Typography>
                </Box>

                {/* Cards Section */}
                <Grid container spacing={3} sx={{ mb: 3 }}>
                    {cards.map((card) => (
                        <Grid key={card.id} size={{ xs: 12, sm: 6, md: 3 }}>
                            <Paper
                                onClick={() => setSelectedCard(card.id)}
                                elevation={selectedCard === card.id ? 3 : 1}
                                sx={{
                                    p: [3, 2],
                                    height: '100%',
                                    cursor: 'pointer',
                                    borderRadius: 1,
                                    border: selectedCard === card.id ? '1px solid #2196f3' : '1px solid #e0e0e0',
                                    transition: 'all 0.2s',
                                    '&:hover': {
                                        boxShadow: 2,
                                        borderColor: '#bbdefb',
                                    },
                                }}
                            >
                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }} mb={1}>
                                    {card.icon}
                                    <Typography variant="h6" gutterBottom fontWeight="medium">
                                        {card.title}
                                    </Typography>
                                </Box>
                                <Typography variant="body2" color="text.secondary">
                                    {card.description}
                                </Typography>
                            </Paper>
                        </Grid>
                    ))}
                </Grid>

                {/* Details Section */}
                <Paper
                    sx={{
                        p: 4,
                        mb: 3,
                        borderRadius: 1,
                        bgcolor: '#f8f9fa',
                        border: '1px solid #e0e0e0',
                    }}
                >
                    <Grid container spacing={4}>
                        {/* Left side - Image placeholder */}
                        <Grid size={{ xs: 12, md: 5 }}>
                            <Box
                                sx={{
                                    width: '100%',
                                    height: 300,
                                    bgcolor: '#e0e0e0',
                                    borderRadius: 1,
                                }}
                            ></Box>
                        </Grid>

                        {/* Right side - Details content */}
                        <Grid size={{ xs: 12, md: 7 }}>
                            <Typography variant="h5" gutterBottom fontWeight="medium">
                                Experience {details[selectedCard].title}
                            </Typography>

                            <Box sx={{ mt: 3 }}>
                                {details[selectedCard].features.map((feature, index) => (
                                    <Box key={feature.title} sx={{ display: 'flex', mb: 3 }}>
                                        <Box sx={{ mr: 2, mt: 0.5 }}>{feature.icon}</Box>
                                        <Box>
                                            <Typography variant="subtitle1" fontWeight="medium">
                                                {feature.title}
                                            </Typography>
                                            <Typography variant="body2" color="text.secondary">
                                                {feature.description}
                                            </Typography>
                                        </Box>
                                    </Box>
                                ))}
                            </Box>

                            <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 6 }}>
                                <Link
                                    href="https://dollargeneral-dev.collibra.com/apps/"
                                    target="_blank"
                                    sx={{
                                        display: 'flex',
                                        alignItems: 'center',
                                        textDecoration: 'none',
                                        '&:hover': { textDecoration: 'underline' },
                                    }}
                                >
                                    <Typography variant="button" color="info" sx={{ mr: 0.5 }}>
                                        Learn More About Collibra
                                    </Typography>
                                    <ArrowForwardIcon fontSize="small" color="info" />
                                </Link>
                            </Box>
                        </Grid>
                    </Grid>
                </Paper>
            </Container>
        </Paper>
    )
}

// Card data
const cards = [
    {
        id: 1,
        title: 'Smart Search',
        description: 'Find any data asset instantly with our intelligent search capabilities.',
        icon: <SearchIcon color="info" fontSize="large" />,
    },
    {
        id: 2,
        title: 'Data Lineage',
        description: 'Track data movement and transformation across your organization.',
        icon: <TimelineIcon color="info" fontSize="large" />,
    },
    {
        id: 3,
        title: 'Governance',
        description: 'Ensure compliance and maintain data quality standards.',
        icon: <ShieldIcon color="info" fontSize="large" />,
    },
    {
        id: 4,
        title: 'Data Assets',
        description: 'Explore curated datasets, tables, and APIs available for use.',
        icon: <ViewListIcon color="info" fontSize="large" />,
    },
]

// Detail features
const details: Record<
    number,
    { title: string; features: { title: string; description: string; icon: JSX.Element }[] }
> = {
    1: {
        title: 'Data Discovery',
        features: [
            {
                title: 'Centralized Repository',
                description: 'All your data assets, metadata, and documentation in one place.',
                icon: <FolderIcon color="info" />,
            },
            {
                title: 'Data Lineage Visualization',
                description: 'Understand data relationships and dependencies with interactive graphs.',
                icon: <AccountTreeIcon color="info" />,
            },
            {
                title: 'Collaboration Tools',
                description: 'Work together with built-in commenting and sharing features.',
                icon: <PeopleIcon color="info" />,
            },
        ],
    },
    2: {
        title: 'Data Lineage',
        features: [
            {
                title: 'End-to-End Traceability',
                description: 'Track data from source systems through transformations to consumption.',
                icon: <AccountTreeIcon color="info" />,
            },
            {
                title: 'Impact Analysis',
                description: 'Understand how changes affect downstream systems and reports.',
                icon: <FolderIcon color="info" />,
            },
            {
                title: 'Visual Mapping',
                description: 'Interactive diagrams showing data relationships and transformations.',
                icon: <PeopleIcon color="info" />,
            },
        ],
    },
    3: {
        title: 'Data Governance',
        features: [
            {
                title: 'Policy Management',
                description: 'Define, enforce, and monitor data policies and standards.',
                icon: <ShieldIcon color="info" />,
            },
            {
                title: 'Role-Based Access',
                description: 'Secure your data with granular permissions and access controls.',
                icon: <FolderIcon color="info" />,
            },
            {
                title: 'Compliance Reporting',
                description: 'Generate reports for regulatory compliance and audits.',
                icon: <PeopleIcon color="info" />,
            },
        ],
    },
    4: {
        title: 'Data Assets',
        features: [
            {
                title: 'Asset Inventory',
                description: 'Comprehensive catalog of all data assets across your organization.',
                icon: <FolderIcon color="info" />,
            },
            {
                title: 'Usage Analytics',
                description: 'Track how data is being accessed and used throughout your organization.',
                icon: <AccountTreeIcon color="info" />,
            },
            {
                title: 'Data Quality Metrics',
                description: 'Monitor and improve data quality with built-in metrics and alerts.',
                icon: <PeopleIcon color="info" />,
            },
        ],
    },
}
