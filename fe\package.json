{"name": "dg-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "dev-h": "node --loader ts-node/esm server.ts", "build": "next build", "start": "cross-env NODE_ENV=production node --loader ts-node/esm server.ts", "ts": "tsc --noEmit", "lint": "next lint", "test": "jest", "test:coverage": "jest --coverage", "test:watch": "jest --watch", "sonar": "sonarqube-scanner"}, "dependencies": {"@emotion/cache": "^11.14.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@looker/embed-sdk": "^1.8.7", "@mantine/dates": "^7.17.2", "@mui/icons-material": "^6.3.0", "@mui/material": "^6.4.5", "@mui/material-nextjs": "^6.2.0", "@reduxjs/toolkit": "1.9.6", "@tableau/embedding-api": "3.12.0", "apexcharts": "3.41.1", "axios": "^1.8.3", "date-fns": "^4.1.0", "formik": "^2.4.6", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "material-react-table": "^3.2.1", "next": "^14.2.22", "next-auth": "^4.24.11", "powerbi-client": "^2.23.1", "powerbi-client-react": "^1.4.0", "pptxjs": "^0.0.0", "react": "18.2.0", "react-apexcharts": "1.4.1", "react-dom": "18.2.0", "react-redux": "8.1.2", "typescript": "5.1.6", "xlsx": "https://cdn.sheetjs.com/xlsx-0.20.2/xlsx-0.20.2.tgz", "yup": "^1.6.1"}, "devDependencies": {"@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^9.0.9", "@types/lodash": "^4.17.15", "@types/node": "20.4.5", "@types/react": "18.2.18", "@types/react-dom": "18.2.7", "@typescript-eslint/eslint-plugin": "^8.32.1", "cross-env": "^7.0.3", "eslint": "^8.57.0", "eslint-config-next": "^15.1.6", "eslint-config-prettier": "^10.0.1", "eslint-plugin-import": "^2.31.0", "eslint-plugin-notice": "^1.0.0", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-sonarjs": "^3.0.2", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-junit": "^16.0.0", "prettier": "^3.4.2", "sonarqube-scanner": "^4.2.8", "ts-node": "^10.9.2"}, "type": "module"}