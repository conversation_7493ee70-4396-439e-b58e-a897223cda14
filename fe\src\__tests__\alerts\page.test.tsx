// __tests__/NewsAlertsPage.test.tsx

import { render, screen } from '@testing-library/react'
import NewsAlertsPage from '@/app/alerts/page'
import React from 'react' // Adjust path if needed

// Mock GenericLayout
jest.mock(
    '@/components/common/layout/GenericLayout',
    () =>
        ({
             children,
             showSidebar,
             useContainer,
         }: {
            children: React.ReactNode
            showSidebar: boolean
            useContainer: boolean
        }) => (
            <div
                data-testid="generic-layout"
                data-show-sidebar={showSidebar.toString()}
                data-use-container={useContainer.toString()}
            >
                {children}
            </div>
        ),
)
// Mock child components to keep test focused
jest.mock('@/app/alerts/components/AlertsTable', () => () => <div data-testid="alerts-table" />)

jest.mock('@/components/common/utility/PageTitle', () => ({ title }: { title: string }) => <h1>{title}</h1>)

describe('NewsAlertsPage', () => {
    it('renders the page with title, layout, and alerts table', () => {
        render(<NewsAlertsPage />)

        // Check GenericLayout is rendered with correct props (as data-attributes)
        const layout = screen.getByTestId('generic-layout')
        expect(layout).toHaveAttribute('data-show-sidebar', 'true')
        expect(layout).toHaveAttribute('data-use-container', 'true')

        // Check PageTitle renders correct title text
        expect(screen.getByRole('heading', { name: /news and alerts/i })).toBeInTheDocument()

        // Check AlertsTable rendered
        expect(screen.getByTestId('alerts-table')).toBeInTheDocument()
    })
})
