package utils

import (
	"dng-module/internal/model"

	"gorm.io/gorm"
)

// CheckUserByEmail checks if a user exists by email and returns a boolean and the user record.
func CheckUserByEmail(db *gorm.DB, email string) (bool, model.User, error) {
	var user model.User
	err := db.Where("email = ?", email).First(&user).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return false, model.User{}, nil
		}
		return false, model.User{}, err
	}
	return true, user, nil
}

func UniqueInts(input []int) []int {
	uniqueMap := make(map[int]struct{})
	for _, v := range input {
		uniqueMap[v] = struct{}{}
	}
	var result []int
	for k := range uniqueMap {
		result = append(result, k)
	}
	return result
}

func Contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}
