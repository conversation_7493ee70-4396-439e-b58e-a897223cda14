package model

import "time"

// GetSearchOptionsRequest is the expected request body for getSearchOptions API.
type GetSearchOptionsRequest struct {
	UserID   int    `json:"user_id" binding:"required"`
	RoleName string `json:"role_name" binding:"required"` // e.g., ["ADMIN"]
	Query    string `json:"query" binding:"required"`
}

// Report DTO for creating a report
type CreateReportDto struct {
	ID           int        `json:"id"`
	Name         string     `json:"name" binding:"required"`
	ReportID     string     `json:"report_id" binding:"required"`
	GroupID      string     `json:"group_id"`
	Description  string     `json:"description"`
	Category     string     `json:"category" binding:"required"`
	DomainID     int        `json:"domain_id" binding:"required"`
	SourceSystem string     `json:"source_system"`
	CreatedBy    string     `json:"created_by" binding:"required"`
	PublishedOn  *time.Time `json:"published_on" type:"date"`
	//UserEmail    string `json:"user_email" binding:"required"`
	CreatedAt  string `json:"created_at,omitempty"`
	UpdatedAt  string `json:"updated_at,omitempty"`
	ReportUrl  string  `json:"report_url"`
	IsFavorite bool   `json:"is_favorite,omitempty"`
	IsSaved    bool   `json:"is_saved,omitempty"`
}

// Define a struct to match JSON body
type UserRequest struct {
	UserID   int    `json:"user_id" binding:"required"` // Required field
	RoleName string `json:"role_name"`
}

// GetReportsQueryParams - Struct for query parameters
type GetReportsQueryParams struct {
	Search       string   `form:"search"`
	Sort         string   `form:"sort"`
	Order        string   `form:"order"`
	View         string   `form:"view"`           // "all", "favorites", "saved"
	Page         int      `form:"page,default=0"` // Changed default from 1 to 0
	Size         int      `form:"size"`
	Category     []string `form:"category"`
	SourceSystem []string `form:"source_system"`
	Domain       []int    `form:"domain"`
	StartDate    string   `form:"start_date"`
	EndDate      string   `form:"end_date"`
}

// FavoriteReportRequest represents the request body
type FavoriteReportRequest struct {
	UserID     int    `json:"user_id" binding:"required"`
	ReportID   int    `json:"report_id" binding:"required"`
	IsFavorite bool   `json:"is_favorite"`
	IsSaved    bool   `json:"is_saved"`
	View       string `json:"view"`
}

// Define a struct to match JSON body
type UserReq struct {
	UserEmail string   `json:"email" binding:"required"` // Required field
	AdGroups  []string `json:"ad_groups" binding:"required"`
		FirstName string   `json:"first_name"`
	LastName  string   `json:"last_name"`

}

type DashboardRequest struct {
	DashboardID string `json:"dashboard_id" binding:"required"`
}
