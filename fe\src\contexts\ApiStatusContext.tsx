'use client'

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'
import axios from 'axios'
import { BACKEND_URL } from '@/utils/constants'
import { API_STATUS_EVENT } from '@/utils/axiosInstance'

// Define the context type
type ApiStatusContextType = {
  isApiAvailable: boolean
  checkApiStatus: () => Promise<boolean>
  isChecking: boolean
}

// Create the context with default values
const ApiStatusContext = createContext<ApiStatusContextType>({
  isApiAvailable: true,
  checkApiStatus: async () => true,
  isChecking: false
})

// Custom hook to use the API status context
export const useApiStatus = () => useContext(ApiStatusContext)

type ApiStatusProviderProps = {
  children: ReactNode
  checkInterval?: number // in milliseconds
  apiUrl?: string
}

export const ApiStatusProvider: React.FC<ApiStatusProviderProps> = ({
  children,
  checkInterval = 60000, // Default to checking every minute
  apiUrl = BACKEND_URL
}) => {
  const [isApiAvailable, setIsApiAvailable] = useState<boolean>(true)
  const [isChecking, setIsChecking] = useState<boolean>(false)

  // Function to check API status
  const checkApiStatus = async (): Promise<boolean> => {
    if (!apiUrl) return true // If no API URL is provided, assume it's available

    setIsChecking(true)
    try {
      console.log('Checking API status - attempting to reach health endpoint');

      // Check the backend health/ready endpoint which includes database connectivity
      // Note: The health endpoint is at the root level, not under /api
      const baseUrl = apiUrl.replace('/dng/api', ''); // Remove /api if present
      const healthUrl = `${baseUrl}/health/ready`;
      console.log('Checking backend health endpoint at:', healthUrl);

      const response = await axios.head(healthUrl, {
        timeout: 5000,
        // Accept any status to handle it ourselves
        validateStatus: () => true
      });

      // Check if the response status is not 200 OK
      if (response.status !== 200) {
        console.error(`Health endpoint returned non-200 status: ${response.status}`);
        throw new Error(`Health endpoint returned status ${response.status}`);
      }

      // Check database connectivity from headers
      const dbConnected = response.headers['x-health-database-connected'];
      const healthStatus = response.headers['x-health-status'];

      console.log('Health check headers:', {
        dbConnected,
        healthStatus,
        allHeaders: response.headers
      });

      // If database is not connected, throw an error
      if (dbConnected === 'false' || healthStatus === 'degraded') {
        throw new Error('Database connectivity check failed');
      }

      console.log('Backend health endpoint is available');
      setIsApiAvailable(true)
      return true
    } catch (err: any) {
      console.error('Backend health endpoint check failed:', err);
      console.error('Error details:', {
        status: err?.response?.status,
        statusText: err?.response?.statusText,
        data: err?.response?.data,
        url: err?.config?.url
      });

      // Mark API as unavailable
      setIsApiAvailable(false)

      // If this is a network error (backend completely down), redirect to error page
      if (!err.response && typeof window !== 'undefined') {
        // Only redirect if we're in the browser and not already on the error page
        const currentPath = window.location.pathname;
        if (!currentPath.startsWith('/error/')) {
          console.log('Redirecting to API error page due to network error');
          window.location.href = '/error/api';
        }
      }

      return false
    } finally {
      setIsChecking(false)
    }
  }

  // Set up regular health checks and listen for API status events
  useEffect(() => {
    // Initial check
    checkApiStatus();

    // Set up interval for regular checks
    const intervalId = setInterval(checkApiStatus, checkInterval);

    // Listen for API status events from axios interceptors
    const handleApiStatusChange = (event: Event) => {
      const customEvent = event as CustomEvent<{
        isAvailable: boolean;
        timestamp: Date;
      }>;

      console.log('API status event received:', customEvent.detail);

      // If API is marked as unavailable, update state immediately
      if (!customEvent.detail.isAvailable) {
        setIsApiAvailable(false);
      } else {
        // If API is marked as available, verify with a health check
        checkApiStatus().then(isAvailable => {
          console.log('Health check after API status event:', { isAvailable });
          setIsApiAvailable(isAvailable);
        });
      }
    };

    // Add event listener for API status changes
    if (typeof window !== 'undefined') {
      window.addEventListener(API_STATUS_EVENT, handleApiStatusChange);
    }

    // Clean up
    return () => {
      clearInterval(intervalId);
      if (typeof window !== 'undefined') {
        window.removeEventListener(API_STATUS_EVENT, handleApiStatusChange);
      }
    };
  }, [apiUrl, checkInterval]);

  // Memoize context value to prevent unnecessary re-renders
  const value = React.useMemo(() => ({
    isApiAvailable,
    checkApiStatus,
    isChecking
  }), [isApiAvailable, isChecking]);

  return (
    <ApiStatusContext.Provider value={value}>
      {children}
    </ApiStatusContext.Provider>
  )
}
