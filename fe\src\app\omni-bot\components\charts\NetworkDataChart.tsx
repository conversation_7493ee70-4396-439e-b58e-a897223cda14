'use client'
import { Box, Paper, ToggleButton, ToggleButtonGroup, Typography } from '@mui/material'
import React, { useState } from 'react'
import GenericChart from './GenericChart'
import networkData from './data/networkDataChart.json'

const NetworkDataChart: React.FC = () => {
    const [chartType, setChartType] = useState<'sales' | 'inventory'>('sales')

    // Use imported data
    const salesOptions = networkData.salesOptions
    const inventoryOptions = networkData.inventoryOptions
    const salesSeries = networkData.salesSeries
    const inventorySeries = networkData.inventorySeries

    const handleChartTypeChange = (event: React.MouseEvent<HTMLElement>, newType: 'sales' | 'inventory' | null) => {
        if (newType !== null) {
            setChartType(newType)
        }
    }

    return (
        <Paper
            elevation={0}
            sx={{
                p: 2,
                borderRadius: 3,
                border: '1px solid',
                borderColor: 'divider',
                backgroundColor: 'white',
                mb: 3,
            }}
        >
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Box>
                    <Typography variant="h6" gutterBottom sx={{ fontWeight: 'bold', color: '#424242' }}>
                        {chartType === 'sales' ? 'Sales Performance by Region' : 'Inventory Turnover by Region'}
                    </Typography>
                    <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                        {chartType === 'sales'
                            ? 'Percentage above target for each region'
                            : 'Annual inventory turns by region'}
                    </Typography>
                </Box>
                <ToggleButtonGroup
                    value={chartType}
                    exclusive
                    onChange={handleChartTypeChange}
                    size="small"
                    sx={{ height: 36 }}
                >
                    <ToggleButton value="sales" sx={{ textTransform: 'none' }}>
                        Sales
                    </ToggleButton>
                    <ToggleButton value="inventory" sx={{ textTransform: 'none' }}>
                        Inventory
                    </ToggleButton>
                </ToggleButtonGroup>
            </Box>
            <Box sx={{ height: 300 }}>
                <GenericChart
                    options={chartType === 'sales' ? salesOptions : inventoryOptions}
                    series={chartType === 'sales' ? salesSeries : inventorySeries}
                    type={chartType === 'sales' ? 'bar' : 'line'}
                    height="100%"
                    width="100%"
                />
            </Box>
        </Paper>
    )
}

export default NetworkDataChart
