import { useEffect, useState } from 'react'
import { <PERSON>, Divider, Drawer, I<PERSON><PERSON><PERSON><PERSON>, useMediaQuery } from '@mui/material'
import ChevronRightIcon from '@mui/icons-material/ChevronRight'
import { ChevronLeft } from '@mui/icons-material'
import SidebarItems from './SidebarItems'
import { Logo, LogoSmall } from '@/components/common/layout/shared/logo/logo'

interface ItemType {
    isMobileSidebarOpen: boolean
    onSidebarClose: (event: React.MouseEvent<HTMLElement>) => void
    isSidebarOpen: boolean
}

const MSidebar = ({ isMobileSidebarOpen, onSidebarClose, isSidebarOpen }: ItemType) => {
    const [isCollapsed, setIsCollapsed] = useState(false) // Track collapse state
    const lgUp = useMediaQuery((theme: any) => theme.breakpoints.up('lg'))

    const sidebarWidth = isCollapsed ? '80px' : '240px' // Width for collapsed or expanded state

    const handleToggleCollapse = () => {
        setIsCollapsed((prev) => !prev) // Toggle the collapse state
    }

    const scrollbarStyles = {
        '&::-webkit-scrollbar': {
            width: '7px',
        },
        '&::-webkit-scrollbar-thumb': {
            backgroundColor: '#eff2f7',
            borderRadius: '15px',
        },
    }

    const logoContainerStyles = {
        display: 'flex',
        alignItems: 'center',
        justifyContent: isCollapsed ? 'center' : 'space-between',
        padding: '10px 16px',
        height: '70px',
        color: '#fff',
        transition: 'padding 0.3s ease',
        position: 'relative',
    }

    const toggleButtonStyles = {
        position: isCollapsed ? 'absolute' : 'relative',
        top: isCollapsed ? '100%' : 'auto',
        right: isCollapsed ? '0px' : 'auto',
        transform: isCollapsed ? 'translateY(-50%)' : 'none',
        marginLeft: isCollapsed ? '0' : 'auto',
        backgroundColor: '#fff',
        borderRadius: '50%',
        boxShadow: '0 0 8px rgba(0,0,0,0.2)',
        zIndex: 10,
        padding: '4px', // Smaller padding
        width: '24px', // Set fixed width
        height: '24px', // Set fixed height
    }

    const drawerStyles = {
        width: sidebarWidth,
        flexShrink: 0,
        transition: 'width 0.3s ease', // Smooth transition for collapsing
        overflow: 'hidden',
    }

    // Reset collapse state when on a small screen
    useEffect(() => {
        if (!lgUp) {
            setIsCollapsed(false)
        }
    }, [lgUp])

    if (lgUp) {
        return (
            <Box sx={drawerStyles}>
                <Drawer
                    anchor="left"
                    open={isSidebarOpen}
                    variant="permanent"
                    slotProps={{
                        paper: {
                            sx: {
                                boxSizing: 'border-box',
                                width: sidebarWidth,
                                overflow: 'hidden',
                                ...scrollbarStyles,
                            },
                        },
                    }}
                >
                    <Box
                        sx={{
                            height: '100%',
                            display: 'flex',
                            flexDirection: 'column',
                            position: 'relative',
                        }}
                    >
                        {/* Logo and Toggle Button */}
                        <Box sx={logoContainerStyles}>
                            {isCollapsed ? <LogoSmall /> : <Logo />}
                            <IconButton onClick={handleToggleCollapse} sx={toggleButtonStyles}>
                                {isCollapsed ? (
                                    <ChevronRightIcon fontSize={'small'} />
                                ) : (
                                    <ChevronLeft fontSize={'small'} />
                                )}
                            </IconButton>
                        </Box>
                        <Divider />
                        {/* Sidebar Content */}
                        <Box
                            sx={{
                                flex: 1,
                                overflowY: 'auto',
                                ...scrollbarStyles,
                            }}
                        >
                            <SidebarItems isCollapsed={isCollapsed} />
                        </Box>
                    </Box>
                </Drawer>
            </Box>
        )
    }

    return (
        <Drawer
            anchor="left"
            open={isMobileSidebarOpen}
            onClose={onSidebarClose}
            variant="temporary"
            slotProps={{
                paper: {
                    sx: {
                        boxSizing: 'border-box',
                        width: sidebarWidth,
                        overflow: 'hidden',
                        ...scrollbarStyles,
                    },
                },
            }}
        >
            <Box
                sx={{
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    position: 'relative',
                }}
            >
                {/* Logo */}
                <Box sx={logoContainerStyles}>
                    <Logo />
                </Box>

                {/* Sidebar Content */}
                <Box
                    sx={{
                        flex: 1,
                        overflowY: 'auto',
                        ...scrollbarStyles,
                    }}
                >
                    <SidebarItems isCollapsed={false} />
                </Box>
            </Box>
        </Drawer>
    )
}

export default MSidebar
