{"swagger": "2.0", "info": {"contact": {}}, "paths": {"/api/domains": {"post": {"description": "Retrieve all available domains", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["users"], "summary": "Get all domains", "responses": {"200": {"description": "Successfully retrieved domains", "schema": {"type": "array", "items": {"$ref": "#/definitions/userHandler.DomainModel"}}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/api/user/upsert": {"post": {"description": "Insert or update a user record", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["users"], "summary": "Upsert user", "parameters": [{"description": "User upsert request", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/model.UserReq"}}], "responses": {"200": {"description": "User record inserted successfully", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Invalid request body", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/dng/api/reports": {"post": {"security": [{"BearerAuth": []}], "description": "Fetch catalog reports based on query parameters and user context with pagination support", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["reports"], "summary": "Retrieve catalog reports", "parameters": [{"type": "integer", "default": 1, "description": "Page number", "name": "page", "in": "query"}, {"type": "integer", "default": 10, "description": "Items per page", "name": "limit", "in": "query"}, {"type": "string", "description": "Search term", "name": "search", "in": "query"}, {"type": "string", "default": "created_at", "description": "Sort field", "name": "sort_by", "in": "query"}, {"enum": ["asc", "desc"], "type": "string", "default": "desc", "description": "Sort order", "name": "sort_order", "in": "query"}, {"description": "User context for report retrieval", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/model.UserRequest"}}], "responses": {"200": {"description": "Successfully retrieved reports", "schema": {"$ref": "#/definitions/model.ReportResponse"}}, "400": {"description": "Invalid query parameters", "schema": {"$ref": "#/definitions/model.ErrorResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/model.ErrorResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/model.ErrorResponse"}}}}}, "/dng/api/reports/bulk-insert": {"post": {"security": [{"BearerAuth": []}], "description": "Upload an Excel file to create multiple reports with validation", "consumes": ["multipart/form-data"], "produces": ["application/json"], "tags": ["reports"], "summary": "Bulk create reports from Excel", "parameters": [{"type": "file", "description": "Excel file to upload (must be .xlsx or .xls)", "name": "file", "in": "formData", "required": true}, {"type": "string", "description": "User who created the reports", "name": "created_by", "in": "formData"}], "responses": {"200": {"description": "All records successfully processed", "schema": {"$ref": "#/definitions/model.ReportResponse"}}, "206": {"description": "Partial success - some records processed", "schema": {"$ref": "#/definitions/model.ReportResponse"}}, "304": {"description": "No records modified - all unchanged", "schema": {"$ref": "#/definitions/model.ReportResponse"}}, "400": {"description": "Invalid file or processing error", "schema": {"$ref": "#/definitions/model.ErrorResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/model.ErrorResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/model.ErrorResponse"}}}}}, "/dng/api/reports/bulk-insert-errors": {"get": {"security": [{"BearerAuth": []}], "description": "Download an error Excel file containing validation errors from bulk upload", "produces": ["application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"], "tags": ["reports"], "summary": "Download error file", "parameters": [{"type": "string", "description": "Temporary file path of the error file", "name": "filePath", "in": "query", "required": true}], "responses": {"200": {"description": "Excel file with error details", "schema": {"type": "file"}, "headers": {"Content-Disposition": {"type": "string", "description": "attachment; filename=bulk_upload_errors.xlsx"}, "Content-Type": {"type": "string", "description": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}}}, "400": {"description": "Invalid file path", "schema": {"$ref": "#/definitions/model.ErrorResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/model.ErrorResponse"}}, "404": {"description": "File not found", "schema": {"$ref": "#/definitions/model.ErrorResponse"}}}}}, "/dng/api/reports/download-template": {"get": {"security": [{"BearerAuth": []}], "description": "Provides an Excel template with proper formatting for bulk report upload", "produces": ["application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"], "tags": ["reports"], "summary": "Download Excel Template", "responses": {"200": {"description": "Excel template downloaded successfully", "schema": {"type": "file"}, "headers": {"Content-Disposition": {"type": "string", "description": "attachment; filename=report_template.xlsx"}, "Content-Type": {"type": "string", "description": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/model.ErrorResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/model.ErrorResponse"}}}}}, "/dng/api/reports/looker/looker-embed": {"get": {"security": [{"BearerAuth": []}], "description": "Get a secure embed URL for a specific Looker dashboard with SSO", "produces": ["application/json"], "tags": ["looker"], "summary": "Retrieve Looker Embed URL", "parameters": [{"type": "string", "description": "Looker Dashboard ID", "name": "dashboard_id", "in": "query", "required": true}], "responses": {"200": {"description": "Successfully retrieved Looker embed URL", "schema": {"$ref": "#/definitions/model.LookerEmbedResponse"}}, "400": {"description": "Missing dashboard ID", "schema": {"$ref": "#/definitions/model.ErrorResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/model.ErrorResponse"}}, "500": {"description": "Failed to retrieve embed URL", "schema": {"$ref": "#/definitions/model.ErrorResponse"}}}}}, "/dng/api/reports/looker/metadata": {"get": {"security": [{"BearerAuth": []}], "description": "Retrieve metadata for a Looker dashboard including fields and filters", "produces": ["application/json"], "tags": ["looker"], "summary": "Get Looker metadata", "parameters": [{"type": "string", "description": "Looker Dashboard ID", "name": "dashboard_id", "in": "query", "required": true}], "responses": {"200": {"description": "Successfully retrieved metadata", "schema": {"$ref": "#/definitions/model.LookerDashboardMetadata"}}, "400": {"description": "Missing dashboard ID", "schema": {"$ref": "#/definitions/model.ErrorResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/model.ErrorResponse"}}, "422": {"description": "Invalid dashboard ID", "schema": {"$ref": "#/definitions/model.ErrorResponse"}}, "500": {"description": "Failed to fetch metadata", "schema": {"$ref": "#/definitions/model.ErrorResponse"}}}}}, "/dng/api/reports/looker/render-pdf": {"post": {"security": [{"BearerAuth": []}], "description": "Create and download a PDF of a Looker dashboard with specified filters", "consumes": ["application/json"], "produces": ["application/pdf"], "tags": ["looker"], "summary": "Generate Looker Dashboard PDF", "parameters": [{"description": "Dashboard PDF Generation Request", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/model.DashboardRequest"}}], "responses": {"200": {"description": "PDF file of the dashboard", "schema": {"type": "file"}, "headers": {"Content-Disposition": {"type": "string", "description": "attachment; filename=dashboard_{id}.pdf"}, "Content-Type": {"type": "string", "description": "application/pdf"}}}, "400": {"description": "Invalid request", "schema": {"$ref": "#/definitions/model.ErrorResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/model.ErrorResponse"}}, "500": {"description": "Failed to generate PDF", "schema": {"$ref": "#/definitions/model.ErrorResponse"}}}}}, "/dng/api/reports/pbi/embed-token": {"get": {"security": [{"BearerAuth": []}], "description": "Get an embed token for a specific Power BI report with access validation", "produces": ["application/json"], "tags": ["powerbi"], "summary": "Retrieve Power BI Embed Token", "parameters": [{"type": "string", "description": "Power BI Report ID", "name": "report_id", "in": "query", "required": true}, {"type": "string", "description": "Power BI Group ID (use 'me' for personal workspace)", "name": "group_id", "in": "query", "required": true}], "responses": {"200": {"description": "Successfully retrieved embed token", "schema": {"$ref": "#/definitions/model.EmbedTokenResponse"}}, "400": {"description": "Missing required parameters", "schema": {"$ref": "#/definitions/model.ErrorResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/model.ErrorResponse"}}, "500": {"description": "Failed to retrieve embed token", "schema": {"$ref": "#/definitions/model.ErrorResponse"}}}}}, "/dng/api/reports/pbi/metadata": {"get": {"security": [{"BearerAuth": []}], "description": "Retrieve metadata for a Power BI report including datasets and parameters", "produces": ["application/json"], "tags": ["powerbi"], "summary": "Get Power BI metadata", "parameters": [{"type": "string", "description": "Power BI Report ID", "name": "report_id", "in": "query", "required": true}, {"type": "string", "description": "Power BI Group ID (use 'me' for personal workspace)", "name": "group_id", "in": "query", "required": true}], "responses": {"200": {"description": "Successfully retrieved metadata", "schema": {"$ref": "#/definitions/model.PowerBIReportMetaData"}}, "400": {"description": "Missing required parameters", "schema": {"$ref": "#/definitions/model.ErrorResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/model.ErrorResponse"}}, "422": {"description": "Invalid report ID or group ID", "schema": {"$ref": "#/definitions/model.ErrorResponse"}}, "500": {"description": "Failed to fetch metadata", "schema": {"$ref": "#/definitions/model.ErrorResponse"}}}}}, "/dng/api/reports/search": {"post": {"security": [{"BearerAuth": []}], "description": "Get available search options and filters based on user role and permissions", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["reports"], "summary": "Retrieve search options", "parameters": [{"description": "Search options request including user context", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/model.GetSearchOptionsRequest"}}], "responses": {"200": {"description": "Successfully retrieved search options", "schema": {"$ref": "#/definitions/model.ReportSearchOption"}}, "400": {"description": "Invalid request", "schema": {"$ref": "#/definitions/model.ErrorResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/model.ErrorResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/model.ErrorResponse"}}}}}, "/dng/api/reports/upsert-report": {"post": {"description": "Create a new report or update an existing one", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["reports"], "summary": "Create or update a report", "parameters": [{"description": "Report data to create or update", "name": "report", "in": "body", "required": true, "schema": {"$ref": "#/definitions/model.CreateReportDto"}}], "responses": {"201": {"description": "Successfully created report", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Invalid input", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/dng/api/reports/user-preferences": {"post": {"security": [{"BearerAuth": []}], "description": "Update the favorite/saved/view status of a report for a user", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["user-preferences"], "summary": "Update report favorite status", "parameters": [{"description": "Favorite report update request", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/model.FavoriteReportRequest"}}], "responses": {"200": {"description": "Successfully updated preference", "schema": {"$ref": "#/definitions/model.ReportWithPreference"}}, "400": {"description": "Invalid request", "schema": {"$ref": "#/definitions/model.ErrorResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/model.ErrorResponse"}}, "404": {"description": "Preference not found", "schema": {"$ref": "#/definitions/model.ErrorResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/model.ErrorResponse"}}}}}}, "definitions": {"model.CreateReportDto": {"type": "object", "required": ["category", "created_by", "domain_id", "name", "report_id"], "properties": {"category": {"type": "string"}, "created_at": {"description": "UserEmail    string `json:\"user_email\" binding:\"required\"`", "type": "string"}, "created_by": {"type": "string"}, "description": {"type": "string"}, "domain_id": {"type": "integer"}, "group_id": {"type": "string"}, "id": {"type": "integer"}, "is_favorite": {"type": "boolean"}, "is_saved": {"type": "boolean"}, "name": {"type": "string"}, "published_on": {"type": "string"}, "report_id": {"type": "string"}, "report_url": {"type": "string"}, "source_system": {"type": "string"}, "updated_at": {"type": "string"}}}, "model.DashboardRequest": {"type": "object", "required": ["dashboard_id"], "properties": {"dashboard_id": {"type": "string"}}}, "model.EmbedTokenResponse": {"type": "object", "properties": {"token": {"type": "string"}}}, "model.ErrorResponse": {"type": "object", "properties": {"details": {"type": "string", "example": "More specific error details"}, "message": {"type": "string", "example": "An error occurred"}, "status": {"type": "string", "example": "error"}}}, "model.FavoriteReportRequest": {"type": "object", "required": ["report_id", "user_id"], "properties": {"is_favorite": {"type": "boolean"}, "is_saved": {"type": "boolean"}, "report_id": {"type": "integer"}, "user_id": {"type": "integer"}, "view": {"type": "string"}}}, "model.GetSearchOptionsRequest": {"type": "object", "required": ["query", "role_name", "user_id"], "properties": {"query": {"type": "string"}, "role_name": {"description": "e.g., [\"ADMIN\"]", "type": "string"}, "user_id": {"type": "integer"}}}, "model.LookerDashboardMetadata": {"type": "object", "properties": {"id": {"type": "string"}, "title": {"type": "string"}}}, "model.LookerEmbedResponse": {"type": "object", "properties": {"url": {"type": "string"}}}, "model.PowerBIReportMetaData": {"type": "object", "properties": {"datasetId": {"type": "string"}, "embedUrl": {"type": "string"}, "groupId": {"type": "string"}, "id": {"type": "string"}, "name": {"type": "string"}, "webUrl": {"type": "string"}}}, "model.ReportResponse": {"type": "object", "properties": {"category": {"type": "string"}, "created_at": {"type": "string"}, "created_by": {"type": "string"}, "description": {"type": "string"}, "domain_id": {"type": "integer"}, "domain_name": {"type": "string"}, "group_id": {"description": "Nullable Group ID", "type": "string"}, "id": {"description": "Converted from uint to string", "type": "integer"}, "name": {"type": "string"}, "report_id": {"type": "string"}, "report_url": {"type": "string"}, "source_system": {"type": "string"}, "updated_at": {"type": "string"}}}, "model.ReportSearchOption": {"type": "object", "properties": {"category": {"type": "string"}, "domain_name": {"type": "string"}, "id": {"type": "integer"}, "name": {"type": "string"}, "source_system": {"type": "string"}}}, "model.ReportWithPreference": {"type": "object", "properties": {"category": {"type": "string"}, "created_at": {"type": "string"}, "created_by": {"type": "string"}, "description": {"type": "string"}, "domain_id": {"type": "integer"}, "group_id": {"type": "string"}, "id": {"type": "integer"}, "is_favorite": {"description": "Preference flags", "type": "boolean"}, "is_saved": {"type": "boolean"}, "name": {"type": "string"}, "published_on": {"type": "string"}, "report_id": {"type": "string"}, "report_url": {"type": "string"}, "source_system": {"type": "string"}, "updated_at": {"type": "string"}}}, "model.UserReq": {"type": "object", "required": ["ad_groups", "email"], "properties": {"ad_groups": {"type": "array", "items": {"type": "string"}}, "email": {"description": "Required field", "type": "string"}, "first_name": {"type": "string"}, "last_name": {"type": "string"}}}, "model.UserRequest": {"type": "object", "required": ["user_id"], "properties": {"role_name": {"type": "string"}, "user_id": {"description": "Required field", "type": "integer"}}}, "userHandler.DomainModel": {"type": "object", "properties": {"id": {"type": "integer"}, "name": {"type": "string"}}}}}