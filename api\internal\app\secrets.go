package app

import (
	"context"
	"fmt"
)

func (app *App) fetchSecrets() error {
	ctx := context.Background()

	secret, err := app.client.GetSecret(ctx, app.cfg.Dapr.SecretStore, app.cfg.Dapr.Secret, nil)
	if err != nil {
		app.log.Error("Error fetching secrets", "error", err)
		return fmt.Errorf("failed to fetch secrets: %w", err)
	}

	if secret != nil {
		for key := range app.cfg.Dapr.SecretKeys {
			if val, exists := secret[key]; exists {
				app.cfg.Dapr.SecretKeys[key] = val
				app.log.Info("Fetched secret", "key", key)

				// Apply secrets to configuration as needed
				if key == "db_password" {
					app.cfg.DB.Password = val
				}
			}
		}
	}

	return nil
}
