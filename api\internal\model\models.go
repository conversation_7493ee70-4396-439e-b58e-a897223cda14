package model

import (
	"time"
)

// UUID TO ID changes
func (User) TableName() string {
	return "users"
}

// User corresponds to m_user
type User struct {
	ID        int       `gorm:"primaryKey;autoIncrement" json:"id"`
	Email     string    `gorm:"unique;not null" json:"email"`
	FirstName string    `json:"first_name,omitempty"`
	LastName  string    `json:"last_name,omitempty"`
	CreatedAt time.Time `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt time.Time `gorm:"autoUpdateTime" json:"updated_at"`
	// Relationships
	UserReportPreference []UserReportPreference `gorm:"foreignKey:UserID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE" json:"userReportPreference"`
}

func (Role) TableName() string {
	return "roles"
}

type Role struct {
	ID           int       `gorm:"primaryKey;autoIncrement" json:"id"`
	RoleName     string    `gorm:"unique;not null" json:"role_name"`
	Description  string    `json:"description,omitempty"`
	CanView      bool      `gorm:"not null;default:true" json:"can_view"`
	CanEdit      bool      `gorm:"not null;default:false" json:"can_edit"`
	IsSuperAdmin bool      `gorm:"not null;default:false" json:"is_super_admin"`
	CreatedAt    time.Time `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt    time.Time `gorm:"autoUpdateTime" json:"updated_at"`
}

func (Report) TableName() string {
	return "reports"
}

// Report model
type Report struct {
	ID           int        `gorm:"primaryKey;autoIncrement" json:"id"`
	Name         string     `gorm:"not null" json:"name"`
	ReportID     string     `gorm:"not null" json:"report_id"`
	GroupID      string     `json:"group_id,omitempty"`
	Description  string     `gorm:"not null" json:"description"`
	Category     string     `gorm:"not null" json:"category"` // e.g., Financial, Ops
	DomainID     int        `gorm:"not null;index" json:"domain_id"`
	SourceSystem string     `gorm:"not null" json:"source_system"`
	ReportUrl    string     `json:"report_url"`
	CreatedBy    string     `json:"created_by,omitempty"`
	CreatedAt    time.Time  `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt    time.Time  `gorm:"autoUpdateTime" json:"updated_at"`
	PublishedOn  *time.Time `gorm:"type:date;default:NULL" json:"published_on"`
	// Relationships
	Domain               Domain                 `gorm:"foreignKey:DomainID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE" json:"domain"`
	UserReportPreference []UserReportPreference `gorm:"foreignKey:ReportID;references:ID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE" json:"user_report_reference"`
}

func (UserReportPreference) TableName() string {
	return "user_report_preference"
}

// UserReportPreference corresponds to m_user_report_preference
type UserReportPreference struct {
	ID         int       `gorm:"primaryKey;autoIncrement" json:"id"`
	UserID     int       `gorm:"not null;index" json:"user_id"`
	ReportID   int       `gorm:"not null;index" json:"report_id"`
	IsFavorite bool      `gorm:"default:false" json:"is_favorite"`
	IsSaved    bool      `gorm:"default:false" json:"is_saved"`
	CreatedAt  time.Time `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt  time.Time `gorm:"autoUpdateTime" json:"updated_at"`
	// Relationships
	User   User   `gorm:"foreignKey:UserID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE" json:"user"`
	Report Report `gorm:"foreignKey:ReportID;references:ID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE" json:"report"`
}

func (Domain) TableName() string {
	return "domains"
}

// Domain corresponds to m_domain
type Domain struct {
	ID          int    `gorm:"primaryKey;autoIncrement" json:"id"`
	Name        string `gorm:"not null" json:"name"`
	Description string `json:"description"`
	// Relationships
	Report []Report `gorm:"foreignKey:DomainID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE" json:"report"`
}

type NewsAlert struct {
	ID            uint      `gorm:"primaryKey" json:"id"`
	Type          string    `json:"type" binding:"required"`
	Title         string    `json:"title" binding:"required"`
	Description   string    `json:"description" binding:"required"`
	StartDateTime time.Time `json:"start_date_time" binding:"required" time_format:"2006-01-02T15:04:05Z07:00"`
	EndDateTime   time.Time `json:"end_date_time" binding:"required" time_format:"2006-01-02T15:04:05Z07:00"`
	IsVisible     bool      `json:"is_visible"`
	Status        string    `json:"status"`
	CreatedBy     string    `json:"created_by" binding:"required"`
	CreatedAt     time.Time `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt     time.Time `gorm:"autoUpdateTime" json:"updated_at"`
}

func (NewsAlert) TableName() string {
	return "news_alerts"
}

// model/news_alert.go
type UpdateNewsAlertDTO struct {
	ID            uint      `json:"id" binding:"required"`
	Type          string    `json:"type"`
	Title         string    `json:"title"`
	Description   string    `json:"description"`
	StartDateTime time.Time `json:"start_date_time"`
	EndDateTime   time.Time `json:"end_date_time"`
	IsVisible     bool      `json:"is_visible"`
	Status        string    `json:"status"`
}

type UserDomainAccess struct {
	ID        int       `gorm:"primaryKey;autoIncrement" json:"id"`
	UserID    int       `gorm:"not null;index:idx_user_domain,unique" json:"user_id"`
	DomainID  int       `gorm:"not null;index:idx_user_domain,unique" json:"domain_id"`
	RoleID    int       `gorm:"not null" json:"role_id"`
	CreatedAt time.Time `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt time.Time `gorm:"autoUpdateTime" json:"updated_at"`

	// Relationships
	User   User   `gorm:"foreignKey:UserID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE"`
	Domain Domain `gorm:"foreignKey:DomainID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE"`
	Role   Role   `gorm:"foreignKey:RoleID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE"`
}

func (UserDomainAccess) TableName() string {
	return "user_domain_access"
}

type ADGroupPattern struct {
	ID            int       `gorm:"primaryKey;autoIncrement" json:"id"`
	Pattern       string    `gorm:"not null" json:"pattern"` // e.g. "finance-admin"
	RoleID        int       `gorm:"not null" json:"role_id"`
	DomainPattern string    `json:"domain_pattern,omitempty"` // optional regex/substr for domain
	Description   string    `json:"description,omitempty"`
	CreatedAt     time.Time `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt     time.Time `gorm:"autoUpdateTime" json:"updated_at"`

	Role Role `gorm:"foreignKey:RoleID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE"`
}

func (ADGroupPattern) TableName() string {
	return "ad_group_role_mapping"
}
