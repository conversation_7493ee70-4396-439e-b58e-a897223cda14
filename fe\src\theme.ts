'use client'
import { createTheme } from '@mui/material/styles'

const montserrat = { style: { fontFamily: 'Montserrat, sans-serif' } }

const theme = createTheme({
    direction: 'ltr',
    palette: {
        primary: {
            main: '#FFF200',
            light: '#ECF2FF',
            dark: '#FFF200',
        },
        secondary: {
            main: '#000000',
            light: '#E8F7FF',
            dark: '#000000',
        },
        success: {
            main: '#3aa866',
            light: '#177965',
            dark: '#02b3a9',
            contrastText: '#ffffff',
        },
        info: {
            main: '#539BFF',
            light: '#EBF3FE',
            dark: '#1682d4',
            contrastText: '#ffffff',
        },
        error: {
            main: '#ED021E',
            light: '#f66565',
            dark: '#ff0f0f',
            contrastText: '#ffffff',
        },
        warning: {
            main: '#FFAE1F',
            light: '#FEF5E5',
            dark: '#ae8e59',
            contrastText: '#ffffff',
        },
        grey: {
            100: '#F2F6FA',
            200: '#EAEFF4',
            300: '#DFE5EF',
            400: '#7C8FAC',
            500: '#5A6A85',
            600: '#2A3547',
        },
        text: {
            primary: '#2A3547',
            secondary: '#5A6A85',
        },
        action: {
            disabledBackground: 'rgba(73,82,88,0.12)',
            hoverOpacity: 0.02,
            hover: '#f6f9fc',
        },
        divider: '#e5eaef',
    },
    typography: {
        fontFamily: montserrat.style.fontFamily,
        h1: {
            fontWeight: 600,
            fontSize: '2.25rem',
            lineHeight: '2.75rem',
            fontFamily: montserrat.style.fontFamily,
        },
        h2: {
            fontWeight: 600,
            fontSize: '1.875rem',
            lineHeight: '2.25rem',
            fontFamily: montserrat.style.fontFamily,
        },
        h3: {
            fontWeight: 600,
            fontSize: '1.5rem',
            lineHeight: '1.75rem',
            fontFamily: montserrat.style.fontFamily,
        },
        h4: {
            fontWeight: 600,
            fontSize: '1.3125rem',
            lineHeight: '1.6rem',
        },
        h5: {
            fontWeight: 600,
            fontSize: '1.125rem',
            lineHeight: '1.6rem',
        },
        h6: {
            fontWeight: 600,
            fontSize: '1rem',
            lineHeight: '1.2rem',
        },
        button: {
            textTransform: 'capitalize',
            fontWeight: 400,
        },
        body1: {
            fontSize: '0.875rem',
            fontWeight: 400,
            lineHeight: '1.334rem',
        },
        body2: {
            fontSize: '0.75rem',
            letterSpacing: '0rem',
            fontWeight: 400,
            lineHeight: '1rem',
        },
        subtitle1: {
            fontSize: '0.875rem',
            fontWeight: 500,
        },
        subtitle2: {
            fontSize: '0.875rem',
            fontWeight: 400,
        },
        caption: {
            fontSize: '0.625rem',
            fontWeight: 400,
        },
    },
    components: {
        MuiCssBaseline: {
            styleOverrides: {
                '.MuiPaper-elevation9, .MuiPopover-root .MuiPaper-elevation': {
                    boxShadow:
                        'rgb(145 158 171 / 30%) 0px 0px 2px 0px, rgb(145 158 171 / 12%) 0px 12px 24px -4px !important',
                },
            },
        },
        MuiCard: {
            styleOverrides: {
                root: {
                    borderRadius: '7px',
                },
            },
        },
        MuiListItemText: {
            styleOverrides: {
                primary: {
                    fontWeight: '500',
                },
            },
        },
        MuiTextField: {
            styleOverrides: {
                root: {
                    '& .MuiFormHelperText-root': {
                        marginLeft: '0px', // Adjust as needed
                    },
                },
            },
        },
        // Setting default color to secondary and size to small for major components
        /*MuiButton: {
            defaultProps: {
                color: 'secondary',
                size: 'small',
            },
        },
        MuiTextField: {
            defaultProps: {
                color: 'secondary',
                size: 'small',
            },
        },
        MuiCheckbox: {
            defaultProps: {
                color: 'secondary',
                size: 'small',
            },
        },
        MuiRadio: {
            defaultProps: {
                color: 'secondary',
                size: 'small',
            },
        },
        MuiSwitch: {
            defaultProps: {
                color: 'secondary',
                size: 'small',
            },
        },
        MuiFormControl: {
            defaultProps: {
                color: 'secondary',
                size: 'small',
            },
        },
        MuiSelect: {
            defaultProps: {
                color: 'secondary',
                size: 'small',
            },
        },
        MuiInputBase: {
            defaultProps: {
                color: 'secondary',
                size: 'small',
            },
        },
        MuiInputLabel: {
            defaultProps: {
                color: 'secondary',
                size: 'small',
            },
        },
        MuiOutlinedInput: {
            defaultProps: {
                color: 'secondary',
                size: 'small',
            },
        },
        MuiFilledInput: {
            defaultProps: {
                color: 'secondary',
                size: 'small',
            },
        },
        MuiInput: {
            defaultProps: {
                color: 'secondary',
                size: 'small',
            },
        },
        MuiIconButton: {
            defaultProps: {
                color: 'secondary',
                size: 'small',
            },
        },
        MuiChip: {
            defaultProps: {
                color: 'secondary',
                size: 'small',
            },
        },
        MuiFab: {
            defaultProps: {
                color: 'secondary',
                size: 'small',
            },
        },
        MuiTable: {
            defaultProps: {
                size: 'small',
            },
        },
        MuiTabs: {
            defaultProps: {
                textColor: 'secondary',
                indicatorColor: 'secondary',
            },
        },
        MuiPagination: {
            defaultProps: {
                color: 'secondary',
                size: 'small',
            },
        },
        MuiCircularProgress: {
            defaultProps: {
                color: 'secondary',
                size: 'small',
            },
        },
        MuiLinearProgress: {
            defaultProps: {
                color: 'secondary',
            },
        },
        MuiRating: {
            defaultProps: {
                size: 'small',
            },
        },
        MuiSlider: {
            defaultProps: {
                color: 'secondary',
                size: 'small',
            },
        },
        MuiToggleButton: {
            defaultProps: {
                color: 'secondary',
                size: 'small',
            },
        },*/
    },
})

export { theme }
