import Login from '@/app/authentication/auth/login'
import { cleanup, render, screen } from '@testing-library/react'

jest.mock('@/components/common/layout/shared/logo/logo', () => ({
    Logo: () => <div data-testid="logo" />,
}))

jest.mock('@/app/authentication/auth/auth-login', () => () => <div data-testid="auth-login" />)

// Mock useSearchParams to prevent errors in child components
jest.mock('next/navigation', () => ({
    ...jest.requireActual('next/navigation'),
    useSearchParams: jest.fn(() => ({
        get: () => null,
    })),
}))

describe('Login Page', () => {
    afterEach(() => {
        cleanup()
    })

    it('renders login page contents', () => {
        render(<Login />)

        // Main heading
        expect(screen.getByText('DNG Enterprise')).toBeInTheDocument()

        // Subtext
        expect(screen.getByText('Self-Service dashboards to')).toBeInTheDocument()
        expect(screen.getByText('optimize your results!')).toBeInTheDocument()

        // Child components
        expect(screen.getByTestId('logo')).toBeInTheDocument()
        expect(screen.getByTestId('auth-login')).toBeInTheDocument()
    })
})
