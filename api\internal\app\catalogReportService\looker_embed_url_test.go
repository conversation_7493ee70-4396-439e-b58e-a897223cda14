package catalogReportService

import (
	// "dng-module/internal/app/catalogReportService"
	// "dng-module/internal/app/catalogReportService"
	"dng-module/internal/model"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"os"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
)

// --- Helper: set env variables for test server ---
func setLookerEnv(t *testing.T, url string) {
	t.Helper()
	os.Setenv("LOOKERSDK_BASE_URL", url)
	os.Setenv("LOOKER_CLIENT_ID", "test_client_id")
	os.Setenv("LOOKER_CLIENT_SECRET", "test_client_secret")
}

func TestGetLookerEmbedURL(t *testing.T) {
	mockLoginResponse := `{"access_token":"mock_access_token"}`
	mockEmbedResponse := `{"url":"https://mocklooker.com/embed/dashboards/1"}`

	// Happy path: login and embed URL succeed
	t.Run("success", func(t *testing.T) {
		ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			switch {
			case strings.Contains(r.URL.Path, "/login"):
				w.Header().Set("Content-Type", "application/json")
				fmt.Fprint(w, mockLoginResponse)
			case strings.Contains(r.URL.Path, "/embed/token_url/me"):
				assert.Equal(t, "Bearer mock_access_token", r.Header.Get("Authorization"))
				w.Header().Set("Content-Type", "application/json")
				fmt.Fprint(w, mockEmbedResponse)
			default:
				http.NotFound(w, r)
			}
		}))
		defer ts.Close()

		setLookerEnv(t, ts.URL)

		url, err := GetLookerEmbedURL("1")
		assert.NoError(t, err)
		assert.Equal(t, "https://mocklooker.com/embed/dashboards/1", url)
	})

	// Error cases:
	// 1) Login POST request error
	t.Run("login http error", func(t *testing.T) {
		// Close server immediately to cause error on POST
		ts := httptest.NewServer(http.NotFoundHandler())
		ts.Close()

		setLookerEnv(t, ts.URL)

		url, err := GetLookerEmbedURL("1")
		assert.Error(t, err)
		assert.Empty(t, url)
	})

	// 2) Login response JSON decode error (malformed JSON)
	t.Run("login decode error", func(t *testing.T) {
		ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			if strings.Contains(r.URL.Path, "/login") {
				w.Write([]byte("not json"))
			}
		}))
		defer ts.Close()

		setLookerEnv(t, ts.URL)

		url, err := GetLookerEmbedURL("1")
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "failed to decode login response")
		assert.Empty(t, url)
	})

	// 3) Embed URL request HTTP error
	t.Run("embed http error", func(t *testing.T) {
		ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			if strings.Contains(r.URL.Path, "/login") {
				w.Header().Set("Content-Type", "application/json")
				fmt.Fprint(w, mockLoginResponse)
			} else if strings.Contains(r.URL.Path, "/embed/token_url/me") {
				// Close connection to simulate error
				hj, ok := w.(http.Hijacker)
				if ok {
					conn, _, _ := hj.Hijack()
					conn.Close()
				}
			}
		}))
		defer ts.Close()

		setLookerEnv(t, ts.URL)

		url, err := GetLookerEmbedURL("1")
		assert.Error(t, err)
		assert.Empty(t, url)
	})

	// 4) Embed URL request non-200 status code
	t.Run("embed non-200 status", func(t *testing.T) {
		ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			if strings.Contains(r.URL.Path, "/login") {
				w.Header().Set("Content-Type", "application/json")
				fmt.Fprint(w, mockLoginResponse)
			} else if strings.Contains(r.URL.Path, "/embed/token_url/me") {
				http.Error(w, "forbidden", http.StatusForbidden)
			}
		}))
		defer ts.Close()

		setLookerEnv(t, ts.URL)

		url, err := GetLookerEmbedURL("1")
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "received status 403")
		assert.Empty(t, url)
	})

	// 5) Embed URL decode error (malformed JSON)
	t.Run("embed decode error", func(t *testing.T) {
		ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			if strings.Contains(r.URL.Path, "/login") {
				w.Header().Set("Content-Type", "application/json")
				fmt.Fprint(w, mockLoginResponse)
			} else if strings.Contains(r.URL.Path, "/embed/token_url/me") {
				w.Header().Set("Content-Type", "application/json")
				fmt.Fprint(w, "invalid json")
			}
		}))
		defer ts.Close()

		setLookerEnv(t, ts.URL)

		url, err := GetLookerEmbedURL("1")
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "failed to decode embed response")
		assert.Empty(t, url)
	})
}

func TestGetLookerDashMetadata(t *testing.T) {
	mockAccessToken := "mock_access_token"
	mockMetadata := model.LookerDashboardMetadata{
		Title: "Test Dashboard",
	}

	// Happy path
	t.Run("success", func(t *testing.T) {
		ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			switch {
			case strings.Contains(r.URL.Path, "/login"):
				w.Header().Set("Content-Type", "application/json")
				fmt.Fprintf(w, `{"access_token": "%s"}`, mockAccessToken)
			case strings.Contains(r.URL.Path, "/dashboards/123"):
				assert.Equal(t, "token "+mockAccessToken, r.Header.Get("Authorization"))
				w.Header().Set("Content-Type", "application/json")
				json.NewEncoder(w).Encode(mockMetadata)
			default:
				http.NotFound(w, r)
			}
		}))
		defer ts.Close()

		setLookerEnv(t, ts.URL)

		meta, err := GetLookerDashMetadata("123")
		assert.NoError(t, err)
		assert.Equal(t, "Test Dashboard", meta.Title)
	})

	// Login HTTP error
	t.Run("login http error", func(t *testing.T) {
		ts := httptest.NewServer(http.NotFoundHandler())
		ts.Close()

		setLookerEnv(t, ts.URL)

		meta, err := GetLookerDashMetadata("123")
		assert.Error(t, err)
		assert.Nil(t, meta)
	})

	// Login JSON decode error
	t.Run("login decode error", func(t *testing.T) {
		ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			if strings.Contains(r.URL.Path, "/login") {
				w.Write([]byte("not json"))
			}
		}))
		defer ts.Close()

		setLookerEnv(t, ts.URL)

		meta, err := GetLookerDashMetadata("123")
		assert.Error(t, err)
		assert.Nil(t, meta)
	})

	// Metadata request creation error (simulate by passing invalid dashboardID that makes URL invalid)
	t.Run("metadata request creation error", func(t *testing.T) {
		setLookerEnv(t, "http://valid.url") // Valid base url

		// Pass invalid dashboardID causing fmt.Sprintf to create invalid URL (simulate)
		meta, err := GetLookerDashMetadata(string([]byte{0xff, 0xfe, 0xfd}))
		assert.Error(t, err)
		assert.Nil(t, meta)
	})

	// Metadata HTTP error
	t.Run("metadata http error", func(t *testing.T) {
		ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			switch {
			case strings.Contains(r.URL.Path, "/login"):
				w.Header().Set("Content-Type", "application/json")
				fmt.Fprintf(w, `{"access_token": "%s"}`, mockAccessToken)
			case strings.Contains(r.URL.Path, "/dashboards/123"):
				http.Error(w, "not found", http.StatusNotFound)
			default:
				http.NotFound(w, r)
			}
		}))
		defer ts.Close()

		setLookerEnv(t, ts.URL)

		meta, err := GetLookerDashMetadata("123")
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "not found")
		assert.Nil(t, meta)
	})

	// Metadata decode error
	t.Run("metadata decode error", func(t *testing.T) {
		ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			switch {
			case strings.Contains(r.URL.Path, "/login"):
				w.Header().Set("Content-Type", "application/json")
				fmt.Fprintf(w, `{"access_token": "%s"}`, mockAccessToken)
			case strings.Contains(r.URL.Path, "/dashboards/123"):
				w.Header().Set("Content-Type", "application/json")
				w.Write([]byte("invalid json"))
			default:
				http.NotFound(w, r)
			}
		}))
		defer ts.Close()

		setLookerEnv(t, ts.URL)

		meta, err := GetLookerDashMetadata("123")
		assert.Error(t, err)
		assert.Nil(t, meta)
	})
}
