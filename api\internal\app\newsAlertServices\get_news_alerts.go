package newsAlertService

import (
	"dng-module/config"
	"dng-module/internal/model"
	"dng-module/internal/utils"
	"strings"

	"fmt"
	"time"
)

// GetNewsAlertsParams defines the parameters for fetching news alerts.
type GetNewsAlertsParams struct {
	Search    string   // search string for title
	SortBy    string   // column to sort by (default: "start_date_time")
	Order     string   // asc or desc (default: "desc")
	Limit     int      // number of items to return
	Skip      int      // number of items to skip (offset)
	Status    []string // filter by status
	TodayOnly bool     // whether to filter only today's alerts
}

// GetNewsAlerts retrieves news alerts with optional filters.
func GetNewsAlerts(params GetNewsAlertsParams) ([]model.NewsAlert, int64, error) {
	var alerts []model.NewsAlert
	var totalCount int64

	// Default values
	if params.SortBy == "" {
		params.SortBy = "start_date_time"
	}
	if strings.ToLower(params.Order) != "asc" {
		params.Order = "desc"
	}
	if params.Limit <= 0 {
		params.Limit = 15
	}

	db := config.DB.Model(&model.NewsAlert{})

	// Search filter
	if params.Search != "" {
		db = db.Where("LOWER(title) LIKE ?", "%"+strings.ToLower(params.Search)+"%")
	}

	// Filter by status
	if len(params.Status) > 0 {
		db = db.Where("status IN ?", params.Status)
	}

	// Apply "Today Only" filter if requested
	if params.TodayOnly {
		now := time.Now().UTC()
		fmt.Println("Current Time:", now)

		db = db.Where("start_date_time <= ? AND end_date_time >= ?", now, now)
	}

	// Count total matching records before limit/offset
	if err := db.Count(&totalCount).Error; err != nil {
		return nil, 0, err
	}

	// Apply sort, limit, and offset
	db = db.Order(params.SortBy + " " + params.Order).
		Offset(params.Skip).
		Limit(params.Limit)

	// Fetch alerts
	if err := db.Find(&alerts).Error; err != nil {
		return nil, 0, err
	}

	return alerts, totalCount, nil
}

func GetVisibleNewsAlerts() ([]model.NewsAlert, error) {
	var alerts []model.NewsAlert
	var filteredAlerts []model.NewsAlert

	currentTime := time.Now().UTC() // 🔥 Use UTC here!
	utils.Logger.Infof("Fetching visible news alerts as of %s", currentTime)

	err := config.DB.
		Model(&model.NewsAlert{}).
		Where("is_visible = ?", true).
		Find(&alerts).Error

	if err != nil {
		utils.Logger.Errorf("Failed to fetch visible news alerts from database: %v", err)
		return nil, err
	}

	utils.Logger.Infof("Fetched %d visible news alerts", len(alerts))

	for _, alert := range alerts {
		switch alert.Type {
		case "alert":
			if currentTime.After(alert.StartDateTime) && currentTime.Before(alert.EndDateTime) {
				filteredAlerts = append(filteredAlerts, alert)
			}
		case "event":
			if currentTime.Before(alert.EndDateTime) {
				filteredAlerts = append(filteredAlerts, alert)
			}
		case "news":
			if currentTime.Before(alert.EndDateTime) {
				filteredAlerts = append(filteredAlerts, alert)
			}
		}
	}

	utils.Logger.Infof("Returning %d visible alerts after filtering", len(filteredAlerts))
	return filteredAlerts, nil
}
