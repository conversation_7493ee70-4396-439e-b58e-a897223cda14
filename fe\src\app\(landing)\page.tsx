// app/dashboard/page.tsx
import { getServerSession } from 'next-auth'

import { Box, InputAdornment, Typography } from '@mui/material'
import Grid from '@mui/material/Grid2'
import { Search } from '@mui/icons-material'
import AlertsCarousel from '@/app/(landing)/components/AlertsCarousel'
import FeatureCard from '@/app/(landing)/components/FeatureCard'
import CustomTextField from '@/components/common/forms/theme-elements/CustomTextField'
import AuthOptions from '@/utils/authOptions'
import { fetchVisibleEvents } from '@/app/alerts/services'
import MessageBanner from '@/app/(landing)/components/MessageBanner'
import GenericLayout from '@/components/common/layout/GenericLayout'
import loginBg from '../../../public/images/landing/wave.svg'

const featureCards = [
    { title: 'Reports Catalog', icon: 'chart', link: 'reports' },
    {
        title: 'Self-Service',
        icon: 'list',
        link: 'https://dgstg.cloud.looker.com/explore/init/summ_item_by_store?qid=jVqu936fhqhhe38pyIOv9Q&origin_space=130&toggle=fil,vis',
    },
    { title: 'Omni Bot', icon: 'chat', link: '/omni-bot' },
    { title: 'Data Catalog', icon: 'presentation', link: 'data-catalog' },
] as const

const LandingHeader = ({ name }: { name: string }) => (
    <Box sx={{ textAlign: 'center', maxWidth: 'md', mx: 'auto', my: 4 }}>
        <Typography variant="h6" gutterBottom>
            Welcome, {name} 👋
        </Typography>
        <Typography variant="body2" color="text.primary" sx={{ mb: 3 }}>
            Real-time data visualization, seamless integration with multiple
            <br />
            data sources, and customizable reports to suit your needs.
        </Typography>
    </Box>
)

const SearchBar = () => (
    <Box sx={{ maxWidth: 'sm', mx: 'auto' }}>
        <CustomTextField
            placeholder="Start exploring now"
            sx={{
                '& .MuiInputBase-root': {
                    borderRadius: 20,
                    border: '1px solid #ccc',
                },
            }}
            slotProps={{
                input: {
                    startAdornment: (
                        <InputAdornment position="start" sx={{ ml: 1 }}>
                            <Search />
                        </InputAdornment>
                    ),
                    disableUnderline: true,
                },
            }}
            variant="standard"
            size="small"
        />
    </Box>
)

const FeatureGrid = () => (
    <Grid container spacing={2}>
        {featureCards.map((feature) => (
            <Grid size={{ xs: 12, sm: 6, lg: 3 }} key={feature.title}>
                <FeatureCard title={feature.title} icon={feature.icon} link={feature.link} />
            </Grid>
        ))}
    </Grid>
)

export default async function Dashboard() {
    const session = await getServerSession(AuthOptions)
    const userName = session?.user?.name ?? 'Guest'
    const token = session?.user?.access_token ?? ''

    const alerts = await fetchVisibleEvents(token)

    return (
        <GenericLayout showSidebar={false} useContainer={true} showLogoInHeader={true} backgroundImageSrc={loginBg.src}>
            <MessageBanner alerts={alerts} />

            <LandingHeader name={userName} />

            <Grid container spacing={3}>
                <Grid size={{ xs: 12 }}>
                    <Box sx={{ display: 'flex', flexDirection: 'column' }}>
                        <FeatureGrid />
                    </Box>
                </Grid>
            </Grid>

            <Grid container spacing={1}>
                <Grid size={{ xs: 12 }}>
                    <Box sx={{ mt: 5 }}>
                        <AlertsCarousel alerts={alerts ?? []} />
                    </Box>
                </Grid>
            </Grid>
        </GenericLayout>
    )
}
