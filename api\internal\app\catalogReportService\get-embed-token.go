package catalogReportService

import (
	"bytes"
	"dng-module/config"
	"dng-module/internal/model"
	"dng-module/internal/utils"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"
)

const (
	headerContentType   = "Content-Type"
	headerAuthorization = "Authorization"
	authBearerPrefix    = "Bearer "
)

type PowerBIAPIError struct {
	StatusCode int
	Message    string
}

func (e *PowerBIAPIError) Error() string {
	return e.Message
}

// GetAccessToken fetches an access token from Azure AD
func GetAccessToken(cfg config.Config) (string, error) {
	tenantID := cfg.Dapr.SecretKeys["azure_tenant_id"]
	clientID := cfg.Dapr.SecretKeys["azure_client_id"]
	clientSecret := cfg.Dapr.SecretKeys["azure_client_secret"]

	if tenantID == "" || clientID == "" || clientSecret == "" {
		return "", errors.New("missing Azure AD environment variables")
	}

	authURL := fmt.Sprintf("https://login.microsoftonline.com/%s/oauth2/v2.0/token", tenantID)

	// Correctly format data as x-www-form-urlencoded
	data := url.Values{}
	data.Set("grant_type", "client_credentials")
	data.Set("client_id", clientID)
	data.Set("client_secret", clientSecret)
	data.Set("scope", "https://analysis.windows.net/powerbi/api/.default")

	// Send the request
	req, err := http.NewRequest("POST", authURL, strings.NewReader(data.Encode()))
	if err != nil {
		utils.Logger.Error("Failed to create request: ", err)
		return "", err
	}
	req.Header.Set(headerContentType, "application/x-www-form-urlencoded")

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		utils.Logger.Error("Failed to get access token: ", err)
		return "", err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return "", fmt.Errorf("failed to get access token, status: %d, response: %s", resp.StatusCode, string(body))
	}

	var tokenResponse model.TokenResponse
	if err := json.NewDecoder(resp.Body).Decode(&tokenResponse); err != nil {
		return "", err
	}

	utils.Logger.Info("Fetching  Access token:", tokenResponse.AccessToken)
	return tokenResponse.AccessToken, nil
}

func GetEmbedToken(groupID, reportID, accessToken string) (string, error) {
	var embedURL string
	var reqBody []byte
	var err error

	if groupID == "me" {
		// Step 1: Get dataset ID for the report
		datasetID, err := GetDatasetIDForMyWorkspaceReport(reportID, accessToken)
		if err != nil {
			return "", fmt.Errorf("failed to get dataset ID: %w", err)
		}
		// Step 2: Use generic GenerateToken API
		embedURL = "https://api.powerbi.com/v1.0/myorg/GenerateToken"
		reqBody, err = json.Marshal(map[string]interface{}{
			"reports": []map[string]string{
				{"id": reportID},
			},
			"datasets": []map[string]string{
				{"id": datasetID},
			},
			"accessLevel": "View",
		})
		if err != nil {
			return "", err
		}
	} else {
		// Named or shared workspace
		embedURL = fmt.Sprintf("https://api.powerbi.com/v1.0/myorg/groups/%s/reports/%s/GenerateToken", groupID, reportID)

		reqBody, _ = json.Marshal(map[string]string{
			"accessLevel": "View",
		})
	}

	// Send POST request
	req, err := http.NewRequest("POST", embedURL, bytes.NewBuffer(reqBody))
	if err != nil {
		utils.Logger.Error("Failed to create request: ", err)
		return "", err
	}

	req.Header.Set("Authorization", authBearerPrefix+accessToken)
	req.Header.Set(headerContentType, "application/json")

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		utils.Logger.Error("Failed to get embed token: ", err)
		return "", err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return "", fmt.Errorf("failed to get embed token, status: %d, response: %s", resp.StatusCode, string(body))
	}

	var embedTokenResponse model.EmbedTokenResponse
	if err := json.NewDecoder(resp.Body).Decode(&embedTokenResponse); err != nil {
		return "", err
	}

	if embedTokenResponse.Token == "" {
		return "", errors.New("empty embed token received")
	}

	utils.Logger.Info("Successfully fetched Embed Token", embedTokenResponse.Token)
	return embedTokenResponse.Token, nil
}

func GetPowerBIMetaData(groupID, reportID, accessToken string) (*model.PowerBIReportMetaData, error) {
	var apiURL string
	if groupID == "" || groupID == "me" {
		apiURL = fmt.Sprintf("https://api.powerbi.com/v1.0/myorg/reports/%s", reportID)
	} else {
		apiURL = fmt.Sprintf("https://api.powerbi.com/v1.0/myorg/groups/%s/reports/%s", groupID, reportID)
	}

	req, err := http.NewRequest("GET", apiURL, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}
	req.Header.Set("Authorization", authBearerPrefix+accessToken)
	req.Header.Set(headerContentType, "application/json")

	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("API request failed: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, &PowerBIAPIError{
			StatusCode: resp.StatusCode,
			Message:    fmt.Sprintf("Power BI API returned status %d: %s", resp.StatusCode, string(body)),
		}
	}

	var apiResponse struct {
		ID        string    `json:"id"`
		Name      string    `json:"name"`
		WebURL    string    `json:"webUrl"`
		EmbedURL  string    `json:"embedUrl"`
		DatasetID string    `json:"datasetId"`
		CreatedAt time.Time `json:"createdDateTime"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&apiResponse); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	// Ensure required fields are present
	if apiResponse.Name == "" {
		apiResponse.Name = "Unnamed Report" // Default name
	}

	// Convert to your model
	metadata := &model.PowerBIReportMetaData{
		ID:        apiResponse.ID,
		Name:      apiResponse.Name,
		WebURL:    apiResponse.WebURL,
		EmbedURL:  apiResponse.EmbedURL,
		DatasetID: apiResponse.DatasetID,
		GroupID:   groupID, // Explicitly set from input
	}

	return metadata, nil
}
func GetDatasetIDForMyWorkspaceReport(reportID, accessToken string) (string, error) {
	url := fmt.Sprintf("https://api.powerbi.com/v1.0/myorg/reports/%s", reportID)

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return "", err
	}
	req.Header.Set("Authorization", authBearerPrefix+accessToken)

	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return "", fmt.Errorf("failed to fetch report details, status: %d, body: %s", resp.StatusCode, string(body))
	}

	var report struct {
		DatasetID string `json:"datasetId"`
	}
	if err := json.NewDecoder(resp.Body).Decode(&report); err != nil {
		return "", err
	}

	if report.DatasetID == "" {
		return "", errors.New("dataset ID not found in report metadata")
	}

	return report.DatasetID, nil
}
