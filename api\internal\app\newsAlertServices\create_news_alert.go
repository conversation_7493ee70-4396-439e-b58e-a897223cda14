package newsAlertService

import (
	"dng-module/config"
	"dng-module/internal/model"
	"time"
)

// determineStatus calculates the current status of the news alert based on time
func determineStatus(startTime, endTime time.Time) string {
	now := time.Now().UTC()

	switch {
	case now.Before(startTime):
		return "scheduled"
	case now.After(endTime):
		return "inactive"
	default:
		return "active"
	}
}

// CreateNewsAlert creates a new news alert and sets the status automatically
func CreateNewsAlert(alert model.NewsAlert) (*model.NewsAlert, error) {
	// Automatically determine status based on time
	alert.Status = determineStatus(alert.StartDateTime, alert.EndDateTime)

	// Set timestamps
	now := time.Now().UTC()
	alert.CreatedAt = now
	alert.UpdatedAt = now

	// Save to database
	if err := config.DB.Create(&alert).Error; err != nil {
		return nil, err
	}
	return &alert, nil
}
