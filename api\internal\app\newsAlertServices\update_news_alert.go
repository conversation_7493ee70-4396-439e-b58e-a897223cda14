package newsAlertService

import (
	"dng-module/config"
	"dng-module/internal/model"
	"errors"
	"time"

	"gorm.io/gorm"
)

func UpdateNewsAlert(dto model.UpdateNewsAlertDTO) (*model.NewsAlert, error) {
	var alert model.NewsAlert

	// Find existing record
	if err := config.DB.First(&alert, dto.ID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("news alert not found")
		}
		return nil, err
	}

	// Check if any field has changed
	if alert.Type == dto.Type &&
		alert.Title == dto.Title &&
		alert.Description == dto.Description &&
		alert.StartDateTime.Equal(dto.StartDateTime) &&
		alert.EndDateTime.Equal(dto.EndDateTime) &&
		alert.IsVisible == dto.IsVisible {
		return nil, errors.New("no changes detected")
	}

	// Update fields
	alert.Type = dto.Type
	alert.Title = dto.Title
	alert.Description = dto.Description
	alert.StartDateTime = dto.StartDateTime
	alert.EndDateTime = dto.EndDateTime
	alert.IsVisible = dto.IsVisible

	// Recalculate status based on new time values
	alert.Status = determineStatus(alert.StartDateTime, alert.EndDateTime)

	alert.UpdatedAt = time.Now().UTC()

	// Save changes
	if err := config.DB.Save(&alert).Error; err != nil {
		return nil, err
	}

	return &alert, nil
}
