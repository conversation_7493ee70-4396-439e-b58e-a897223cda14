// __tests__/AppLayout.test.tsx
import React from 'react'
import { render, screen, fireEvent } from '@testing-library/react'
import '@testing-library/jest-dom'
import AppLayout from '@/components/common/layout/GenericLayout'

// Mock the Sidebar and Header components to simplify the test
jest.mock('@/components/common/layout/sidebar/Sidebar', () => (props: any) => (
    <div data-testid="sidebar">
        Sidebar - open: {props.isSidebarOpen ? 'true' : 'false'}
        <button onClick={props.onSidebarClose}>Close Sidebar</button>
    </div>
))

jest.mock('@/components/common/layout/header/header', () => (props: any) => (
    <header data-testid="header">
        Header - showLogo: {props.showLogo ? 'true' : 'false'}
        <button onClick={props.toggleMobileSidebar}>Toggle Sidebar</button>
    </header>
))

describe('AppLayout', () => {
    it('renders children correctly', () => {
        render(<AppLayout>{<div>Child content</div>}</AppLayout>)
        expect(screen.getByText('Child content')).toBeInTheDocument()
    })

    it('conditionally renders sidebar when showSidebar is true', () => {
        render(<AppLayout showSidebar>{<div>Content</div>}</AppLayout>)
        expect(screen.getByTestId('sidebar')).toBeInTheDocument()
    })

    it('does not render sidebar when showSidebar is false', () => {
        render(<AppLayout>{<div>Content</div>}</AppLayout>)
        expect(screen.queryByTestId('sidebar')).not.toBeInTheDocument()
    })

    it('conditionally renders header when showHeader is true', () => {
        render(<AppLayout showHeader>{<div>Content</div>}</AppLayout>)
        expect(screen.getByTestId('header')).toBeInTheDocument()
    })

    it('does not render header when showHeader is false', () => {
        render(<AppLayout showHeader={false}>{<div>Content</div>}</AppLayout>)
        expect(screen.queryByTestId('header')).not.toBeInTheDocument()
    })

    it('renders container when useContainer is true', () => {
        const { container } = render(
            <AppLayout useContainer>
                <div>Content</div>
            </AppLayout>
        )
        const containerDiv = container.querySelector('div[style]')
        expect(containerDiv).toBeInTheDocument()
    })

    it('toggles mobile sidebar state when header button clicked', () => {
        render(<AppLayout showSidebar showHeader>{<div>Content</div>}</AppLayout>)
        expect(screen.getByTestId('sidebar')).toHaveTextContent('open: true')
        fireEvent.click(screen.getByText('Close Sidebar'))
    })
})
