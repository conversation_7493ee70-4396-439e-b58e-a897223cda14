'use client'

import { MantineProvider } from '@mantine/core'
import { DatePickerInput, DateTimePicker } from '@mantine/dates'
import React from 'react'

// Only import minimal Mantine CSS here
import '@mantine/core/styles.layer.css'
import '@mantine/dates/styles.layer.css'

import DateRangeIcon from '@mui/icons-material/DateRange'

const DATE_FORMAT = 'MMM DD, YYYY'
const DATE_TIME_FORMAT = 'MMM DD, YYYY HH:mm A'

interface DatePickerProps {
    name?: string
    value: Date | null
    onChange: (val: Date | null) => void
    label?: string | React.ReactNode
    minDate?: Date
    maxDate?: Date
    sx?: any
    error?: boolean
}

interface DateRangePickerProps {
    value: [Date | null, Date | null]
    onChange: (val: [Date | null, Date | null]) => void
    label: string
    minDate?: Date
    maxDate?: Date
    sx?: any
    error?: boolean
}

// 🗓 Single Date Picker Component
export function SingleDatePicker({
    value,
    onChange,
    label,
    name,
    minDate,
    maxDate,
    sx,
    error = false,
}: Readonly<DatePickerProps>) {
    return (
        <MantineProvider
            withCssVariables={false}
            theme={{ components: { Popover: { defaultProps: { zIndex: 2000 } } } }}
        >
            <DatePickerInput
                type="default"
                name={name}
                value={value}
                valueFormat={DATE_FORMAT}
                onChange={onChange}
                label={label}
                placeholder={'Select Date'}
                clearable
                variant="default"
                minDate={minDate}
                maxDate={maxDate}
                styles={{ input: { minHeight: '36px' }, ...sx }} // Consistent height styling
                error={error}
            />
        </MantineProvider>
    )
}

// 📅 Date Range Picker Component
export function DateRangePicker({
    value,
    onChange,
    label,
    minDate,
    maxDate,
    sx,
    error = false,
}: Readonly<DateRangePickerProps>) {
    return (
        <MantineProvider
            withCssVariables={false}
            theme={{ components: { Popover: { defaultProps: { zIndex: 2000 } } } }}
        >
            <DatePickerInput
                type="range"
                value={value}
                onChange={onChange}
                valueFormat={DATE_FORMAT}
                // label={label}
                leftSection={<DateRangeIcon fontSize={'small'} />}
                placeholder={label ?? 'Select Date Range'}
                variant="default"
                allowSingleDateInRange
                minDate={minDate}
                maxDate={maxDate}
                styles={{ input: { minHeight: '36px' }, ...sx }} // Consistent height styling
                error={error}
            />
        </MantineProvider>
    )
}

// 🕒 DateTime Picker Component
export function SingleDateTimePicker({
    value,
    onChange,
    label,
    minDate,
    maxDate,
    sx,
    error = false,
}: Readonly<DatePickerProps>) {
    return (
        <MantineProvider
            withCssVariables={false}
            theme={{ components: { Popover: { defaultProps: { zIndex: 2000 } } } }}
        >
            <DateTimePicker
                value={value}
                valueFormat={DATE_TIME_FORMAT}
                onChange={onChange}
                label={label}
                placeholder={'Select Date & Time'}
                clearable
                minDate={minDate}
                maxDate={maxDate}
                styles={{ input: { minHeight: '36px' }, ...sx }} // Consistent height styling
                error={error}
            />
        </MantineProvider>
    )
}
