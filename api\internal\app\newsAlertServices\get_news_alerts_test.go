package newsAlertService

import (
	"dng-module/config"
	// newsAlertService "dng-module/internal/app/newsAlertServices"
	"dng-module/internal/model"
	"dng-module/testing/utilsTest"
	"errors"
	"regexp"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
)

func TestGetNewsAlerts(t *testing.T) {
	// Setup mock DB
	db, mock := utilsTest.SetupMockDB(t)
	config.DB = db
	defer utilsTest.CloseMockDB(db)

	// Freeze time for consistent testing (truncated to seconds)
	now := time.Date(2025, 5, 22, 12, 0, 0, 0, time.UTC).Truncate(time.Second)
	past := now.Add(-1 * time.Hour)
	future := now.Add(1 * time.Hour)

	tests := []struct {
		name        string
		params      GetNewsAlertsParams
		mockClosure func(mock sqlmock.Sqlmock, testTime time.Time)
		expected    []model.NewsAlert
		totalCount  int64
		expectError bool
	}{
		{
			name: "success - basic fetch with defaults",
			params: GetNewsAlertsParams{
				Limit: 15,
			},
			mockClosure: func(mock sqlmock.Sqlmock, testTime time.Time) {
				mock.ExpectQuery(regexp.QuoteMeta(`SELECT count(*) FROM "news_alerts"`)).
					WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(2))

				mock.ExpectQuery(regexp.QuoteMeta(`SELECT * FROM "news_alerts" ORDER BY start_date_time desc LIMIT $1`)).
					WithArgs(15).
					WillReturnRows(sqlmock.NewRows([]string{"id", "title", "start_date_time", "end_date_time", "status"}).
						AddRow(1, "Alert 1", past, future, "active").
						AddRow(2, "Alert 2", past.Add(-1*time.Hour), future.Add(1*time.Hour), "active"))
			},
			expected: []model.NewsAlert{
				{
					ID:            1,
					Title:         "Alert 1",
					StartDateTime: past,
					EndDateTime:   future,
					Status:        "active",
				},
				{
					ID:            2,
					Title:         "Alert 2",
					StartDateTime: past.Add(-1 * time.Hour),
					EndDateTime:   future.Add(1 * time.Hour),
					Status:        "active",
				},
			},
			totalCount:  2,
			expectError: false,
		},
		{
			name: "success - with search and status filters",
			params: GetNewsAlertsParams{
				Search: "alert",
				Status: []string{"active", "pending"},
				Limit:  10,
				Order:  "asc",
				SortBy: "title",
			},
			mockClosure: func(mock sqlmock.Sqlmock, testTime time.Time) {
				mock.ExpectQuery(regexp.QuoteMeta(
					`SELECT count(*) FROM "news_alerts" WHERE LOWER(title) LIKE $1 AND status IN ($2,$3)`)).
					WithArgs("%alert%", "active", "pending").
					WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(1))

				mock.ExpectQuery(regexp.QuoteMeta(
					`SELECT * FROM "news_alerts" WHERE LOWER(title) LIKE $1 AND status IN ($2,$3) ORDER BY title asc LIMIT $4`)).
					WithArgs("%alert%", "active", "pending", 10).
					WillReturnRows(sqlmock.NewRows([]string{"id", "title", "status"}).
						AddRow(1, "Alert X", "active"))

			},
			expected: []model.NewsAlert{
				{
					ID:     1,
					Title:  "Alert X",
					Status: "active",
				},
			},
			totalCount:  1,
			expectError: false,
		},
		{
			name: "success - no results",
			params: GetNewsAlertsParams{
				Limit: 10,
			},
			mockClosure: func(mock sqlmock.Sqlmock, testTime time.Time) {
				mock.ExpectQuery(regexp.QuoteMeta(`SELECT count(*) FROM "news_alerts"`)).
					WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(0))

				mock.ExpectQuery(regexp.QuoteMeta(`SELECT * FROM "news_alerts" ORDER BY start_date_time desc LIMIT $1`)).
					WithArgs(10).
					WillReturnRows(sqlmock.NewRows([]string{"id", "title", "start_date_time", "end_date_time", "status"}))
			},
			expected:    []model.NewsAlert{},
			totalCount:  0,
			expectError: false,
		},

		{
			name: "success - today only filter",
			params: GetNewsAlertsParams{
				TodayOnly: true,
				Limit:     15,
			},
			mockClosure: func(mock sqlmock.Sqlmock, testTime time.Time) {
				// Use sqlmock.AnyArg for time parameters to avoid exact matching
				mock.ExpectQuery(regexp.QuoteMeta(`SELECT count(*) FROM "news_alerts" WHERE start_date_time <= $1 AND end_date_time >= $2`)).
					WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg()).
					WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(1))

				mock.ExpectQuery(regexp.QuoteMeta(`SELECT * FROM "news_alerts" WHERE start_date_time <= $1 AND end_date_time >= $2 ORDER BY start_date_time desc LIMIT $3`)).
					WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), 15).
					WillReturnRows(sqlmock.NewRows([]string{"id", "title", "status"}).
						AddRow(1, "Today's Alert", "active"))
			},
			expected: []model.NewsAlert{
				{
					ID:     1,
					Title:  "Today's Alert",
					Status: "active",
				},
			},
			totalCount:  1,
			expectError: false,
		},
		{
			name: "error - database count failure",
			params: GetNewsAlertsParams{
				Limit: 15,
			},
			mockClosure: func(mock sqlmock.Sqlmock, testTime time.Time) {
				mock.ExpectQuery(regexp.QuoteMeta(`SELECT count(*) FROM "news_alerts"`)).
					WillReturnError(errors.New("count failed"))
			},
			expected:    nil,
			totalCount:  0,
			expectError: true,
		},
		{
			name: "error - database find failure",
			params: GetNewsAlertsParams{
				Limit: 15,
			},
			mockClosure: func(mock sqlmock.Sqlmock, testTime time.Time) {
				mock.ExpectQuery(regexp.QuoteMeta(`SELECT count(*) FROM "news_alerts"`)).
					WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(2))
				mock.ExpectQuery(regexp.QuoteMeta(`SELECT * FROM "news_alerts" ORDER BY start_date_time desc LIMIT $1`)).
					WithArgs(15).
					WillReturnError(errors.New("find failed"))
			},
			expected:    nil,
			totalCount:  2,
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Reset mock expectations for each test case
			mock.ExpectationsWereMet() // Clear any previous expectations
			tt.mockClosure(mock, now)

			alerts, totalCount, err := GetNewsAlerts(tt.params)

			if tt.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.totalCount, totalCount)

				// Compare only relevant fields
				for i := range alerts {
					assert.Equal(t, tt.expected[i].ID, alerts[i].ID)
					assert.Equal(t, tt.expected[i].Title, alerts[i].Title)
					assert.Equal(t, tt.expected[i].Status, alerts[i].Status)

					// Compare times if they exist in expected
					if !tt.expected[i].StartDateTime.IsZero() {
						assert.Equal(t, tt.expected[i].StartDateTime, alerts[i].StartDateTime)
					}
					if !tt.expected[i].EndDateTime.IsZero() {
						assert.Equal(t, tt.expected[i].EndDateTime, alerts[i].EndDateTime)
					}
				}
			}

			assert.NoError(t, mock.ExpectationsWereMet())
		})
	}
}

func TestGetVisibleNewsAlerts(t *testing.T) {
	// Setup mock DB
	db, mock := utilsTest.SetupMockDB(t)
	config.DB = db
	defer utilsTest.CloseMockDB(db)

	now := time.Now().UTC()
	past := now.Add(-1 * time.Hour)
	future := now.Add(1 * time.Hour)

	tests := []struct {
		name        string
		mockClosure func(mock sqlmock.Sqlmock)
		expected    []model.NewsAlert
		expectError bool
	}{
		{
			name: "success - visible alerts",
			mockClosure: func(mock sqlmock.Sqlmock) {
				mock.ExpectQuery(regexp.QuoteMeta(`SELECT * FROM "news_alerts" WHERE is_visible = $1`)).
					WithArgs(true).
					WillReturnRows(sqlmock.NewRows([]string{"id", "type", "title", "start_date_time", "end_date_time"}).
						AddRow(1, "alert", "Alert 1", past, future).
						AddRow(2, "event", "Event 1", past, future.Add(2*time.Hour)).
						AddRow(3, "news", "News 1", past, future.Add(3*time.Hour)))
			},
			expected: []model.NewsAlert{
				{
					ID:            1,
					Type:          "alert",
					Title:         "Alert 1",
					StartDateTime: past,
					EndDateTime:   future,
				},
				{
					ID:            2,
					Type:          "event",
					Title:         "Event 1",
					StartDateTime: past,
					EndDateTime:   future.Add(2 * time.Hour),
				},
				{
					ID:            3,
					Type:          "news",
					Title:         "News 1",
					StartDateTime: past,
					EndDateTime:   future.Add(3 * time.Hour),
				},
			},
			expectError: false,
		},
		{
			name: "success - no visible alerts in DB",
			mockClosure: func(mock sqlmock.Sqlmock) {
				mock.ExpectQuery(regexp.QuoteMeta(`SELECT * FROM "news_alerts" WHERE is_visible = $1`)).
					WithArgs(true).
					WillReturnRows(sqlmock.NewRows([]string{"id", "type", "title", "start_date_time", "end_date_time"}))
			},
			expected:    []model.NewsAlert{},
			expectError: false,
		},
		{
			name: "success - all alerts filtered out",
			mockClosure: func(mock sqlmock.Sqlmock) {
				past := time.Now().UTC().Add(-3 * time.Hour)
				past2 := time.Now().UTC().Add(-4 * time.Hour)
				mock.ExpectQuery(regexp.QuoteMeta(`SELECT * FROM "news_alerts" WHERE is_visible = $1`)).
					WithArgs(true).
					WillReturnRows(sqlmock.NewRows([]string{"id", "type", "title", "start_date_time", "end_date_time"}).
						AddRow(1, "alert", "Old Alert", past2, past). // expired alert
						AddRow(2, "event", "Old Event", past2, past). // expired event
						AddRow(3, "news", "Old News", past2, past))   // expired news
			},
			expected:    []model.NewsAlert{},
			expectError: false,
		},
		{
			name: "error - database failure",
			mockClosure: func(mock sqlmock.Sqlmock) {
				mock.ExpectQuery(regexp.QuoteMeta(`SELECT * FROM "news_alerts" WHERE is_visible = $1`)).
					WithArgs(true).
					WillReturnError(errors.New("database error"))
			},
			expected:    nil,
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.mockClosure(mock)

			alerts, err := GetVisibleNewsAlerts()

			if tt.expectError {
				assert.Error(t, err)
				assert.Nil(t, alerts)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, len(tt.expected), len(alerts))
				for i := range alerts {
					assert.Equal(t, tt.expected[i].ID, alerts[i].ID)
					assert.Equal(t, tt.expected[i].Type, alerts[i].Type)
					assert.Equal(t, tt.expected[i].Title, alerts[i].Title)
				}
			}

			assert.NoError(t, mock.ExpectationsWereMet())
		})
	}
}
