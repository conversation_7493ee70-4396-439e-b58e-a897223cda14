package newsAlertService

import (
	"dng-module/config"
	// newsAlertService "dng-module/internal/app/newsAlertServices"
	"dng-module/testing/utilsTest"

	// utilsTest "dng-module/testutils"
	"errors"
	"fmt"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"
)

func setupMockDB(t *testing.T) (*gorm.DB, sqlmock.Sqlmock) {
	db, mock := utilsTest.SetupMockDB(t)
	config.DB = db
	return db, mock
}

func TestDifferenceHelper(t *testing.T) {
	input := []uint{1, 2, 3, 4}
	existing := []uint{2, 4}
	expectedMissing := []uint{1, 3}
	got := Difference(input, existing)
	assert.ElementsMatch(t, got, expectedMissing)

	// Test when nothing is missing
	got2 := Difference([]uint{1, 2}, []uint{1, 2})
	assert.Empty(t, got2)

	// Test when all are missing
	got3 := Difference([]uint{1, 2}, []uint{})
	assert.ElementsMatch(t, got3, []uint{1, 2})
}

func TestDeleteNewsAlerts_Success(t *testing.T) {
	_, mock := setupMockDB(t)
	ids := []uint{1, 2}

	// Mock Pluck for validation
	mock.ExpectQuery(`SELECT .* FROM "news_alerts" WHERE id IN \(\$[0-9]+(,\$[0-9]+)*\)`).
		WithArgs(1, 2).
		WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1).AddRow(2))

	// Mock Delete
	mock.ExpectBegin()
	mock.ExpectExec(`DELETE FROM "news_alerts" WHERE "news_alerts"\."id" IN \(\$[0-9]+(,\$[0-9]+)*\)`).
		WithArgs(1, 2).
		WillReturnResult(sqlmock.NewResult(0, 2))
	mock.ExpectCommit()

	err := DeleteNewsAlerts(ids)
	assert.NoError(t, err)
}

func TestDeleteNewsAlerts_SingleIDSuccess(t *testing.T) {
	_, mock := setupMockDB(t)
	ids := []uint{1}

	mock.ExpectQuery(`SELECT .* FROM "news_alerts" WHERE id IN \(\$1\)`).
		WithArgs(1).
		WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))

	mock.ExpectBegin()
	mock.ExpectExec(`DELETE FROM "news_alerts" WHERE "news_alerts"\."id"( = \$1| IN \(\$1\))`).
		WithArgs(1).
		WillReturnResult(sqlmock.NewResult(0, 1))
	mock.ExpectCommit()

	err := DeleteNewsAlerts(ids)
	assert.NoError(t, err)
}

func TestDeleteNewsAlerts_EmptyIDs(t *testing.T) {
	err := DeleteNewsAlerts([]uint{})
	assert.EqualError(t, err, "no IDs provided")
}

func TestDeleteNewsAlerts_ValidationFails(t *testing.T) {
	_, mock := setupMockDB(t)
	ids := []uint{1, 2}

	mock.ExpectQuery(`SELECT .* FROM "news_alerts" WHERE id IN \(\$[0-9]+(,\$[0-9]+)*\)`).
		WithArgs(1, 2).
		WillReturnError(errors.New("db error"))

	err := DeleteNewsAlerts(ids)
	assert.Contains(t, err.Error(), "failed to validate existing alerts")
}

func TestDeleteNewsAlerts_SomeIDsNotFound(t *testing.T) {
	_, mock := setupMockDB(t)
	ids := []uint{1, 2}

	mock.ExpectQuery(`SELECT .* FROM "news_alerts" WHERE id IN \(\$[0-9]+(,\$[0-9]+)*\)`).
		WithArgs(1, 2).
		WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1)) // only 1 exists

	err := DeleteNewsAlerts(ids)
	assert.Contains(t, err.Error(), "some news alerts not found: [2]")
}

func TestDeleteNewsAlerts_DeleteFails(t *testing.T) {
	_, mock := setupMockDB(t)
	ids := []uint{1}

	mock.ExpectQuery(`SELECT .* FROM "news_alerts" WHERE id IN \(\$[0-9]+(,\$[0-9]+)*\)`).
		WithArgs(1).
		WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))

	mock.ExpectBegin()
	mock.ExpectExec(`DELETE FROM "news_alerts" WHERE "news_alerts"\."id" IN \(\$[0-9]+(,\$[0-9]+)*\)`).
		WithArgs(1).
		WillReturnError(fmt.Errorf("delete error"))
	mock.ExpectRollback()

	err := DeleteNewsAlerts(ids)
	assert.Contains(t, err.Error(), "failed to delete news alerts")
}
