package catalogReportService

import (
	"dng-module/internal/model"
	"dng-module/internal/utils"

	"gorm.io/gorm"
)

func GetFilterOptions(db *gorm.DB, userEmail string) (map[string]interface{}, error) {
	// Step 1: Get user
	var user model.User
	if err := db.Where("email = ?", userEmail).First(&user).Error; err != nil {
		utils.Logger.Error("Error fetching user by email: ", err)
		return nil, err
	}

	utils.Logger.Infof("User ID: %d", user.ID)

	// Step 2: Get user's domain access and roles
	type UserAccessInfo struct {
		DomainID     uint
		RoleID       uint
		IsSuperAdmin bool
		CanView      bool
	}
	var accessInfo []UserAccessInfo
	if err := db.Table("user_domain_access as uda").
		Select("uda.domain_id, r.id as role_id, r.is_super_admin, r.can_view").
		Joins("join roles r on uda.role_id = r.id").
		Where("uda.user_id = ?", user.ID).
		Scan(&accessInfo).Error; err != nil {
		utils.Logger.Error("Error fetching user domain access: ", err)
		return nil, err
	}

	if len(accessInfo) == 0 {
		utils.Logger.Warn("No domain access found for user")
		return map[string]interface{}{
			"domains":       []model.Domain{},
			"source_system": []string{},
			"category":      []string{},
		}, nil
	}

	// Step 3: Check if user is Super Admin
	for _, access := range accessInfo {
		if access.IsSuperAdmin {
			utils.Logger.Info("Super Admin access - returning all filter options")

			var allDomains []model.Domain
			var sourceSystems []string
			var categories []string

			if err := db.Find(&allDomains).Error; err != nil {
				return nil, err
			}
			if err := db.Model(&model.Report{}).Distinct().Pluck("source_system", &sourceSystems).Error; err != nil {
				return nil, err
			}
			if err := db.Model(&model.Report{}).Distinct().Pluck("category", &categories).Error; err != nil {
				return nil, err
			}

			return map[string]interface{}{
				"domains":       allDomains,
				"source_system": sourceSystems,
				"category":      categories,
			}, nil
		}
	}

	// Step 4: Filter accessible domains (based on can_view = true)
	var accessibleDomainIDs []uint
	for _, access := range accessInfo {
		if access.CanView {
			accessibleDomainIDs = append(accessibleDomainIDs, access.DomainID)
		}
	}

	if len(accessibleDomainIDs) == 0 {
		utils.Logger.Warn("User does not have can_view access to any domains")
		return map[string]interface{}{
			"domains":       []model.Domain{},
			"source_system": []string{},
			"category":      []string{},
		}, nil
	}

	// Step 5: Fetch domain details
	var domains []model.Domain
	if err := db.Where("id IN ?", accessibleDomainIDs).Find(&domains).Error; err != nil {
		utils.Logger.Error("Error fetching domain details: ", err)
		return nil, err
	}

	// Step 6: Fetch source systems and categories for accessible domains
	var sourceSystems []string
	if err := db.Model(&model.Report{}).
		Where("domain_id IN ?", accessibleDomainIDs).
		Distinct().Pluck("source_system", &sourceSystems).Error; err != nil {
		utils.Logger.Error("Error fetching source systems: ", err)
		return nil, err
	}

	var categories []string
	if err := db.Model(&model.Report{}).
		Where("domain_id IN ?", accessibleDomainIDs).
		Distinct().Pluck("category", &categories).Error; err != nil {
		utils.Logger.Error("Error fetching categories: ", err)
		return nil, err
	}

	// Step 7: Return filtered result
	return map[string]interface{}{
		"domains":       domains,
		"source_system": sourceSystems,
		"category":      categories,
	}, nil
}
