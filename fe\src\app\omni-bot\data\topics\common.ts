import { TopicContext } from '../../types'

// Import topic context data from JSON
import topicContextData from './topicContextData.json'

// Convert JSON to TopicContext type
export const TOPIC_CONTEXT: TopicContext = topicContextData as TopicContext

// Topic Keywords for Detection
export const TOPIC_KEYWORDS = {
    networkData: [
        'distribution center',
        'dc',
        'performance',
        'inventory',
        'optimization',
        'audit accuracy',
        'inventory accuracy',
        'in-stock',
        'capacity utilization',
        'financial impact',
    ],
    dcSpecificTrends: [
        'dc',
        'distribution center',
        'janesville',
        'memphis',
        'oklahoma',
        'zone',
        'audit',
        'accuracy',
        'cartons',
        'labor hour',
        'error rate',
        'trailer',
        'process',
    ],
    employeePerformance: [
        'employee',
        'shift',
        'performance',
        'audit accuracy',
        'audit completion',
        'trailer accuracy',
        'picking errors',
        'error rates',
        'morning shift',
        'evening shift',
        'night shift',
    ],
    orderToteErrors: [
        'order',
        'tote',
        'under-pick',
        'over-pick',
        'error details',
        'audit result',
        'invalid picks',
        'quantity discrepancies',
        'error rate',
        'wrong item',
        'missing item',
    ],
}

// Generic Responses
export const GENERIC_RESPONSES = {
    networkData:
        'I can provide information about our distribution center performance and inventory optimization. Would you like to know about network-wide audit accuracy, distribution centers with low inventory accuracy, in-stock percentages, the correlation between capacity utilization and accuracy, or the financial impact of improving audit accuracy?',
    dcSpecificTrends:
        'I can provide detailed information about distribution center performance metrics. Would you like to know about audit accuracy trends, labor productivity comparisons, error rates by audit type, zone performance breakdowns, audit frequency compliance, or process-specific issues?',
    employeePerformance:
        'I can provide detailed information about employee and shift performance metrics. Would you like to know about individual audit accuracy, audit completion rates, trailer accuracy trends, picking error analysis, time-of-day error patterns, or shift/zone performance breakdowns?',
    orderToteErrors:
        'I can provide detailed information about order and tote-level errors. Would you like to know about under-pick errors by zone, audit results for specific orders, employee-specific error metrics, tote error details, or items with significant quantity discrepancies?',
    chartAvailable: (title: string) =>
        `Here's a comprehensive chart showing multiple metrics for ${title}. This visualization displays audit accuracy (bars), capacity utilization (blue line), and in-stock percentage (dashed line) across all distribution centers. The green horizontal line indicates our target audit accuracy of 97%.`,
    chartUnavailable:
        "I'd be happy to show you a chart, but I don't have detailed information for this topic yet. You can ask about audit accuracy, capacity utilization, or in-stock percentages across our distribution centers.",
    chartRequest:
        "I'd be happy to show you a comprehensive chart of our distribution center performance. This visualization combines audit accuracy, capacity utilization, and in-stock percentages in a single view, allowing you to see correlations between these metrics.",
    fallback: (query: string) =>
        `I understand you're asking about ${query}... Let me gather some information on that topic for you. What specific aspects are you most interested in learning about?`,
    missingContext: "I don't have detailed information about this topic yet. How can I assist you with something else?",
}

// Chat History Data
export const CHATS = [
    { id: 't1', title: 'Network Accuracy Report' },
    { id: 't2', title: 'Memphis DC Discrepancy...' },
    { id: 't3', title: 'Q3 Inventory Shrinkage' },
    { id: 't4', title: 'Employee Training ROI' },
    { id: 't5', title: 'Trailer Loading Audit' },
]
