FROM harbor.dolgen.net/devops/kit/go:latest AS build
WORKDIR /build

# Arguments Setup
ARG VER
ARG CI_PROJECT_NAME
ARG APP_NAME
ARG PORT_MAPPING
ARG GITLAB_USER_LOGIN
ARG CI_COMMIT_SHORT_SHA


RUN echo ${CI_PROJECT_NAME}

# copy 
COPY . .

# Go Build settings
RUN go env -w GOARCH=amd64
RUN go env -w GOOS=linux
RUN go env -w CGO_ENABLED=0

RUN go mod tidy
RUN go build -ldflags "-X 'main.version=${VER}' -X 'main.date=$(date)' -X 'main.commit=${CI_COMMIT_SHORT_SHA}' -X 'main.builtBy=${GITLAB_USER_LOGIN}'" -o /app/ .

WORKDIR /app
COPY ./config ./config

# final stage/image
FROM harbor.dolgen.net/devops/kit/go/runtime:latest

# Arguments Setup
ARG VER
ARG CI_PROJECT_NAME
ARG APP_NAME
ARG PORT_MAPPING
ARG GITLAB_USER_LOGIN
ARG CI_COMMIT_SHORT_SHA

# Labels Setup
LABEL MAINTAINER=${CI_PROJECT_NAME}
LABEL dg.app=${CI_PROJECT_NAME}
LABEL dg.version=${VER}
LABEL dg.builtby=${GITLAB_USER_LOGIN}
LABEL dg.commit=${CI_COMMIT_SHORT_SHA}
LABEL dg.date=$(date)

# Update the OS packages
RUN apk update \
    && apk upgrade 
    
WORKDIR /app
COPY --from=build /app ./

RUN chmod g+s /app \
  && chmod g+rx /app/* \
  && chown -R container-user:container-user /app

USER container-user
RUN whoami


ENV PATH=/app:$PATH

EXPOSE ${PORT_MAPPING}
ENV PORT_MAPPING=${PORT_MAPPING}
ENV APP_NAME=${APP_NAME}

ENTRYPOINT ${APP_NAME}
