package newsAlertService

import (
	"dng-module/config"
	"dng-module/internal/model"
	"errors"
	"fmt"
)

func DeleteNewsAlerts(ids []uint) error {
	if len(ids) == 0 {
		return errors.New("no IDs provided")
	}

	// Check if all IDs exist before deletion (optional but useful for validation)
	var existingIDs []uint
	if err := config.DB.Model(&model.NewsAlert{}).
		Where("id IN ?", ids).
		Pluck("id", &existingIDs).Error; err != nil {
		return fmt.Errorf("failed to validate existing alerts: %w", err)
	}

	if len(existingIDs) != len(ids) {
		missing := Difference(ids, existingIDs)
		return fmt.Errorf("some news alerts not found: %v", missing)
	}

	// Proceed to delete
	if err := config.DB.Delete(&model.NewsAlert{}, ids).Error; err != nil {
		return fmt.Errorf("failed to delete news alerts: %w", err)
	}

	return nil
}

// Helper to find IDs that don't exist in DB
func Difference(input, existing []uint) []uint {
	existingMap := make(map[uint]bool)
	for _, id := range existing {
		existingMap[id] = true
	}
	var missing []uint
	for _, id := range input {
		if !existingMap[id] {
			missing = append(missing, id)
		}
	}
	return missing
}
