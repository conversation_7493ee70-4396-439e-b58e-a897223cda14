package userService

import (
	"dng-module/internal/model"
	"dng-module/internal/utils"
	"errors"
	"fmt"
	"strings"

	"gorm.io/gorm"
)

type UserRoleResponse struct {
	UserID          int          `json:"user_id"`
	Email           string       `json:"email"`
	Roles           []DomainRole `json:"roles"`
	PrimaryRoleName string       `json:"role_name"`
}

type DomainRole struct {
	DomainID   int    `json:"domain_id"`
	DomainName string `json:"domain_name"`
	RoleID     int    `json:"role_id"`
	RoleName   string `json:"role_name"`
}

// func UserUpsert(db *gorm.DB, UserEmail string, AdGroups []string) (UserRoleResponse, error){
	func UserUpsert(db *gorm.DB, UserEmail string, AdGroups []string, FirstName string, LastName string) (UserRoleResponse, error) {
	utils.Logger.Info(fmt.Sprintf("Starting access setup for user: %s with %d AD groups", UserEmail, len(AdGroups)))

	tx := db.Begin()
	if tx.Error != nil {
		utils.Logger.Error(fmt.Sprintf("Database transaction failed: %v", tx.Error))
		return UserRoleResponse{}, tx.Error
	}
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			utils.Logger.Error(fmt.Sprintf("Critical error during user setup for %s: %v", UserEmail, r))
		}
	}()

	var user model.User
	// err := tx.Where("email = ?", UserEmail).FirstOrCreate(&user, model.User{Email: UserEmail}).Error
		err := db.Where("email = ?", UserEmail).FirstOrCreate(&user, model.User{
		Email:     UserEmail,
		FirstName: FirstName,
		LastName:  LastName,
	}).Error

	if err != nil {
		tx.Rollback()
		utils.Logger.Error(fmt.Sprintf("Failed to create/find user account for %s: %v", UserEmail, err))
		return UserRoleResponse{}, err
	}

	var patterns []model.ADGroupPattern
	err = tx.Preload("Role").Find(&patterns).Error
	if err != nil {
		tx.Rollback()
		utils.Logger.Error(fmt.Sprintf("Failed to retrieve role definitions: %v", err))
		return UserRoleResponse{}, err
	}

	matchingPatterns := []model.ADGroupPattern{}
	for _, pattern := range patterns {
		for _, group := range AdGroups {
			if strings.EqualFold(group, pattern.Pattern) {
				matchingPatterns = append(matchingPatterns, pattern)
				break
			}
		}
	}
	if len(matchingPatterns) == 0 {
		tx.Rollback()
		utils.Logger.Warn(fmt.Sprintf("User %s has no matching AD groups for system roles", UserEmail))
		return UserRoleResponse{}, errors.New("no role/domain mappings found for user AD groups")
	}
	utils.Logger.Info(fmt.Sprintf("Found %d matching role assignments for user %s", len(matchingPatterns), UserEmail))

	var allDomains []model.Domain
	err = tx.Find(&allDomains).Error
	if err != nil {
		tx.Rollback()
		utils.Logger.Error(fmt.Sprintf("Failed to retrieve domain information: %v", err))
		return UserRoleResponse{}, err
	}

	err = tx.Where("user_id = ?", user.ID).Delete(&model.UserDomainAccess{}).Error
	if err != nil {
		tx.Rollback()
		utils.Logger.Error(fmt.Sprintf("Failed to reset previous access settings for user %s: %v", UserEmail, err))
		return UserRoleResponse{}, err
	}

	type domainKey struct {
		DomainID int
	}
	rolePriority := func(role model.Role) int {
		if role.IsSuperAdmin {
			return 0
		}
		if role.CanEdit {
			return 1
		}
		return 2
	}

	// Build a map of roleID to full role struct
	roleMap := make(map[int]model.Role)
	for _, pattern := range patterns {
		roleMap[pattern.RoleID] = pattern.Role
	}

	domainAccessMap := map[domainKey]model.UserDomainAccess{}

	for _, pattern := range matchingPatterns {
		if pattern.Role.IsSuperAdmin {
			utils.Logger.Info(fmt.Sprintf("User %s granted Super Admin permissions", UserEmail))
			for _, domain := range allDomains {
				key := domainKey{DomainID: domain.ID}
				existing, exists := domainAccessMap[key]
				existingRole := roleMap[existing.RoleID]
				if !exists || rolePriority(pattern.Role) < rolePriority(existingRole) {
					domainAccessMap[key] = model.UserDomainAccess{
						UserID:   user.ID,
						DomainID: domain.ID,
						RoleID:   pattern.RoleID,
					}
				}
			}
		} else {
			for _, domain := range allDomains {
				if pattern.DomainPattern == "" || strings.Contains(strings.ToLower(domain.Name), strings.ToLower(pattern.DomainPattern)) {
					key := domainKey{DomainID: domain.ID}
					existing, exists := domainAccessMap[key]
					existingRole := roleMap[existing.RoleID]
					if !exists || rolePriority(pattern.Role) < rolePriority(existingRole) {
						domainAccessMap[key] = model.UserDomainAccess{
							UserID:   user.ID,
							DomainID: domain.ID,
							RoleID:   pattern.RoleID,
						}
					}
				}
			}
		}
	}

	if len(domainAccessMap) == 0 {
		tx.Rollback()
		utils.Logger.Warn(fmt.Sprintf("User %s has no matching domains for their AD groups", UserEmail))
		return UserRoleResponse{}, errors.New("no matching domains found for AD group domain patterns")
	}

	finalAccesses := make([]model.UserDomainAccess, 0, len(domainAccessMap))
	for _, access := range domainAccessMap {
		finalAccesses = append(finalAccesses, access)
	}

	err = tx.Create(&finalAccesses).Error
	if err != nil {
		tx.Rollback()
		utils.Logger.Error(fmt.Sprintf("Failed to save access permissions for user %s: %v", UserEmail, err))
		return UserRoleResponse{}, err
	}

	if err := tx.Commit().Error; err != nil {
		utils.Logger.Error(fmt.Sprintf("Failed to commit transaction for user %s: %v", UserEmail, err))
		return UserRoleResponse{}, err
	}

	var result []struct {
		DomainID     int
		DomainName   string
		RoleID       int
		RoleName     string
		CanView      bool
		CanEdit      bool
		IsSuperAdmin bool
	}
	err = db.Table("user_domain_access AS uda").
		Select("uda.domain_id, d.name AS domain_name, r.id AS role_id, r.role_name AS role_name, r.can_view, r.can_edit, r.is_super_admin").
		Joins("JOIN domains d ON d.id = uda.domain_id").
		Joins("JOIN roles r ON r.id = uda.role_id").
		Where("uda.user_id = ?", user.ID).
		Scan(&result).Error

	if err != nil {
		utils.Logger.Error(fmt.Sprintf("Failed to retrieve final access information: %v", err))
		return UserRoleResponse{}, err
	}

	var roles []DomainRole
	var primaryRoleName string
	var selectedRole model.Role
	hasRole := false

	for _, row := range result {
		roles = append(roles, DomainRole{
			DomainID:   row.DomainID,
			DomainName: row.DomainName,
			RoleID:     row.RoleID,
			RoleName:   row.RoleName,
		})

		// Select the most privileged role as the primary role name
		if !hasRole ||
			(row.IsSuperAdmin && !selectedRole.IsSuperAdmin) ||
			(row.CanEdit && !selectedRole.IsSuperAdmin && !selectedRole.CanEdit) {
			selectedRole = model.Role{
				RoleName:     row.RoleName,
				CanEdit:      row.CanEdit,
				IsSuperAdmin: row.IsSuperAdmin,
			}
			hasRole = true
		}
	}

	if hasRole {
		primaryRoleName = selectedRole.RoleName
	}

	response := UserRoleResponse{
		UserID:          user.ID,
		Email:           user.Email,
		Roles:           roles,
		PrimaryRoleName: primaryRoleName,
	}

	utils.Logger.Info(fmt.Sprintf("Completed access setup for user %s: %d domains, primary role: %s",
		UserEmail, len(roles), primaryRoleName))

	return response, nil
}
