'use client'

import React from 'react'
import { Box, AppBar, Toolbar } from '@mui/material'
import { Logo } from '@/components/common/layout/shared/logo/logo'
import Profile from '@/components/common/layout/header/profile'

interface HeaderWithErrorImageProps {
  imagePath?: string
  showHeader?: boolean
  showFooterSpace?: boolean
}

/**
 * A component that displays the header and a full-page error image
 * This creates a clean error experience while maintaining some context
 */
const HeaderWithErrorImage: React.FC<HeaderWithErrorImageProps> = ({
  imagePath = '/images/500_Err.png',
  showHeader = true,
  showFooterSpace = true
}) => {
  // Add a style to the body to prevent scrolling
  React.useEffect(() => {
    // Save the original styles
    const originalOverflow = document.body.style.overflow;
    const originalHeight = document.body.style.height;

    // Apply new styles to prevent scrolling
    document.body.style.overflow = 'hidden';
    document.body.style.height = '100%';

    // Cleanup function to restore original styles
    return () => {
      document.body.style.overflow = originalOverflow;
      document.body.style.height = originalHeight;
    };
  }, []);

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        minHeight: '100vh',
        width: '100%',
        margin: 0,
        padding: 0,
        overflow: 'hidden',
        backgroundColor: '#fff'
      }}
    >
      {/* Header */}
      {showHeader && (
        <AppBar position="sticky" color="default" sx={{ boxShadow: 'none', backdropFilter: 'blur(4px)' }}>
          <Toolbar sx={{ width: '100%', color: 'text.secondary' }}>
            <Logo />
            <Box flexGrow={1} />
            <Profile />
          </Toolbar>
        </AppBar>
      )}

      {/* Error Image Container */}
      <Box
        sx={{
          flex: 1,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          overflow: 'hidden',
          padding: 0
        }}
      >
        {/* Error Image */}
        <Box
          component="img"
          src={imagePath}
          alt="Error"
          sx={{
            maxWidth: '100%',
            maxHeight: '100%',
            width: 'auto',
            height: 'auto',
            objectFit: 'contain',
            padding: 0,
            display: 'block'
          }}
          onError={(e) => {
            console.error('Error loading error image');
            // Fallback to a simple colored box if image fails to load
            const target = e.target as HTMLImageElement;
            target.style.display = 'none';
          }}
        />

        {/* Footer Space */}
        {showFooterSpace && (
          <Box sx={{ height: '80px', width: '100%' }} />
        )}
      </Box>
    </Box>
  )
}

export default HeaderWithErrorImage
