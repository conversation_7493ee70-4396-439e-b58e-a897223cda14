import dynamic from 'next/dynamic'
import React from 'react'
import { Box, Typography } from '@mui/material'
import { SOURCE_OPTIONS } from '@/utils/constants'
import Image from 'next/image'
import { Report } from '../types'
import LookerDashboard from '@/app/reports/components/LookerDashboard'
import * as Yup from 'yup'

const PowerBIReport = dynamic(() => import('@/app/reports/components/PowerBIReport'), {
    loading: () => <p>Initiating PBI Component...</p>,
    ssr: false,
})

export const ReportIcon = ({ sourceSystem }: { sourceSystem: string }) => {
    const iconMap: { [key: string]: { src: string; alt: string } } = {
        [SOURCE_OPTIONS.PowerBI.toLowerCase()]: { src: '/images/logos/pbi-logo.svg', alt: SOURCE_OPTIONS.PowerBI },
        [SOURCE_OPTIONS.Looker.toLowerCase()]: { src: '/images/logos/looker-logo.svg', alt: SOURCE_OPTIONS.Looker },
    }

    const icon = iconMap[sourceSystem.toLowerCase()]
    if (!icon) return null

    return (
        <Box display="inline-flex" justifyContent={'center'} width="40px">
            <Image src={icon.src} alt={icon.alt} width={20} height={20} style={{ width: 'auto', marginRight: '6px' }} />
        </Box>
    )
}

// Detailed Report Component for Expanded View
export const ReportDetails: React.FC<{ report: Report }> = ({ report }) => {
    return (
        <Box sx={{ margin: 1 }}>
            {report.source_system.toLowerCase() === SOURCE_OPTIONS.PowerBI.toLowerCase() ? (
                <PowerBIReport reportId={report.report_id} groupId={report.group_id || ''} />
            ) : (
                <LookerDashboard dashboardId={report.report_id} />
            )}
        </Box>
    )
}

export const ReportSchema = Yup.object().shape({
    source_system: Yup.string().required('Source System is required.'),
    name: Yup.string().required('Name is required.'),
    domain_id: Yup.string().required('Domain is required.'),
    category: Yup.string().required('Category is required.'),
    //description: Yup.string().required('Description is required.'),
    report_id: Yup.string().required('Report ID is required.'),
    group_id: Yup.string().test('group-id-required-for-powerbi', 'Group ID is required for PowerBI.', function (value) {
        const { source_system } = this.parent
        if (source_system === SOURCE_OPTIONS.PowerBI) {
            return !!value?.trim()
        }
        return true
    }),
})

export const ErrorBox = () => {
    return (
        <Box>
            <Typography variant="subtitle2" color="#FFF">
                Provide a direct Power BI report link.
            </Typography>
            <Typography variant="body2" color="#FFF">
                1. Open the shared link in your browser.
            </Typography>
            <Typography variant="body2" color="#FFF">
                2. Copy the address bar URL.
            </Typography>
            <Typography variant="body2" color="#FFF">
                3. Paste that URL here.
            </Typography>
        </Box>
    )
}
