package catalogReportService

import (
	"dng-module/internal/infra/db"
	"dng-module/internal/model"
	"dng-module/internal/utils"
	"errors"
	"fmt"
	"time"

	"gorm.io/gorm"
)

func CreateReport(reportDto model.CreateReportDto, userEmail string) (*model.ReportResponse, error) {
	utils.Logger.Info(fmt.Sprintf("User %s attempting to create/update report '%s'", userEmail, reportDto.Name))

	var report model.Report
	var domain model.Domain
	var user model.User

	// Fetch user
	if err := db.DB.Where("email = ?", userEmail).First(&user).Error; err != nil {
		utils.Logger.Error(fmt.Sprintf("Security check failed: User %s not found in system", userEmail), err)
		return nil, errors.New("unauthorized: user not found")
	}

	// Get domain access for the user (with role)
	type Access struct {
		DomainID     uint
		CanEdit      bool
		IsSuperAdmin bool
	}
	var accessList []Access
	err := db.DB.Table("user_domain_access AS uda").
		Select("uda.domain_id, r.can_edit, r.is_super_admin").
		Joins("JOIN roles r ON uda.role_id = r.id").
		Where("uda.user_id = ?", user.ID).
		Scan(&accessList).Error
	if err != nil {
		utils.Logger.Error(fmt.Sprintf("Failed to check permissions for user %s", userEmail), err)
		return nil, err
	}

	// Check authorization
	isAuthorized := false
	isSuperAdmin := false
	for _, access := range accessList {
		if access.IsSuperAdmin {
			isSuperAdmin = true
			break
		}
		if access.DomainID == uint(reportDto.DomainID) && access.CanEdit {
			isAuthorized = true
		}
	}

	if !isSuperAdmin && !isAuthorized {
		utils.Logger.Warn(fmt.Sprintf("Access denied: User %s does not have permission for domain ID %d", userEmail, reportDto.DomainID))
		return nil, errors.New("unauthorized access to domain")
	}

	// Check domain existence
	if err := db.DB.Select("name").Where("id = ?", reportDto.DomainID).First(&domain).Error; err != nil {
		utils.Logger.Error(fmt.Sprintf("Domain with ID %d does not exist", reportDto.DomainID), err)
		return nil, errors.New("invalid domain ID")
	}

	// Update or create
	isUpdate := reportDto.ID != 0
	if isUpdate {
		if err := db.DB.First(&report, reportDto.ID).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				utils.Logger.Error(fmt.Sprintf("Report with ID %d was not found", reportDto.ID), err)
				return nil, errors.New("report not found")
			}
			return nil, err
		}
	} else {
		// Enhanced duplicate check that handles NULL report_id properly
		if reportDto.ReportID == "" {
			// Check for existing report with NULL report_id in this domain
			var count int64
			if err := db.DB.Model(&model.Report{}).
				Where("report_id IS NULL AND domain_id = ?", reportDto.DomainID).
				Count(&count).Error; err != nil {
				return nil, err
			}
			if count > 0 {
				utils.Logger.Warn("Report with empty report ID already exists in this domain")
				return nil, errors.New("a report with empty report ID already exists in this domain")
			}
		} else {
			// Check for existing report with this report_id
			if err := db.DB.Where("report_id = ?", reportDto.ReportID).First(&report).Error; err == nil {
				utils.Logger.Warn(fmt.Sprintf("Report with ID '%s' already exists", reportDto.ReportID))
				return nil, errors.New("report with this ReportID already exists")
			}
		}
		if reportDto.ReportUrl != "" {
			var existingReport model.Report
			if err := db.DB.Where("report_url = ?", reportDto.ReportUrl).First(&existingReport).Error; err == nil {
				utils.Logger.Warn(fmt.Sprintf("Report with URL '%s' already exists", reportDto.ReportUrl))
				return nil, errors.New("a report with this ReportUrl already exists")
			} else if !errors.Is(err, gorm.ErrRecordNotFound) {
				return nil, err
			}
		}
	}

	// Assign fields
	report.Name = reportDto.Name
	report.ReportID = reportDto.ReportID
	report.GroupID = reportDto.GroupID
	report.Description = reportDto.Description
	report.Category = reportDto.Category
	report.DomainID = reportDto.DomainID
	report.SourceSystem = reportDto.SourceSystem
	report.PublishedOn = reportDto.PublishedOn
	report.UpdatedAt = time.Now().UTC()
	report.ReportUrl = reportDto.ReportUrl

	if !isUpdate {
		report.CreatedBy = reportDto.CreatedBy
		report.CreatedAt = report.UpdatedAt
		if err := db.DB.Create(&report).Error; err != nil {
			utils.Logger.Error(fmt.Sprintf("Failed to create new report '%s'", reportDto.Name), err)
			return nil, err
		}
		utils.Logger.Info(fmt.Sprintf("Successfully created report '%s' in domain '%s'", report.Name, domain.Name))
	} else {
		if err := db.DB.Save(&report).Error; err != nil {
			utils.Logger.Error(fmt.Sprintf("Failed to update report '%s' (ID: %d)", reportDto.Name, reportDto.ID), err)
			return nil, err
		}
		utils.Logger.Info(fmt.Sprintf("Successfully updated report '%s' in domain '%s'", report.Name, domain.Name))
	}

	return &model.ReportResponse{
		ID:           report.ID,
		Name:         report.Name,
		ReportID:     report.ReportID,
		GroupID:      &report.GroupID,
		Description:  report.Description,
		Category:     report.Category,
		DomainID:     report.DomainID,
		DomainName:   domain.Name,
		SourceSystem: report.SourceSystem,
		ReportUrl:    report.ReportUrl,
		CreatedBy:    report.CreatedBy,
		CreatedAt:    report.CreatedAt,
		UpdatedAt:    report.UpdatedAt,
	}, nil
}
