package app

import (
	// deliveryHTTP "my-app/internal/delivery/http"

	"github.com/gin-gonic/gin"
	sloggin "github.com/samber/slog-gin"
)

func (app *App) startService() error {
	// emailSMTP := emailSMTP.NewSMTP(app.cfg)
	// emailUC := emailUseCase.NewUseCase(emailSMTP, app.cfg)
	// emailCTRL := deliveryHTTP.NewHandlers(emailUC, app.log)

	// Fetch secrets
	app.fetchSecrets()

	// Use Sloggin and suppress healthz endpoint logging
	app.router.Use(sloggin.NewWithFilters(app.log, sloggin.IgnorePathPrefix("/health")))
	app.router.Use(gin.Recovery())

	app.log.Info("Starting routes...")
	// emailCTRL.EmailRoutes(app.router, app.cfg)

	return nil
}
