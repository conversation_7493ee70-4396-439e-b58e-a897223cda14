/**
 * Utility functions for handling HTTP errors
 */

/**
 * Handles HTTP errors by redirecting to the appropriate error page
 * @param status The HTTP status code
 */
export const handleHttpError = (status: number): void => {
  let errorType: string;

  switch (status) {
    case 400:
      errorType = 'bad-request';
      break;
    case 401:
      errorType = 'unauthorized';
      break;
    case 403:
      errorType = 'forbidden';
      break;
    case 404:
      errorType = 'not-found';
      break;
    case 503:
      errorType = 'api';
      break;
    case 500:
    default:
      errorType = 'server';
      break;
  }

  // Redirect to the unified error page with the appropriate type
  window.location.href = `/error/${errorType}`;
};

/**
 * Gets the appropriate error image path based on the HTTP status code
 * @param status The HTTP status code
 * @returns The path to the error image
 */
export const getErrorImagePath = (status: number): string => {
  switch (status) {
    case 401:
      return '/images/401.png';
    case 403:
      return '/images/403.png';
    case 404:
      return '/images/404_err.png';
    case 500:
    default:
      return '/images/500.png';
  }
};
