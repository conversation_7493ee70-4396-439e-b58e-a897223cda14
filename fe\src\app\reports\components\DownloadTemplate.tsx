'use client'

import { Download } from '@mui/icons-material'
import { Box, Button, CircularProgress, Snackbar, Typography } from '@mui/material'
import { useState } from 'react'
import { bulkInsertDownloadService } from '@/app/reports/services'
import { downloadBlob } from '@/utils/helper'

export default function DownloadTemplate() {
    const [loading, setLoading] = useState(false)
    const [showSuccess, setShowSuccess] = useState(false)
    const [error, setError] = useState<string | null>(null)

    const handleTemplateDownload = async () => {
        setLoading(true)
        setError(null)
        try {
            const blob = await bulkInsertDownloadService()
            downloadBlob(blob, 'report_template.xlsx')
            setShowSuccess(true)
        } catch (error) {
            console.error('Download error:', error)
            setError('Failed to download template')
        } finally {
            setTimeout(() => setLoading(false), 500)
        }
    }

    return (
        <Box textAlign="center" maxWidth="20rem">
            <Typography variant="h6" gutterBottom>
                Bulk Upload Reports
            </Typography>
            <Typography variant="body2" color="text.secondary" gutterBottom>
                Upload report metadata using the provided template. Ensure the format is correct.
            </Typography>
            <Button
                variant="outlined"
                fullWidth
                startIcon={loading ? <CircularProgress size={20} color="inherit" /> : <Download />}
                color="secondary"
                onClick={handleTemplateDownload}
                disabled={loading}
            >
                {loading ? 'Downloading...' : 'Download Template'}
            </Button>
            {error && (
                <Typography variant="body2" color="error" sx={{ mt: 1 }}>
                    {error}
                </Typography>
            )}
            <Snackbar
                open={showSuccess}
                autoHideDuration={3000}
                onClose={() => setShowSuccess(false)}
                message="Template download started"
            />
        </Box>
    )
}
