package config

import (
	"context"
	"fmt"
	"path/filepath"
	"strings"

	"github.com/dapr/go-sdk/client"
	"github.com/spf13/viper"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	"gorm.io/gorm/schema"
)

var DB *gorm.DB

type Config struct {
	Dapr DaprConf `mapstructure:"dapr"`
	App  AppConf  `mapstructure:"app"`
	DB   DBConf   `mapstructure:"database"`
}

type DaprConf struct {
	SecretStore string            `mapstructure:"secret_store"`
	Secret      string            `mapstructure:"secret"`
	SecretKeys  map[string]string `mapstructure:"secret_keys"`
}

type AppConf struct {
	Host             string `mapstructure:"host"`
	Port             string `mapstructure:"port"`
	VersionApi       string `mapstructure:"version_api"`
	HealthCheckLive  string `mapstructure:"health_check_live"`
	HealthCheckReady string `mapstructure:"health_check_ready"`
}

type DBConf struct {
	Host     string `mapstructure:"host"`
	User     string `mapstructure:"user"`
	Password string `mapstructure:"password"`
	Name     string `mapstructure:"name"`
	Port     string `mapstructure:"port"`
	Schema   string `mapstructure:"schema"`
	DBName   string `mapstructure:"dbname"`
	SSLMode  string `mapstructure:"sslmode"`
}

func defaults() map[string]any {
	return map[string]any{
		"dapr.secret_store": "kubernetes",
		"dapr.secret":       "dng-module-secrets",
		"dapr.secret_keys": map[string]string{
			"db_password": "password",
			"db_user":     "user",
			"db_host":     "localhost",
			"db_name":     "postgres",
		},
		"app.host":               "0.0.0.0",
		"app.port":               "8080",
		"app.version_api":        "/version",
		"app.health_check_live":  "/healthz/live",
		"app.health_check_ready": "/healthz/ready",
		"database.sslmode":       "disable",
	}
}

func LoadConfig(file string) (Config, error) {
	viper.SetConfigFile(file)

	viper.AutomaticEnv()
	viper.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))

	for k, v := range defaults() {
		viper.SetDefault(k, v)
	}

	if err := viper.ReadInConfig(); err != nil {
		return Config{}, fmt.Errorf("error reading config file: %w", err)
	}

	var config Config
	if err := viper.Unmarshal(&config); err != nil {
		return Config{}, fmt.Errorf("error unmarshaling config: %w", err)
	}

	return config, nil
}

func LoadConfigByEnv(env string) (Config, error) {
	return LoadConfig(filepath.Join(".", "config", fmt.Sprintf("config.%s.yml", env)))
}
func LoadSecretsFromDapr(daprConf DaprConf) (map[string]string, error) {
	c, err := client.NewClient()
	if err != nil {
		return nil, fmt.Errorf("failed to create Dapr client: %w", err)
	}
	defer c.Close()

	secrets := make(map[string]string)
	for alias, key := range daprConf.SecretKeys {
		// Provide an empty metadata map as the fourth argument
		secretMap, err := c.GetSecret(context.Background(), daprConf.SecretStore, key, map[string]string{})
		if err != nil {
			return nil, fmt.Errorf("failed to fetch secret %s: %w", key, err)
		}
		// Store the actual secret value using alias as key
		if val, ok := secretMap[key]; ok {
			secrets[alias] = val
		} else {
			return nil, fmt.Errorf("secret key '%s' not found in Dapr response", key)
		}
	}
	return secrets, nil
}

func ConnectDatabase(dbConf DBConf) error {
	if dbConf.SSLMode == "" {
		dbConf.SSLMode = "disable"
	}

	dsn := fmt.Sprintf(
		"host=%s user=%s password=%s dbname=%s port=%s search_path=%s sslmode=%s",
		dbConf.Host,
		dbConf.User,
		dbConf.Password,
		dbConf.DBName,
		dbConf.Port,
		dbConf.Schema,
		dbConf.SSLMode,
	)

	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{
		NamingStrategy: schema.NamingStrategy{
			SingularTable: true,
		},
		DisableForeignKeyConstraintWhenMigrating: false,
		Logger:                                   logger.Default.LogMode(logger.Info),
	})
	if err != nil {
		return fmt.Errorf("failed to connect to the database: %w", err)
	}

	DB = db
	return nil
}
