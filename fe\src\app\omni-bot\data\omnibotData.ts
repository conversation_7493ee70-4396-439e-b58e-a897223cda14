import {
  TOPICS,
  CHATS,
  NETWORK_DATA_QA,
  AUDIT_QA,
  DC_SPECIFIC_QA,
  EMPLOYEE_PERFORMANCE_QA,
  ORDER_TOTE_ERRORS_QA,
  TOPIC_CONTEXT,
  TOPIC_KEYWORDS,
  GENERIC_RESPONSES,
  getTopicIdToKeyMap as getIdToKeyMap
} from './topics';

// Re-export all data
export {
  TOPICS,
  CHATS,
  NETWORK_DATA_QA,
  AUDIT_QA,
  DC_SPECIFIC_QA,
  EMPLOYEE_PERFORMANCE_QA,
  ORDER_TOTE_ERRORS_QA,
  TOPIC_CONTEXT,
  TOPIC_KEYWORDS,
  GENERIC_RESPONSES
};

// Helper Functions
export const getTopicIdToKeyMap = () => {
  return getIdToKeyMap();
};
