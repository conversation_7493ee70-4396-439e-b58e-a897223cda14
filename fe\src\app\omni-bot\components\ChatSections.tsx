import React from 'react'
import { Box, Divider, List, ListItem, ListItemButton, ListItemText } from '@mui/material'

interface ChatSectionsProps {
    sections: {
        title: string
        chats: string[]
    }[]
}

const ChatSections = ({ sections }: ChatSectionsProps) => {
    return (
        <Box sx={{ width: '100%', maxHeight: '100%', overflow: 'auto', p: 0 }}>
            {sections.map((section, index) => (
                <Box key={section.title} sx={{ mb: 1 }}>
                    {/* Section Divider and Title */}
                    <Divider sx={{ mb: 1 }} />
                    <ListItem>
                        <ListItemText
                            primary={section.title}
                            slotProps={{
                                primary: {
                                    variant: 'caption',
                                    color: 'text.secondary',
                                    fontWeight: 'bold',
                                },
                            }}
                            sx={{ m: 0 }}
                        />
                    </ListItem>
                    {/* Scrollable Chats */}
                    <List
                        sx={{
                            maxHeight: '150px', // Limit height for inner scroll
                            overflowY: 'auto',
                            p: 0,
                            '&::-webkit-scrollbar': { width: '6px' },
                            '&::-webkit-scrollbar-thumb': {
                                backgroundColor: 'rgba(0, 0, 0, 0.2)',
                                borderRadius: '6px',
                            },
                            '&::-webkit-scrollbar-thumb:hover': {
                                backgroundColor: 'rgba(0, 0, 0, 0.4)',
                            },
                        }}
                    >
                        {section.chats.map((chat, chatIndex) => (
                            <ListItemButton key={crypto.randomUUID()}>
                                <ListItemText primary={chat} sx={{ margin: 0 }} />
                            </ListItemButton>
                        ))}
                    </List>
                </Box>
            ))}
        </Box>
    )
}

export default ChatSections
