import LayoutControl from '@/components/common/layout/layout'
import '../app/global.css'


export const metadata = {
    title: 'Dollar General Analytics',
    description: 'Analytics platform for Dollar General',
    icons: {
        icon: [
            { url: '/images/icons/favicon.ico', sizes: 'any' },
            { url: '/images/icons/favicon-16x16.png', type: 'image/png', sizes: '16x16' },
            { url: '/images/icons/favicon-32x32.png', type: 'image/png', sizes: '32x32' },
            { url: '/images/icons/android-chrome-192x192.png', type: 'image/png', sizes: '192x192' },
            { url: '/images/icons/android-chrome-512x512.png', type: 'image/png', sizes: '512x512' }
        ],
        apple: [
            { url: '/images/icons/apple-touch-icon.png', sizes: '180x180', type: 'image/png' }
        ]
    },
    manifest: '/manifest.json',
    appleWebApp: {
        capable: true,
        statusBarStyle: 'default',
        title: 'DG Analytics'
    }
};

export const viewport = {
    width: 'device-width',
    initialScale: 1,
    maximumScale: 1,
    themeColor: '#FFDE00',
};


export default function RootLayout({ children }: Readonly<{ children: React.ReactNode }>) {
    return (
            <html lang="en">
                <head>
                    <link rel="preconnect" href="https://fonts.googleapis.com" />
                    <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
                    <link
                        href="https://fonts.googleapis.com/css2?family=Montserrat:wght@100;200;300;400;500;600;700;800;900&display=swap"
                        rel="stylesheet"
                    />
                    <link rel="icon" href="/favicon.ico" sizes="any" />
                    <link rel="icon" type="image/png" sizes="32x32" href="/images/icons/favicon-32x32.png" />
                    <link rel="icon" type="image/png" sizes="16x16" href="/images/icons/favicon-16x16.png" />
                    <link rel="apple-touch-icon" sizes="180x180" href="/images/icons/apple-touch-icon.png" />
                    <link rel="manifest" href="/site.webmanifest" />
                    <link rel="mask-icon" href="/images/icons/safari-pinned-tab.svg" color="#FFDE00" />
                    <meta name="msapplication-TileColor" content="#FFDE00" />
                    <meta name="msapplication-config" content="/browserconfig.xml" />
                    <meta name="theme-color" content="#FFDE00" />
                    <meta name="emotion-insertion-point" content="" />
                </head>
                <body>
                    <LayoutControl>{children}</LayoutControl>
                </body>
            </html>
    )
}
