import http from "k6/http";
import { group, check, sleep } from "k6";

//const BASE_URL = `https://${__ENV.K6_TARGET_HOST}`;

// Sleep duration between successive requests.
// You might want to edit the value of this variable or remove calls to the sleep function on the script.
const SLEEP_DURATION = 1;

// Global variables should be initialized.

export default function() {
    group("/get", () => {
       
        // Request No. 1
        {
            let url = "https://test-api.k6.io/";
            let request = http.get(url);

            check(request, {
                "Successful": (r) => r.status === 200
            });
        }
    });
      sleep(SLEEP_DURATION);

}

