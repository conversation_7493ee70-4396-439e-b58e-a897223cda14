package catalogReportService

import (
	"dng-module/internal/model"
	"errors"
	"fmt"

	"gorm.io/gorm"
)

const userReportWhereClause = "user_id = ? AND report_id = ?"

func UpdatePreference(db *gorm.DB, userID, reportID int, isFavorite bool, isSaved bool, view string) (model.UserReportPreference, error) {
	var preference model.UserReportPreference

	// Attempt to fetch the existing record
	err := db.Where(userReportWhereClause, userID, reportID).First(&preference).Error

	switch {
	case errors.Is(err, gorm.ErrRecordNotFound):
		if view == "favorites" && isFavorite {
			newPreference := model.UserReportPreference{
				UserID:     userID,
				ReportID:   reportID,
				IsFavorite: isFavorite,
			}
			if err := db.Create(&newPreference).Error; err != nil {
				return model.UserReportPreference{}, err
			}
			return newPreference, nil
		} else if view == "saved-later" && isSaved {
			newPreference := model.UserReportPreference{
				UserID:   userID,
				ReportID: reportID,
				IsSaved:  isSaved,
			}
			if err := db.Create(&newPreference).Error; err != nil {
				return model.UserReportPreference{}, err
			}
			return newPreference, nil
		}
		return model.UserReportPreference{}, nil

	case err != nil:
		return model.UserReportPreference{}, fmt.Errorf("failed to fetch user report preference: %w", err)

	default:
		if view == "favorites" {
			if !isFavorite && !preference.IsSaved {
				if err := db.Delete(&preference).Error; err != nil {
					return model.UserReportPreference{}, err
				}
				return preference, nil
			}
			if err := db.Model(&model.UserReportPreference{}).
				Where(userReportWhereClause, userID, reportID).
				Update("is_favorite", isFavorite).Error; err != nil {
				return model.UserReportPreference{}, err
			}
			preference.IsFavorite = isFavorite
		} else if view == "saved-later" {
			if !isSaved && !preference.IsFavorite {
				if err := db.Delete(&preference).Error; err != nil {
					return model.UserReportPreference{}, err
				}
				return preference, nil
			}
			if err := db.Model(&model.UserReportPreference{}).
				Where(userReportWhereClause, userID, reportID).
				Update("is_saved", isSaved).Error; err != nil {
				return model.UserReportPreference{}, err
			}
			preference.IsSaved = isSaved
		}
	}

	return preference, nil
}
