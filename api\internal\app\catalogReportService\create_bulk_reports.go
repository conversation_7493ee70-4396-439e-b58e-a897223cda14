package catalogReportService

import (
	"dng-module/config"
	"dng-module/internal/infra/db"
	"dng-module/internal/model"
	"dng-module/internal/utils"
	"errors"
	"fmt"
	"mime/multipart"
	"net/url"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/xuri/excelize/v2"
	"gorm.io/gorm"
)

// BulkCreateReportResult - result of bulk report creation
type BulkCreateReportResult struct {
	TotalProcessed   int                    `json:"total_processed"`
	SuccessCount     int                    `json:"success_count"`
	ErrorCount       int                    `json:"error_count"`
	Errors           []BulkUploadError      `json:"errors,omitempty"`
	UnchangedCount   int                    `json:"unchanged_count"`
	Reports          []model.ReportResponse `json:"reports,omitempty"`
	UpdatedReports   []model.ReportResponse `json:"updated_reports,omitempty"`
	UnchangedReports []model.ReportResponse `json:"unchanged_reports,omitempty"`
	ErrorFilePath    string                 `json:"error_file_path,omitempty"`
}

// BulkUploadError - an error during bulk upload
type BulkUploadError struct {
	RowNumber int    `json:"rowNumber"`
	ReportID  string `json:"reportId,omitempty"`
	Message   string `json:"message"`
}

// BulkCreateReports - Processes an Excel file to create multiple reports at once
func BulkCreateReports(cfg config.Config, file *multipart.FileHeader, createdBy string, userEmail string) (*BulkCreateReportResult, error) {
	result := &BulkCreateReportResult{}
	seenReportIDs := make(map[string]int) // Track Report IDs and their first occurrence row

	// Retrieve user by email
	var user model.User
	if err := db.DB.Where("email = ?", userEmail).First(&user).Error; err != nil {
		utils.Logger.Error(fmt.Sprintf("User %s not found: %v", userEmail, err))
		return nil, errors.New("unauthorized: user not found")
	}

	// Log the bulk upload attempt
	utils.Logger.Infof(fmt.Sprintf("User %s starting bulk report upload", userEmail))

	// Step 1: Get accessible domain IDs based on user_id and can_edit=true permissions
	var accessibleDomainIDs []int
	err := db.DB.Table("user_domain_access AS uda").
		Select("uda.domain_id").
		Joins("JOIN roles r ON r.id = uda.role_id").
		Where("uda.user_id = ? AND r.can_edit = ?", user.ID, true).
		Pluck("uda.domain_id", &accessibleDomainIDs).Error
	if err != nil {
		utils.Logger.Error(fmt.Sprintf("Failed to retrieve accessible domains for user %s: %v", userEmail, err))
		return nil, errors.New("failed to fetch accessible domains: " + err.Error())
	}

	if len(accessibleDomainIDs) == 0 {
		utils.Logger.Warn(fmt.Sprintf("User %s has no edit access to any domains", userEmail))
		return nil, errors.New("you don't have edit permissions for any domains")
	}

	utils.Logger.Info(fmt.Sprintf("User %s has edit access to %d domains", userEmail, len(accessibleDomainIDs)))

	// Fetch domain name->id map for faster lookup, but only for domains user has access to
	var allowedDomains []model.Domain
	if err := db.DB.Where("id IN ?", accessibleDomainIDs).Find(&allowedDomains).Error; err != nil {
		utils.Logger.Error(fmt.Sprintf("Failed to fetch domain names for user %s: %v", userEmail, err))
		return nil, errors.New("failed to fetch domain names for allowed IDs")
	}

	domainNameToID := make(map[string]int)
	for _, d := range allowedDomains {
		domainNameToID[d.Name] = d.ID
	}

	// Create a temporary file path with timestamp to avoid conflicts
	tempDir := os.TempDir()
	timestamp := time.Now().Format("20060102150405")
	tempFilePath := filepath.Join(tempDir, "report_upload_"+timestamp+".xlsx")
	error_file_path := filepath.Join(tempDir, "report_upload_errors_"+timestamp+".xlsx")

	// Save the uploaded file to a temporary location
	if err := saveUploadedFile(file, tempFilePath); err != nil {
		utils.Logger.Error(fmt.Sprintf("Failed to save uploaded file for user %s: %v", userEmail, err))
		return nil, err
	}
	defer os.Remove(tempFilePath)

	// Open the Excel file
	excelFile, err := excelize.OpenFile(tempFilePath)
	if err != nil {
		utils.Logger.Error(fmt.Sprintf("Failed to open Excel file for user %s: %v", userEmail, err))
		return nil, errors.New("failed to open Excel file: " + err.Error())
	}
	defer excelFile.Close()

	// Get the first sheet
	sheetName := excelFile.GetSheetName(0)
	rows, err := excelFile.GetRows(sheetName)
	if err != nil {
		utils.Logger.Error(fmt.Sprintf("Failed to read Excel rows for user %s: %v", userEmail, err))
		return nil, errors.New("failed to read Excel rows: " + err.Error())
	}

	if len(rows) <= 1 {
		utils.Logger.Warn(fmt.Sprintf("User %s uploaded empty Excel file", userEmail))
		return nil, errors.New("no data found in Excel file")
	}

	// Create a new Excel file for error feedback
	errorFile := excelize.NewFile()
	defer errorFile.Close()

	// Create headers with additional error column
	headers := append(rows[0], "Error Message")
	for colIdx, header := range headers {
		cellName, err := excelize.CoordinatesToCellName(colIdx+1, 1)
		if err != nil {
			utils.Logger.Error(fmt.Sprintf("Failed to create cell coordinate for user %s: %v", userEmail, err))
			return nil, errors.New("failed to create cell coordinate: " + err.Error())
		}
		errorFile.SetCellValue("Sheet1", cellName, header)
	}

	// Set column width for error message column
	errorColName, _ := excelize.ColumnNumberToName(len(headers))
	errorFile.SetColWidth("Sheet1", errorColName, errorColName, 50)

	result.TotalProcessed = len(rows) - 1
	hasErrors := false

	for i, row := range rows {
		if i == 0 {
			continue
		}

		rowIndex := i + 1
		var errorMessage string

		// ===== ADD DUPLICATE CHECKING CODE HERE =====
		if len(row) > 6 && row[6] != "" { // Check if Report ID column exists and has value
			reportID := row[6]
			if firstRow, exists := seenReportIDs[reportID]; exists {
				// Duplicate found - mark as error
				errorMessage = fmt.Sprintf("Duplicate Report ID '%s' (first seen in row %d)", reportID, firstRow)
				result.ErrorCount++
				result.Errors = append(result.Errors, BulkUploadError{
					RowNumber: rowIndex,
					ReportID:  reportID,
					Message:   errorMessage,
				})
				hasErrors = true

				// Write to error file
				for colIdx, cellValue := range row {
					cellName, _ := excelize.CoordinatesToCellName(colIdx+1, rowIndex)
					errorFile.SetCellValue("Sheet1", cellName, cellValue)
				}
				errorCellName, _ := excelize.CoordinatesToCellName(len(headers), rowIndex)
				errorFile.SetCellValue("Sheet1", errorCellName, errorMessage)

				// Highlight in orange
				style, _ := errorFile.NewStyle(&excelize.Style{
					Fill: excelize.Fill{Type: "pattern", Color: []string{"#FFA500"}, Pattern: 1},
				})
				for colIdx := 1; colIdx <= len(headers); colIdx++ {
					cellName, _ := excelize.CoordinatesToCellName(colIdx, rowIndex)
					errorFile.SetCellStyle("Sheet1", cellName, cellName, style)
				}

				continue // Skip this duplicate row
			}
			// Record first occurrence
			seenReportIDs[reportID] = rowIndex
		}

		if len(row) > 0 { // If URL exists (column index 8)
			report_url := row[0]
			utils.Logger.Infof(fmt.Sprintf("Row %d - Detected URL: %s", i+1, report_url))

			// Set SourceSystem based on URL type (new)
			if IsPowerBIURL(report_url) {
				row[1] = "powerbi" // Override SourceSystem
			} else if IsLookerURL(report_url) {
				row[1] = "looker" // Override SourceSystem
			}

			if IsPowerBIURL(report_url) {
				utils.Logger.Info("Processing Power BI URL")
				reportID, groupID, err := ExtractIDsFromPowerBIURL(report_url)
				if err != nil {
					// Create explicit error message for "me" workspace case
					errorMessage := fmt.Sprintf("Invalid Power BI URL: %v", err)
					if strings.Contains(err.Error(), "personal workspace URLs") {
						errorMessage = "Personal workspace URLs (with 'me') are not supported - please use a group workspace URL"
					}

					utils.Logger.Error(errorMessage)

					// Set error message that will be displayed in Excel
					rowError := BulkUploadError{
						RowNumber: rowIndex,
						ReportID:  reportID, // Will be empty if extraction failed
						Message:   errorMessage,
					}
					result.ErrorCount++
					result.Errors = append(result.Errors, rowError)
					hasErrors = true

					// Write to error file (maintaining your existing format)
					for colIdx, cellValue := range row {
						cellName, _ := excelize.CoordinatesToCellName(colIdx+1, rowIndex)
						errorFile.SetCellValue("Sheet1", cellName, cellValue)
					}
					errorCellName, _ := excelize.CoordinatesToCellName(len(headers), rowIndex)
					errorFile.SetCellValue("Sheet1", errorCellName, errorMessage)

					// Highlight error row in red (as in your existing code)
					style, _ := errorFile.NewStyle(&excelize.Style{
						Fill: excelize.Fill{Type: "pattern", Color: []string{"#FFCCCC"}, Pattern: 1},
					})
					for colIdx := 1; colIdx <= len(headers); colIdx++ {
						cellName, _ := excelize.CoordinatesToCellName(colIdx, rowIndex)
						errorFile.SetCellStyle("Sheet1", cellName, cellName, style)
					}

					continue // Skip further processing for this row
				}

				// Proceed with normal processing for valid group workspace URLs
				if row[6] == "" {
					row[6] = reportID
				}
				if row[7] == "" {
					row[7] = groupID
				}

				accessToken, err := GetAccessToken(cfg)
				if err != nil {
					utils.Logger.Error("Failed to get Power BI access token: ", err)
				} else {
					metadata, err := GetPowerBIMetaData(groupID, reportID, accessToken)
					if err != nil {
						utils.Logger.Error(fmt.Sprintf("Failed to fetch Power BI metadata: %v", err))
					} else {
						printPowerBIMetadata(metadata)
						if row[2] == "" && metadata.Name != "" {
							row[2] = metadata.Name
						}
					}
				}
			} else if IsLookerURL(report_url) {
				utils.Logger.Infof("Processing Looker URL")
				dashboardID, err := ExtractLookerDashboardID(report_url)
				if err != nil {
					utils.Logger.Error(fmt.Sprintf("Invalid Looker URL: %v", err))
				} else {
					// Use DashboardID if ReportID is empty (new)
					if row[6] == "" {
						row[6] = dashboardID
					}

					metadata, err := GetLookerDashMetadata(dashboardID, cfg)
					if err != nil {
						utils.Logger.Error(fmt.Sprintf("Failed to fetch Looker metadata: %v", err))
					} else {
						printLookerMetadata(metadata)
						// Populate Name if empty (new)
						if row[2] == "" && metadata.Title != "" {
							row[2] = metadata.Title
						}
					}
				}
			} else {
				utils.Logger.Warn("Unsupported URL type - must be Power BI or Looker")
			}
		}
		if len(row) < 7 {
			errorMessage = "Row has incomplete data"
			result.ErrorCount++
			result.Errors = append(result.Errors, BulkUploadError{
				RowNumber: rowIndex,
				Message:   errorMessage,
			})
			hasErrors = true
		} else {
			// Validate source system (column 1)
			sourceSystem := row[1]
			if sourceSystem == "" {
				errorMessage = "Source system cannot be empty"
			}
			// Validate domain (column 2)
			var domain model.Domain
			var domainID int = 0

			if errorMessage == "" && row[3] != "" {
				domainName := row[3]
				id, ok := domainNameToID[domainName]
				if !ok {
					errorMessage = "You don't have permission to upload data for this domain: " + domainName
				} else {
					domainID = id
					domain = model.Domain{Name: domainName, ID: domainID} // so you can use it later in response
				}

			} else if row[3] == "" {
				errorMessage = "Domain cannot be empty"
			}
			// Basic check only
			if errorMessage == "" {
				category := row[4]
				if category == "" {
					errorMessage = "Category cannot be empty"
				}
			}

			publishedOnStr := ""
			if len(row) > 8 { // Ensure the column exists
				publishedOnStr = row[8] // Assuming "Published On" is in the 8th column (index 7)
			}
			var publishedOn *time.Time
			if errorMessage == "" && publishedOnStr != "" {
				parsedDate, err := time.Parse("2006-01-02", publishedOnStr)
				if err != nil {
					errorMessage = "Invalid PublishedOn date format (expected YYYY-MM-DD)"
				} else {
					publishedOn = &parsedDate
				}
			}
			// If any validation failed, record the error
			if errorMessage != "" {
				result.ErrorCount++
				result.Errors = append(result.Errors, BulkUploadError{
					RowNumber: rowIndex,
					ReportID:  row[6],
					Message:   errorMessage,
				})
				hasErrors = true
			} else {
				// Check if report with same ReportID already exists (regardless of domain)
				// Check if report with same ReportID already exists (regardless of domain)
				var existing model.Report
				if err := db.DB.Where("report_id = ?", row[6]).First(&existing).Error; err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
					// Actual DB error
					errorMessage = "Database error while checking existing report: " + err.Error()
					result.ErrorCount++
					result.Errors = append(result.Errors, BulkUploadError{
						RowNumber: rowIndex,
						ReportID:  row[6],
						Message:   errorMessage,
					})
					hasErrors = true
				}

				if errorMessage == "" {
					// Prepare report DTO
					reportDto := model.CreateReportDto{
						SourceSystem: row[1],
						Name:         row[2],
						DomainID:     domainID,
						Category:     row[4],
						Description:  row[5],
						ReportID:     row[6],
						GroupID:      row[7],
						CreatedBy:    createdBy,
						PublishedOn:  publishedOn,
					}

					// If the report already exists, check if the data is different
					isUpdate := false
					isUnchanged := false

					if existing.ID != 0 {
						isUpdate = true

						// Compare existing report with new data
						if existing.Name == reportDto.Name &&
							existing.DomainID == reportDto.DomainID &&
							existing.Category == reportDto.Category &&
							existing.Description == reportDto.Description &&
							existing.SourceSystem == reportDto.SourceSystem &&
							existing.GroupID == reportDto.GroupID {
							isUnchanged = true
						}
					}
					if isUnchanged {
						// Mark it as unchanged and add to the result
						result.UnchangedCount++
						result.UnchangedReports = append(result.UnchangedReports, model.ReportResponse{
							ID:           existing.ID,
							Name:         existing.Name,
							ReportID:     existing.ReportID,
							DomainName:   domain.Name,
							SourceSystem: existing.SourceSystem,
							Description:  existing.Description,
							Category:     existing.Category,
						})

						//  Set hasErrors = true to force error file generation
						hasErrors = true

						//  Also write this row to errorFile
						for colIdx, cellValue := range row {
							cellName, _ := excelize.CoordinatesToCellName(colIdx+1, rowIndex)
							errorFile.SetCellValue("Sheet1", cellName, cellValue)
						}

						//  Add error message "No changes detected"
						errorMessage = "No changes detected"
						errorCellName, _ := excelize.CoordinatesToCellName(len(headers)+1, rowIndex)
						errorFile.SetCellValue("Sheet1", errorCellName, errorMessage)

						// ✅ Highlight the row in yellow
						style, _ := errorFile.NewStyle(&excelize.Style{
							Fill: excelize.Fill{Type: "pattern", Color: []string{"#FFFF99"}, Pattern: 1},
						})
						for colIdx := 1; colIdx <= len(headers)+1; colIdx++ {
							cellName, _ := excelize.CoordinatesToCellName(colIdx, rowIndex)
							errorFile.SetCellStyle("Sheet1", cellName, cellName, style)
						}

						continue
					} else if isUpdate {
						// Update existing report
						err = db.DB.Model(&existing).Updates(model.Report{
							Name:         reportDto.Name,
							DomainID:     reportDto.DomainID,
							Category:     reportDto.Category,
							Description:  reportDto.Description,
							SourceSystem: reportDto.SourceSystem,
							GroupID:      reportDto.GroupID,
							UpdatedAt:    time.Now(),
						}).Error

						if err != nil {
							errorMessage = "Failed to update report: " + err.Error()
							result.ErrorCount++
							result.Errors = append(result.Errors, BulkUploadError{
								RowNumber: rowIndex,
								ReportID:  row[6],
								Message:   errorMessage,
							})
							hasErrors = true
						} else {
							result.SuccessCount++
							result.UpdatedReports = append(result.UpdatedReports, model.ReportResponse{
								ID:           existing.ID,
								Name:         existing.Name,
								ReportID:     existing.ReportID,
								DomainName:   domain.Name,
								SourceSystem: existing.SourceSystem,
								Description:  existing.Description,
								Category:     existing.Category,
							})
						}

					} else {
						// Create new report
						report, err := CreateReport(reportDto, userEmail)

						if err != nil {
							errorMessage = "Failed to create report: " + err.Error()
							result.ErrorCount++
							result.Errors = append(result.Errors, BulkUploadError{
								RowNumber: rowIndex,
								ReportID:  row[6],
								Message:   errorMessage,
							})
							hasErrors = true
						} else {
							result.SuccessCount++
							result.Reports = append(result.Reports, *report)
						}
					}
				}
			}
		}

		// Write row to error file including error message if any
		for colIdx, cellValue := range row {
			cellName, _ := excelize.CoordinatesToCellName(colIdx+1, rowIndex)
			errorFile.SetCellValue("Sheet1", cellName, cellValue)
		}

		// Add error message in the last column if there is one
		if errorMessage != "" {
			errorCellName, _ := excelize.CoordinatesToCellName(len(headers), rowIndex)
			errorFile.SetCellValue("Sheet1", errorCellName, errorMessage)

			// Highlight error rows
			style, _ := errorFile.NewStyle(&excelize.Style{
				Fill: excelize.Fill{Type: "pattern", Color: []string{"#FFCCCC"}, Pattern: 1},
			})

			// Apply style to the entire row
			for colIdx := 1; colIdx <= len(headers); colIdx++ {
				cellName, _ := excelize.CoordinatesToCellName(colIdx, rowIndex)
				errorFile.SetCellStyle("Sheet1", cellName, cellName, style)
			}
		}
	}

	// Save the error file only if there were errors
	if hasErrors {
		if err := errorFile.SaveAs(error_file_path); err != nil {
			utils.Logger.Error(fmt.Sprintf("Failed to save error file for user %s: %v", userEmail, err))
			return nil, errors.New("failed to save error file: " + err.Error())
		}
		result.ErrorFilePath = error_file_path
	}

	// Log the completion of processing
	utils.Logger.Info(fmt.Sprintf("User %s completed bulk report upload. Total: %d, Success: %d, Errors: %d, Unchanged: %d",
		userEmail,
		result.TotalProcessed,
		result.SuccessCount,
		result.ErrorCount,
		result.UnchangedCount))

	return result, nil
}

// saveUploadedFile saves the uploaded file to the destination path
func saveUploadedFile(file *multipart.FileHeader, dst string) error {
	src, err := file.Open()
	if err != nil {
		return err
	}
	defer src.Close()

	out, err := os.Create(dst)
	if err != nil {
		return err
	}
	defer out.Close()

	buffer := make([]byte, 32*1024)

	for {
		n, err := src.Read(buffer)
		if n > 0 {
			_, werr := out.Write(buffer[:n])
			if werr != nil {
				return werr
			}
		}
		if err != nil {
			break
		}
	}

	return nil
}

// GetErrorFile returns the file with error annotations
func GetErrorFile(filePath string) (*os.File, error) {
	// Check if file exists
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		return nil, fmt.Errorf("error file not found: %s", filePath)
	}

	// Open the file
	file, err := os.Open(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to open error file: %s", err.Error())
	}

	return file, nil
}

func ExtractLookerDashboardID(rawURL string) (string, error) {
	// Use url.Parse() from the net/url package
	u, err := url.Parse(rawURL)
	if err != nil {
		return "", fmt.Errorf("invalid URL format: %v", err)
	}

	// Rest of your parsing logic...
	segments := strings.Split(u.Path, "/")
	for i, seg := range segments {
		if seg == "dashboards" && i+1 < len(segments) {
			return segments[i+1], nil
		}
	}

	// Alternative: Extract from query params
	q := u.Query()
	if dashboardID := q.Get("dashboard"); dashboardID != "" {
		return dashboardID, nil
	}

	return "", errors.New("dashboard ID not found in URL")
}

func ExtractIDsFromPowerBIURL(powerbiURL string) (reportID, groupID string, err error) {
	// Parse the URL using the `net/url` package
	u, err := url.Parse(powerbiURL)
	if err != nil {
		return "", "", fmt.Errorf("invalid URL format: %v", err)
	}

	// Extract IDs from path segments (e.g., "/groups/{groupID}/reports/{reportID}")
	segments := strings.Split(strings.Trim(u.Path, "/"), "/")
	for i, seg := range segments {
		if seg == "reports" && i+1 < len(segments) {
			reportID = segments[i+1]
		}
		if seg == "groups" && i+1 < len(segments) {
			groupID = segments[i+1]
		}
	}

	// Fallback to query parameters (e.g., "?reportId=...&groupId=...")
	if reportID == "" {
		reportID = u.Query().Get("reportId")
	}
	if groupID == "" {
		groupID = u.Query().Get("groupId")
	}

	// Validate - specifically reject "me" workspace
	if reportID == "" {
		return "", "", fmt.Errorf("reportID not found in URL")
	}
	if groupID == "me" {
		return "", "", fmt.Errorf("personal workspace URLs (with 'me') are not supported - please provide a URL with explicit groupID")
	}
	if groupID == "" {
		return "", "", fmt.Errorf("groupID not found in URL - please provide a valid Power BI URL containing groupID")
	}

	return reportID, groupID, nil
}

func printPowerBIMetadata(metadata *model.PowerBIReportMetaData) {
	utils.Logger.Info("----- Power BI Metadata -----")
	utils.Logger.Info(fmt.Sprintf("Report Name: %s", metadata.Name))
	utils.Logger.Info(fmt.Sprintf("Report ID: %s", metadata.ID))
	utils.Logger.Info(fmt.Sprintf("Embed URL: %s", metadata.EmbedURL))
	utils.Logger.Info(fmt.Sprintf("Dataset ID: %s", metadata.DatasetID))
	utils.Logger.Info(fmt.Sprintf("Web URL: %s", metadata.WebURL))
	utils.Logger.Info("-----------------------------")
}

func printLookerMetadata(metadata *model.LookerDashboardMetadata) {
	utils.Logger.Info("----- Looker Dashboard Metadata -----")
	utils.Logger.Info(fmt.Sprintf("Title: %s", metadata.Title))
	utils.Logger.Info(fmt.Sprintf("Dashboard ID: %s", metadata.ID))
	utils.Logger.Info("-------------------------------------")
}

func IsPowerBIURL(url string) bool {
	return strings.Contains(url, "powerbi.com") ||
		strings.Contains(url, "analysis.windows.net")
}

func IsLookerURL(url string) bool {
	return strings.Contains(url, "looker.com") ||
		strings.Contains(url, os.Getenv("LOOKER_CUSTOM_DOMAIN"))
}
