import Loading from '@/app/(landing)/loading'
import { render, screen } from '@testing-library/react'

// Mock Loader component
jest.mock('@/components/common/utility/Loader', () => () => <div data-testid="loader">Mocked Loader</div>)

describe('Landing Loading', () => {
    it('renders Loader', () => {
        render(<Loading />)
        expect(screen.getByTestId('loader')).toBeInTheDocument()
        expect(screen.getByText('Mocked Loader')).toBeInTheDocument()
    })
})
