
# Standard Documentation

[Developer Guide](https://confluence.dolgen.net/display/DEVOP/Developer+Guide)  
[Gitlab Pipeline](https://confluence.dolgen.net/display/DEVOP/Gitlab+Pipeline+Stages)  
[SDLC](https://confluence.dolgen.net/display/DEVOP/Methods+and+Tools#MethodsandTools-SystemDeliveryLifeCycle)  
[Tools](https://confluence.dolgen.net/display/DEVOP/Tools+Overview)  
[Standards](https://confluence.dolgen.net/display/DEVOP/Standards)  
[Methods and Tools Space](https://confluence.dolgen.net/display/DEVOP/Methods+and+Tools)  


# Sonar Local Installation
npm install --save-dev sonarqube-scanner

- verify installation
  npx sonarqube-scanner --help

- add in
```bash 
    "scripts": {
    "sonar": "sonarqube-scanner"
    } 
```

- npm run sonar


# Sonar Coverage + Jest Setup
```bash 
npm install -D jest jest-environment-jsdom @testing-library/react @testing-library/dom @testing-library/jest-dom ts-node
```
npm run test:coverage
npx sonar

# Feature Flags,
- add below flags to enable bulk exports into .env
  NEXT_PUBLIC_BULK_UPLOAD_ENABLED=true
  NEXT_PUBLIC_IS_EXPORT_ENABLED=true
  NEXT_PUBLIC_IS_PREFERENCE_ENABLED=true