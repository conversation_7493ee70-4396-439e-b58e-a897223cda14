import ReportForm from '@/app/reports/reportCards/components/ReportForm'
import { render, screen } from '@testing-library/react'

jest.mock('next-auth/react', () => ({
    useSession: () => ({ data: { user: { email: '<EMAIL>' } } }),
}))
jest.mock('@/store/hooks', () => ({
    useAppDispatch: () => jest.fn(),
    useAppSelector: () => ({ filterOptions: { domains: [] } }),
}))
jest.mock('@/app/reports/services', () => ({
    getPBIReportMetaData: jest.fn(),
    getLookerReportMetaData: jest.fn(),
    upsertReport: jest.fn(),
}))

describe('ReportForm', () => {
    it('renders dialog with form fields', () => {
        render(<ReportForm open={true} onClose={jest.fn()} />)
        expect(screen.getByText(/Add Report/i)).toBeInTheDocument()
        expect(screen.getByLabelText(/Name/i)).toBeInTheDocument()
    })
})
