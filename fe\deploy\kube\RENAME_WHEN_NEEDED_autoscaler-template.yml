apiVersion: keda.sh/v1alpha1
kind: ScaledObject
metadata:
  labels: 
    cattle.io/creator: {{GITLAB_USER_LOGIN}}
  name: "{{CI_PROJECT_NAME}}-autoscaler"
  namespace: {{KUBE_NAMESPACE}}
spec:
  scaleTargetRef:
    kind: Deployment
    name: {{CI_PROJECT_NAME}}
  minReplicaCount:  {{KUBE_POD_REPLICAS}}
  triggers:
  - type: memory
    metricType: Utilization # Allowed types are 'Utilization' or 'AverageValue'
    metadata:
      value: "85"