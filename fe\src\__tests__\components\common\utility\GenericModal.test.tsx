import { render, screen, fireEvent } from '@testing-library/react'

import '@testing-library/jest-dom'
import GenericModal from '@/components/common/utility/GenericModal'

describe('GenericModal', () => {
    const defaultProps = {
        open: true,
        onClose: jest.fn(),
        title: 'Test Modal',
        children: <div data-testid="modal-content">Content</div>,
        actions: <button data-testid="modal-action">Action</button>,
    }

    afterEach(() => {
        jest.clearAllMocks()
    })

    test('renders when open is true', () => {
        render(<GenericModal {...defaultProps} />)
        expect(screen.getByText('Test Modal')).toBeInTheDocument()
        expect(screen.getByTestId('modal-content')).toBeInTheDocument()
        expect(screen.getByTestId('modal-action')).toBeInTheDocument()
    })

    test('does not render when open is false', () => {
        render(<GenericModal {...defaultProps} open={false} />)
        expect(screen.queryByText('Test Modal')).not.toBeInTheDocument()
    })

    test('close button calls onClose when clicked', () => {
        render(<GenericModal {...defaultProps} showCloseButton={true} />)
        const closeButton = screen.getByLabelText('close')
        fireEvent.click(closeButton)
        expect(defaultProps.onClose).toHaveBeenCalled()
    })

    test('does not render close button if showCloseButton is false', () => {
        render(<GenericModal {...defaultProps} showCloseButton={false} />)
        expect(screen.queryByLabelText('close')).not.toBeInTheDocument()
    })

    test('renders custom ReactNode title correctly', () => {
        const customTitle = <div data-testid="custom-title">Custom Title</div>
        render(<GenericModal {...defaultProps} title={customTitle} />)
        expect(screen.getByTestId('custom-title')).toBeInTheDocument()
    })
})
