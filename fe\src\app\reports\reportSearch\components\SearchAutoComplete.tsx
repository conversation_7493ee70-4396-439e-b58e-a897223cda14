import { Clear, Search } from '@mui/icons-material'
import { Autocomplete, Box, InputAdornment, TextField, Typography } from '@mui/material'
import { debounce } from 'lodash'
import { useSession } from 'next-auth/react'
import { useCallback, useEffect, useState } from 'react'
import { getSearchOptions } from '../../services'
import { IReportSearchOption } from '../../types'

interface CustomAutocompleteProps {
    value: string | null
    onChange: (event: any, newValue: string) => void
    sx?: object
}

export default function SearchAutoComplete({ value, onChange, sx }: Readonly<CustomAutocompleteProps>) {
    const [options, setOptions] = useState<IReportSearchOption[]>([])
    const [loading, setLoading] = useState(false)
    const [inputValue, setInputValue] = useState('')
    const { data: session } = useSession()

    // Debounced API Call
    const fetchOptions = useCallback(
        debounce(async (query: string) => {
            setLoading(true)
            const results = await getSearchOptions(query, session)
            setOptions(results)
            setLoading(false)
        }, 500),
        [session],
    )

    useEffect(() => {
        if (inputValue) {
            fetchOptions(inputValue)
        } else {
            setOptions([])
        }
    }, [inputValue, fetchOptions])

    const handleInputChange = (event: any, newValue: string) => {
        setInputValue(newValue)
    }

    return (
        <Autocomplete
            freeSolo
            value={value}
            sx={{ minWidth: 300, ...sx }}
            onInputChange={handleInputChange}
            onChange={(event: any, newValue: any) => {
                onChange(event, newValue?.name)
            }}
            inputValue={inputValue}
            options={options}
            loading={loading}
            color="secondary"
            disableClearable={true}
            getOptionLabel={(option) => {
                if (typeof option === 'string') return option
                return option.name
            }}
            renderOption={({ key, ...props }, option) => (
                <li
                    key={key}
                    {...props}
                    style={{
                        padding: '8px 16px',
                        cursor: 'pointer',
                    }}
                >
                    <RenderOption option={option} inputValue={inputValue} />
                </li>
            )}
            isOptionEqualToValue={(option, value) => {
                if (typeof option === 'string' || typeof value === 'string') {
                    return option === value
                }
                return option.name === value.name
            }}
            renderInput={(params) => (
                <TextField
                    {...params}
                    placeholder="Search Reporting Assets, Sales Catalog..."
                    variant="outlined"
                    sx={{ minWidth: '300px' }}
                    size="small"
                    slotProps={{
                        input: {
                            ...params.InputProps,
                            startAdornment: (
                                <InputAdornment position="start">
                                    <Search />
                                </InputAdornment>
                            ),
                            endAdornment: (
                                <>
                                    {inputValue && (
                                        <InputAdornment position="end">
                                            <Clear
                                                fontSize={'small'}
                                                onClick={() => {
                                                    setInputValue('')
                                                    onChange(null, '')
                                                }}
                                                sx={{
                                                    cursor: 'pointer',
                                                    color: 'grey.500',
                                                    '&:hover': { color: 'black' },
                                                }}
                                            />
                                        </InputAdornment>
                                    )}
                                    {params.InputProps.endAdornment}
                                </>
                            ),
                        },
                    }}
                />
            )}
        />
    )
}

// Helper function to highlight matching text
const highlightMatch = (text: string, query: string) => {
    if (!query) return text
    const parts = text.split(new RegExp(`(${query})`, 'gi'))
    return parts.map((part, index) =>
        part.toLowerCase() === query.toLowerCase() ? (
            <span key={crypto.randomUUID()} style={{ fontWeight: 'bold' }}>
                {part}
            </span>
        ) : (
            part
        ),
    )
}

function RenderOption({ option, inputValue }: Readonly<{ option: IReportSearchOption; inputValue: string }>) {
    return (
        <Box
            sx={{
                width: '100%',
                padding: '8px 12px',
                borderRadius: '8px',
                transition: 'background-color 0.2s ease-in-out',
                '&:hover': { backgroundColor: 'rgba(0, 0, 0, 0.06)' },
            }}
        >
            <Typography
                variant="body2"
                sx={{
                    fontWeight: 500,
                    color: 'text.primary',
                }}
            >
                {highlightMatch(option.name, inputValue)}
            </Typography>
            <Box display="flex" gap={1} alignItems="center" mt={0.5}>
                <Typography
                    variant="caption"
                    sx={{
                        fontWeight: 'bold',
                        color: 'white',
                        backgroundColor: 'success.main',
                        padding: '2px 8px',
                        borderRadius: '12px',
                        fontSize: '0.75rem',
                    }}
                >
                    {option.source_system}
                </Typography>
                <Typography
                    variant="caption"
                    sx={{
                        fontWeight: 500,
                        color: 'text.secondary',
                        fontSize: '0.75rem',
                    }}
                >
                    {option.category}
                </Typography>
            </Box>
        </Box>
    )
}
