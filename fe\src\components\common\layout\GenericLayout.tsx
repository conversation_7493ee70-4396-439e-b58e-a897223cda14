'use client'

import Header from '@/components/common/layout/header/header'
import Sidebar from '@/components/common/layout/sidebar/Sidebar'
import { Box, Container, styled } from '@mui/material'
import React, { useState } from 'react'

const MainWrapper = styled('div')(() => ({
    display: 'flex',
    minHeight: '100vh',
    width: '100%',
    position: 'relative',
}))

const PageWrapper = styled('div')(() => ({
    display: 'flex',
    flexGrow: 1,
    flexDirection: 'column',
    zIndex: 1,
    //paddingBottom: '60px',
}))

const ContentBox = styled(Box)(() => ({
    minHeight: 'calc(100vh - 170px)',
    //margin: '1rem',
}))

interface AppLayoutProps {
    children: React.ReactNode
    showSidebar?: boolean
    showHeader?: boolean
    showLogoInHeader?: boolean
    useContainer?: boolean
    backgroundImageSrc?: string
}

export default function AppLayout({
    children,
    showSidebar = false,
    showHeader = true,
    showLogoInHeader = false,
    useContainer = false,
    backgroundImageSrc,
}: Readonly<AppLayoutProps>) {
    const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false)

    return (
        <MainWrapper>
            {showSidebar && (
                <Sidebar
                    isSidebarOpen={true}
                    isMobileSidebarOpen={isMobileSidebarOpen}
                    onSidebarClose={() => setIsMobileSidebarOpen(false)}
                />
            )}
            <PageWrapper style={{ backgroundColor: backgroundImageSrc ? 'none' : 'rgba(0,0,0,0.15)' }}>
                {showHeader && (
                    <Header toggleMobileSidebar={() => setIsMobileSidebarOpen(true)} showLogo={showLogoInHeader} />
                )}
                {useContainer ? (
                    <Container
                        disableGutters
                        sx={{
                            paddingTop: '15px',
                            maxWidth: '1200px',
                            paddingLeft: '15px',
                            paddingRight: '15px',
                        }}
                    >
                        <ContentBox>{children}</ContentBox>
                    </Container>
                ) : (
                    <ContentBox sx={{ px: 4, pt: 4, mx: { xs: 0, md: 2, lg: 10 } }}>{children}</ContentBox>
                )}
            </PageWrapper>

            {backgroundImageSrc && (
                <Box
                    sx={{
                        position: 'absolute',
                        top: 120,
                        left: 0,
                        right: 0,
                        bottom: 0,
                        zIndex: 0,
                        background: `url(${backgroundImageSrc}) no-repeat center center`,
                        backgroundSize: 'cover',
                        //width: '100%',
                        //height: '100%',
                        '@media (max-width: 768px)': {
                            backgroundPosition: 'center center',
                        },
                    }}
                />
            )}
        </MainWrapper>
    )
}
