// Type Definitions
import { SOURCE_OPTIONS } from '@/utils/constants'

export type ReportSource = SOURCE_OPTIONS.PowerBI | SOURCE_OPTIONS.Looker

export interface IReport {
    id: string
    sourceSystem: ReportSource
    name: string
    category: string
    domain: string
    description: string
    reportId: string
    groupId?: string
    createdAt: string
    updatedAt: string
    preferences: {
        isFavorite: boolean
        isSavedForLater: boolean
    }
}

export interface IReportSearchOption {
    id: string
    name: string
    category: string
    domain_name: string
    source_system: string
}

export interface IPbiReport
    extends Omit<IReport, 'category' | 'domain' | 'createdAt' | 'updatedAt' | 'preferences' | 'reportId'> {
    groupId: string
}

export interface DateRange {
    startDate: Date | null
    endDate: Date | null
}

export interface BulkUploadError {
    row_number: number
    report_id: string
    message: string
}

export interface BulkUploadResponse {
    message: string
    data: {
        total_processed: number
        success_count: number
        error_count: number
        errors?: BulkUploadError[]
        error_file_path: string
        unchanged_count?: number
    }
}

// REPORT TABLE
export interface Report {
    id: string
    name: string
    report_id: string
    description: string
    category: string
    domain_id: number
    source_system: string
    group_id: string
    created_by: string
    created_at: string
    updated_at: string
    is_favorite: boolean
    is_saved: boolean
    published_on: Date | null
}

export interface Domain {
    id: number
    name: string
    description: string
    report: Report[] // This will hold an array of reports for each domain
}

export interface Meta {
    total: number
    page: number
    limit: number
    total_pages: number
}

export interface ApiResponse {
    data: Domain[]
    meta: Meta
}

export interface ReportMetaResponse {
    status: string
    message: string
    data: {
        id: string
        name: string
        description: string
        title: string
    }
}
