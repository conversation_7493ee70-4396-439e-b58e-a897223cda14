import { Box, Typography } from '@mui/material'
import { TrendingUp } from '@mui/icons-material'
import Grid from '@mui/material/Grid2'

export default function MetricsHeader() {
    return (
        <Grid container spacing={3}>
            <Grid size={{ xs: 12, sm: 6 }}>
                <Box>
                    <Typography variant="h3" component="h2" fontWeight="bold">
                        $5,954,496
                    </Typography>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Typography variant="body2" color="text.secondary">
                            Sales $
                        </Typography>
                        <Typography variant="body2" color="success.main" sx={{ display: 'flex', alignItems: 'center' }}>
                            <TrendingUp sx={{ fontSize: 16, mr: 0.5 }} />
                            4.3% Up from last year
                        </Typography>
                    </Box>
                </Box>
            </Grid>
            <Grid size={{ xs: 12, sm: 6 }}>
                <Box>
                    <Typography variant="h3" component="h2" fontWeight="bold">
                        9.60%
                    </Typography>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Typography variant="body2" color="text.secondary">
                            Forecast %
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                            7% Previous
                        </Typography>
                    </Box>
                </Box>
            </Grid>
        </Grid>
    )
}
