package userService

import (
	"dng-module/internal/model"
	"dng-module/internal/utils"
	"fmt"

	"gorm.io/gorm"
)

func GetAllDomains(db *gorm.DB, userEmail string) ([]model.Domain, error) {
	utils.Logger.Info(fmt.Sprintf("Starting to fetch domains for user: %s", userEmail))

	var user model.User
	if err := db.Where("email = ?", userEmail).First(&user).Error; err != nil {
		utils.Logger.Error(fmt.Sprintf("User not found: Unable to find user with email %s in the system", userEmail), err)
		return nil, err
	}
	utils.Logger.Info(fmt.Sprintf("User found: Successfully identified user %s (ID: %d)", userEmail, user.ID))

	// Join user_domain_access with role to check if user is a super admin
	type RoleCheck struct {
		IsSuperAdmin bool
		DomainID     uint
	}

	var roleChecks []RoleCheck
	if err := db.Table("user_domain_access").
		Select("user_domain_access.domain_id, role.is_super_admin").
		Joins("JOIN roles ON user_domain_access.role_id = role.id").
		Where("user_domain_access.user_id = ?", user.ID).
		Scan(&roleChecks).Error; err != nil {
		utils.Logger.Error(fmt.Sprintf("Permission check failed: Unable to retrieve domain access information for user %s", userEmail), err)
		return nil, err
	}

	if len(roleChecks) == 0 {
		utils.Logger.Info(fmt.Sprintf("No access: User %s does not have access to any domains", userEmail))
		return []model.Domain{}, nil
	}
	utils.Logger.Info(fmt.Sprintf("Access check complete: User %s has access to %d domains", userEmail, len(roleChecks)))

	// Check if user has at least one super admin role
	for _, rc := range roleChecks {
		if rc.IsSuperAdmin {
			var allDomains []model.Domain
			if err := db.Find(&allDomains).Error; err != nil {
				utils.Logger.Error(fmt.Sprintf("Database error: Failed to retrieve all domains for admin user %s", userEmail), err)
				return nil, err
			}
			utils.Logger.Info(fmt.Sprintf("Super Admin access: User %s is an administrator and can access all %d domains in the system", userEmail, len(allDomains)))
			return allDomains, nil
		}
	}

	// Else return only the assigned domains
	var domainIDs []uint
	for _, rc := range roleChecks {
		domainIDs = append(domainIDs, rc.DomainID)
	}

	var domains []model.Domain
	if err := db.Where("id IN ?", domainIDs).Find(&domains).Error; err != nil {
		utils.Logger.Error(fmt.Sprintf("Database error: Failed to retrieve assigned domains for user %s", userEmail), err)
		return nil, err
	}

	utils.Logger.Info(fmt.Sprintf("Access granted: User %s has access to %d specific domains", userEmail, len(domains)))
	return domains, nil
}
