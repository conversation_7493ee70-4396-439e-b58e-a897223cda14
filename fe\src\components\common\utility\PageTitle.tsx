import React from 'react'
import { Typography, TypographyProps } from '@mui/material'

interface PageTitleProps extends TypographyProps {
    title: string // Title text
}

const PageTitle: React.FC<PageTitleProps> = ({ title, variant = 'h6', component = 'h6', sx, ...props }) => {
    return (
        <Typography
            variant={variant}
            component={component}
            sx={{
                fontWeight: 'bold',
                ...sx,
            }}
            {...props}
        >
            {title}
        </Typography>
    )
}

export default PageTitle
