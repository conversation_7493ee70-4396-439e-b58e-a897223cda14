package catalogReportService

import (
	"bytes"
	"dng-module/config"

	// "dng-module/internal/app/catalogReportService"
	"dng-module/internal/model"
	"dng-module/testing/utilsTest"
	"fmt"
	"io"
	"mime/multipart"
	"os"
	"path/filepath"
	"strings"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"github.com/xuri/excelize/v2"
	"gorm.io/gorm"
)

func createTestFileHeader(t *testing.T, tempDir string, filename string, setupFunc func(*excelize.File)) *multipart.FileHeader {
	// Create Excel file
	f := excelize.NewFile()
	setupFunc(f)

	// Save to temp file
	tempFilePath := filepath.Join(tempDir, filename)
	if err := f.SaveAs(tempFilePath); err != nil {
		t.Fatalf("Failed to create test Excel file: %v", err)
	}

	// Open the file and read its content
	file, err := os.Open(tempFilePath)
	if err != nil {
		t.Fatalf("Failed to open test file: %v", err)
	}
	defer file.Close()

	// Read file content
	fileContent, err := io.ReadAll(file)
	if err != nil {
		t.Fatalf("Failed to read file content: %v", err)
	}

	// Create a buffer to simulate multipart form data
	var buf bytes.Buffer
	writer := multipart.NewWriter(&buf)

	// Create the file field
	fileWriter, err := writer.CreateFormFile("file", filepath.Base(tempFilePath))
	if err != nil {
		t.Fatalf("Failed to create form file: %v", err)
	}

	// Write file content
	_, err = fileWriter.Write(fileContent)
	if err != nil {
		t.Fatalf("Failed to write file content: %v", err)
	}

	// Close writer
	writer.Close()

	// Parse the multipart form
	reader := multipart.NewReader(&buf, writer.Boundary())
	form, err := reader.ReadForm(32 << 20) // 32MB max
	if err != nil {
		t.Fatalf("Failed to read form: %v", err)
	}
	defer form.RemoveAll()

	// Get the file header
	if files, ok := form.File["file"]; ok && len(files) > 0 {
		return files[0]
	}

	t.Fatalf("Failed to create file header")
	return nil
}

func TestBulkCreateReports(t *testing.T) {
	tests := []struct {
		name          string
		mockClosure   func(*testing.T, sqlmock.Sqlmock)
		fileSetup     func(*testing.T) *multipart.FileHeader
		wantResult    *BulkCreateReportResult
		wantErr       bool
		errorContains string
	}{
		{
			name: "empty Excel file",
			mockClosure: func(t *testing.T, mock sqlmock.Sqlmock) {
				userEmail := "<EMAIL>"
				validUser := model.User{ID: 1, Email: userEmail}

				mock.ExpectQuery(`SELECT \* FROM "users" WHERE .*`).
					WithArgs(userEmail, 1).
					WillReturnRows(sqlmock.NewRows([]string{"id", "email"}).AddRow(validUser.ID, validUser.Email))

				mock.ExpectQuery(`SELECT uda.domain_id FROM user_domain_access AS uda JOIN roles r ON r.id = uda.role_id WHERE .*`).
					WithArgs(validUser.ID, true).
					WillReturnRows(sqlmock.NewRows([]string{"domain_id"}).AddRow(1))

				mock.ExpectQuery(`SELECT \* FROM "domains" WHERE .*`).
					WithArgs(1).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).AddRow(1, "Finance"))
			},
			fileSetup: func(t *testing.T) *multipart.FileHeader {
				tempDir := t.TempDir()
				return createTestFileHeader(t, tempDir, "test_empty.xlsx", func(f *excelize.File) {
					// Only headers, no data rows
					f.SetCellValue("Sheet1", "A1", "URL")
					f.SetCellValue("Sheet1", "B1", "SourceSystem")
					f.SetCellValue("Sheet1", "C1", "Name")
					f.SetCellValue("Sheet1", "D1", "Domain")
					f.SetCellValue("Sheet1", "E1", "Category")
					f.SetCellValue("Sheet1", "F1", "Description")
					f.SetCellValue("Sheet1", "G1", "ReportID")
					f.SetCellValue("Sheet1", "H1", "GroupID")
				})
			},
			wantErr:       true,
			errorContains: "no data found in Excel file",
		},
		{
			name: "database error fetching domains",
			mockClosure: func(t *testing.T, mock sqlmock.Sqlmock) {
				userEmail := "<EMAIL>"
				validUser := model.User{ID: 1, Email: userEmail}

				mock.ExpectQuery(`SELECT \* FROM "users" WHERE .*`).
					WithArgs(userEmail, 1).
					WillReturnRows(sqlmock.NewRows([]string{"id", "email"}).AddRow(validUser.ID, validUser.Email))

				mock.ExpectQuery(`SELECT uda.domain_id FROM user_domain_access AS uda JOIN roles r ON r.id = uda.role_id WHERE .*`).
					WithArgs(validUser.ID, true).
					WillReturnRows(sqlmock.NewRows([]string{"domain_id"}).AddRow(1))

				mock.ExpectQuery(`SELECT \* FROM "domains" WHERE .*`).
					WithArgs(1).
					WillReturnError(fmt.Errorf("database error"))
			},
			fileSetup: func(t *testing.T) *multipart.FileHeader {
				tempDir := t.TempDir()
				return createTestFileHeader(t, tempDir, "test_db_error.xlsx", func(f *excelize.File) {
					f.SetCellValue("Sheet1", "A1", "URL")
					f.SetCellValue("Sheet1", "A2", "https://example.com")
				})
			},
			wantErr:       true,
			errorContains: "failed to fetch domain names for allowed IDs",
		},
		{
			name: "successful bulk upload with new reports",
			mockClosure: func(t *testing.T, mock sqlmock.Sqlmock) {
				userEmail := "<EMAIL>"
				validUser := model.User{ID: 1, Email: userEmail}

				// Mock user lookup for bulk service
				mock.ExpectQuery(`SELECT \* FROM "users" WHERE .*`).
					WithArgs(userEmail, 1).
					WillReturnRows(sqlmock.NewRows([]string{"id", "email"}).AddRow(validUser.ID, validUser.Email))

				// Mock accessible domains
				mock.ExpectQuery(`SELECT uda.domain_id FROM user_domain_access AS uda JOIN roles r ON r.id = uda.role_id WHERE .*`).
					WithArgs(validUser.ID, true).
					WillReturnRows(sqlmock.NewRows([]string{"domain_id"}).AddRow(1))

				// Mock domain names
				mock.ExpectQuery(`SELECT \* FROM "domains" WHERE .*`).
					WithArgs(1).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).AddRow(1, "Finance"))

				// Mock report existence check (none exists)
				mock.ExpectQuery(`SELECT \* FROM "reports" WHERE report_id = \$1 ORDER BY "reports"."id" LIMIT \$2`).
					WithArgs("report-123", 1).
					WillReturnError(gorm.ErrRecordNotFound)

				// Mock user lookup for report creation service
				mock.ExpectQuery(`SELECT \* FROM "users" WHERE email = \$1 ORDER BY "users"."id" LIMIT \$2`).
					WithArgs(userEmail, 1).
					WillReturnRows(sqlmock.NewRows([]string{"id", "email"}).AddRow(validUser.ID, validUser.Email))

				// Mock permissions check for report creation
				mock.ExpectQuery(`SELECT uda.domain_id, r.can_edit, r.is_super_admin FROM user_domain_access AS uda JOIN roles r ON uda.role_id = r.id WHERE uda.user_id = \$1`).
					WithArgs(validUser.ID).
					WillReturnRows(sqlmock.NewRows([]string{"domain_id", "can_edit", "is_super_admin"}).AddRow(1, true, false))

				// Mock domain name lookup for report creation
				mock.ExpectQuery(`SELECT "name" FROM "domains" WHERE id = \$1 ORDER BY "domains"."id" LIMIT \$2`).
					WithArgs(1, 1).
					WillReturnRows(sqlmock.NewRows([]string{"name"}).AddRow("Finance"))

				// Mock report creation transaction
				mock.ExpectBegin()
				mock.ExpectQuery(`INSERT INTO "reports" .*`).
					WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))
				mock.ExpectCommit()
			},
			fileSetup: func(t *testing.T) *multipart.FileHeader {
				tempDir := t.TempDir()
				now := time.Now().UTC()

				return createTestFileHeader(t, tempDir, "test_bulk_upload.xlsx", func(f *excelize.File) {
					// Set headers
					f.SetCellValue("Sheet1", "A1", "URL")
					f.SetCellValue("Sheet1", "B1", "SourceSystem")
					f.SetCellValue("Sheet1", "C1", "Name")
					f.SetCellValue("Sheet1", "D1", "Domain")
					f.SetCellValue("Sheet1", "E1", "Category")
					f.SetCellValue("Sheet1", "F1", "Description")
					f.SetCellValue("Sheet1", "G1", "ReportID")
					f.SetCellValue("Sheet1", "H1", "GroupID")
					f.SetCellValue("Sheet1", "I1", "PublishedOn")

					// Use a Tableau URL to avoid PowerBI token issues
					f.SetCellValue("Sheet1", "A2", "https://tableau.example.com/workbooks/123456/views/TestReport")
					f.SetCellValue("Sheet1", "B2", "tableau")
					f.SetCellValue("Sheet1", "C2", "Test Report")
					f.SetCellValue("Sheet1", "D2", "Finance")
					f.SetCellValue("Sheet1", "E2", "Financial")
					f.SetCellValue("Sheet1", "F2", "Test Description")
					f.SetCellValue("Sheet1", "G2", "report-123")
					f.SetCellValue("Sheet1", "H2", "group-123")
					f.SetCellValue("Sheet1", "I2", now.Format("2006-01-02"))
				})
			},
			wantResult: &BulkCreateReportResult{
				TotalProcessed: 1,
				SuccessCount:   1,
				ErrorCount:     0,
				Reports: []model.ReportResponse{
					{
						ID:           1,
						Name:         "Test Report",
						ReportID:     "report-123",
						DomainName:   "Finance",
						SourceSystem: "tableau",
						Description:  "Test Description",
						Category:     "Financial",
					},
				},
			},
			wantErr: false,
		},
		{
			name: "report update error",
			mockClosure: func(t *testing.T, mock sqlmock.Sqlmock) {
				userEmail := "<EMAIL>"
				validUser := model.User{ID: 1, Email: userEmail}

				mock.ExpectQuery(`SELECT \* FROM "users" WHERE .*`).
					WithArgs(userEmail, 1).
					WillReturnRows(sqlmock.NewRows([]string{"id", "email"}).AddRow(validUser.ID, validUser.Email))

				mock.ExpectQuery(`SELECT uda.domain_id FROM user_domain_access AS uda JOIN roles r ON r.id = uda.role_id WHERE .*`).
					WithArgs(validUser.ID, true).
					WillReturnRows(sqlmock.NewRows([]string{"domain_id"}).AddRow(1))

				mock.ExpectQuery(`SELECT \* FROM "domains" WHERE .*`).
					WithArgs(1).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).AddRow(1, "Finance"))

				// Mock existing report
				mock.ExpectQuery(`SELECT \* FROM "reports" WHERE report_id = \$1 ORDER BY "reports"."id" LIMIT \$2`).
					WithArgs("report-123", 1).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name", "report_id", "domain_id", "category", "description", "source_system", "group_id"}).
						AddRow(1, "Old Report", "report-123", 1, "Financial", "Old Description", "powerbi", "group-123"))

				// Mock update to fail
				mock.ExpectBegin()
				mock.ExpectExec(`UPDATE "reports"`).
					WillReturnError(fmt.Errorf("update error"))
				mock.ExpectRollback()
			},
			fileSetup: func(t *testing.T) *multipart.FileHeader {
				tempDir := t.TempDir()
				return createTestFileHeader(t, tempDir, "test_update_error.xlsx", func(f *excelize.File) {
					f.SetCellValue("Sheet1", "A1", "URL")
					f.SetCellValue("Sheet1", "B1", "SourceSystem")
					f.SetCellValue("Sheet1", "C1", "Name")
					f.SetCellValue("Sheet1", "D1", "Domain")
					f.SetCellValue("Sheet1", "E1", "Category")
					f.SetCellValue("Sheet1", "F1", "Description")
					f.SetCellValue("Sheet1", "G1", "ReportID")
					f.SetCellValue("Sheet1", "H1", "GroupID")

					// Different description to trigger update
					f.SetCellValue("Sheet1", "A2", "https://app.powerbi.com/groups/group-123/reports/report-123")
					f.SetCellValue("Sheet1", "B2", "powerbi")
					f.SetCellValue("Sheet1", "C2", "New Report Name")
					f.SetCellValue("Sheet1", "D2", "Finance")
					f.SetCellValue("Sheet1", "E2", "Financial")
					f.SetCellValue("Sheet1", "F2", "New Description")
					f.SetCellValue("Sheet1", "G2", "report-123")
					f.SetCellValue("Sheet1", "H2", "group-123")
				})
			},
			wantResult: &BulkCreateReportResult{
				TotalProcessed: 1,
				ErrorCount:     1,
				Errors: []BulkUploadError{
					{
						RowNumber: 2,
						ReportID:  "report-123",
						Message:   "Failed to update report: update error",
					},
				},
			},
			wantErr: false,
		},
		{
			name: "invalid date format",
			mockClosure: func(t *testing.T, mock sqlmock.Sqlmock) {
				userEmail := "<EMAIL>"
				validUser := model.User{ID: 1, Email: userEmail}

				mock.ExpectQuery(`SELECT \* FROM "users" WHERE .*`).
					WithArgs(userEmail, 1).
					WillReturnRows(sqlmock.NewRows([]string{"id", "email"}).AddRow(validUser.ID, validUser.Email))

				mock.ExpectQuery(`SELECT uda.domain_id FROM user_domain_access AS uda JOIN roles r ON r.id = uda.role_id WHERE .*`).
					WithArgs(validUser.ID, true).
					WillReturnRows(sqlmock.NewRows([]string{"domain_id"}).AddRow(1))

				mock.ExpectQuery(`SELECT \* FROM "domains" WHERE .*`).
					WithArgs(1).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).AddRow(1, "Finance"))

				// No report existence check needed as date validation fails first
			},
			fileSetup: func(t *testing.T) *multipart.FileHeader {
				tempDir := t.TempDir()
				return createTestFileHeader(t, tempDir, "test_invalid_date.xlsx", func(f *excelize.File) {
					f.SetCellValue("Sheet1", "A1", "URL")
					f.SetCellValue("Sheet1", "B1", "SourceSystem")
					f.SetCellValue("Sheet1", "C1", "Name")
					f.SetCellValue("Sheet1", "D1", "Domain")
					f.SetCellValue("Sheet1", "E1", "Category")
					f.SetCellValue("Sheet1", "F1", "Description")
					f.SetCellValue("Sheet1", "G1", "ReportID")
					f.SetCellValue("Sheet1", "H1", "GroupID")
					f.SetCellValue("Sheet1", "I1", "PublishedOn")

					f.SetCellValue("Sheet1", "A2", "https://example.com")
					f.SetCellValue("Sheet1", "B2", "tableau")
					f.SetCellValue("Sheet1", "C2", "Test Report")
					f.SetCellValue("Sheet1", "D2", "Finance")
					f.SetCellValue("Sheet1", "E2", "Financial")
					f.SetCellValue("Sheet1", "F2", "Test Description")
					f.SetCellValue("Sheet1", "G2", "report-123")
					f.SetCellValue("Sheet1", "H2", "group-123")
					f.SetCellValue("Sheet1", "I2", "2023/01/01") // Invalid format
				})
			},
			wantResult: &BulkCreateReportResult{
				TotalProcessed: 1,
				ErrorCount:     1,
				Errors: []BulkUploadError{
					{
						RowNumber: 2,
						ReportID:  "report-123",
						Message:   "Invalid PublishedOn date format (expected YYYY-MM-DD)",
					},
				},
			},
			wantErr: false,
		},
		{
			name: "power BI URL processing",
			mockClosure: func(t *testing.T, mock sqlmock.Sqlmock) {
				userEmail := "<EMAIL>"
				validUser := model.User{ID: 1, Email: userEmail}

				// Mock user lookup
				mock.ExpectQuery(`SELECT \* FROM "users" WHERE .*`).
					WithArgs(userEmail, 1).
					WillReturnRows(sqlmock.NewRows([]string{"id", "email"}).AddRow(validUser.ID, validUser.Email))

				// Mock accessible domains
				mock.ExpectQuery(`SELECT uda.domain_id FROM user_domain_access AS uda JOIN roles r ON r.id = uda.role_id WHERE .*`).
					WithArgs(validUser.ID, true).
					WillReturnRows(sqlmock.NewRows([]string{"domain_id"}).AddRow(1))

				// Mock domain names
				mock.ExpectQuery(`SELECT \* FROM "domains" WHERE .*`).
					WithArgs(1).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).AddRow(1, "Finance"))

				// Mock report existence check
				mock.ExpectQuery(`SELECT \* FROM "reports" WHERE report_id = \$1 ORDER BY "reports"."id" LIMIT \$2`).
					WithArgs("report-123", 1).
					WillReturnError(gorm.ErrRecordNotFound)

				// PowerBI will fail at token acquisition, so no further DB calls expected
			},
			fileSetup: func(t *testing.T) *multipart.FileHeader {
				tempDir := t.TempDir()

				return createTestFileHeader(t, tempDir, "test_powerbi.xlsx", func(f *excelize.File) {
					// Set headers
					f.SetCellValue("Sheet1", "A1", "URL")
					f.SetCellValue("Sheet1", "B1", "SourceSystem")
					f.SetCellValue("Sheet1", "C1", "Name")
					f.SetCellValue("Sheet1", "D1", "Domain")
					f.SetCellValue("Sheet1", "E1", "Category")
					f.SetCellValue("Sheet1", "F1", "Description")
					f.SetCellValue("Sheet1", "G1", "ReportID")
					f.SetCellValue("Sheet1", "H1", "GroupID")

					// Add Power BI URL
					f.SetCellValue("Sheet1", "A2", "https://app.powerbi.com/groups/group-123/reports/report-123")
					f.SetCellValue("Sheet1", "B2", "powerbi")
					f.SetCellValue("Sheet1", "C2", "Test Report")
					f.SetCellValue("Sheet1", "D2", "Finance")
					f.SetCellValue("Sheet1", "E2", "Financial")
					f.SetCellValue("Sheet1", "F2", "Test Description")
					f.SetCellValue("Sheet1", "G2", "report-123")
					f.SetCellValue("Sheet1", "H2", "group-123")
				})
			},
			wantResult: &BulkCreateReportResult{
				TotalProcessed: 1,
				ErrorCount:     1,
				Errors: []BulkUploadError{
					{
						RowNumber: 2,
						ReportID:  "report-123",
						Message:   "Failed to get Power BI access token",
					},
				},
			},
			wantErr: false,
		},
		{
			name: "user not found",
			mockClosure: func(t *testing.T, mock sqlmock.Sqlmock) {
				userEmail := "<EMAIL>"
				mock.ExpectQuery(`SELECT \* FROM "users" WHERE .*`).
					WithArgs(userEmail, 1).
					WillReturnError(gorm.ErrRecordNotFound)
			},
			fileSetup: func(t *testing.T) *multipart.FileHeader {
				tempDir := t.TempDir()
				return createTestFileHeader(t, tempDir, "test.xlsx", func(f *excelize.File) {
					f.SetCellValue("Sheet1", "A1", "URL")
					f.SetCellValue("Sheet1", "A2", "https://example.com")
				})
			},
			wantErr:       true,
			errorContains: "unauthorized: user not found",
		},
		{
			name: "no domain access",
			mockClosure: func(t *testing.T, mock sqlmock.Sqlmock) {
				userEmail := "<EMAIL>"
				validUser := model.User{ID: 1, Email: userEmail}

				mock.ExpectQuery(`SELECT \* FROM "users" WHERE .*`).
					WithArgs(userEmail, 1).
					WillReturnRows(sqlmock.NewRows([]string{"id", "email"}).AddRow(validUser.ID, validUser.Email))

				mock.ExpectQuery(`SELECT uda.domain_id FROM user_domain_access AS uda JOIN roles r ON r.id = uda.role_id WHERE .*`).
					WithArgs(validUser.ID, true).
					WillReturnRows(sqlmock.NewRows([]string{"domain_id"}))
			},
			fileSetup: func(t *testing.T) *multipart.FileHeader {
				tempDir := t.TempDir()
				return createTestFileHeader(t, tempDir, "test_no_access.xlsx", func(f *excelize.File) {
					f.SetCellValue("Sheet1", "A1", "URL")
					f.SetCellValue("Sheet1", "A2", "https://example.com")
				})
			},
			wantErr:       true,
			errorContains: "you don't have edit permissions for any domains",
		},
		{
			name: "duplicate report IDs",
			mockClosure: func(t *testing.T, mock sqlmock.Sqlmock) {
				userEmail := "<EMAIL>"
				validUser := model.User{ID: 1, Email: userEmail}

				mock.ExpectQuery(`SELECT \* FROM "users" WHERE .*`).
					WithArgs(userEmail, 1).
					WillReturnRows(sqlmock.NewRows([]string{"id", "email"}).AddRow(validUser.ID, validUser.Email))

				mock.ExpectQuery(`SELECT uda.domain_id FROM user_domain_access AS uda JOIN roles r ON r.id = uda.role_id WHERE .*`).
					WithArgs(validUser.ID, true).
					WillReturnRows(sqlmock.NewRows([]string{"domain_id"}).AddRow(1))

				mock.ExpectQuery(`SELECT \* FROM "domains" WHERE .*`).
					WithArgs(1).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).AddRow(1, "Finance"))

				// Mock report existence check for first occurrence - will fail to get PowerBI token
				mock.ExpectQuery(`SELECT \* FROM "reports" WHERE report_id = \$1 ORDER BY "reports"."id" LIMIT \$2`).
					WithArgs("report-123", 1).
					WillReturnError(gorm.ErrRecordNotFound)

				// Second occurrence will be caught by duplicate check - no additional DB calls
			},
			fileSetup: func(t *testing.T) *multipart.FileHeader {
				tempDir := t.TempDir()
				return createTestFileHeader(t, tempDir, "test_duplicates.xlsx", func(f *excelize.File) {
					// Set all required headers to prevent index out of range
					f.SetCellValue("Sheet1", "A1", "URL")
					f.SetCellValue("Sheet1", "B1", "SourceSystem")
					f.SetCellValue("Sheet1", "C1", "Name")
					f.SetCellValue("Sheet1", "D1", "Domain")
					f.SetCellValue("Sheet1", "E1", "Category")
					f.SetCellValue("Sheet1", "F1", "Description")
					f.SetCellValue("Sheet1", "G1", "ReportID")
					f.SetCellValue("Sheet1", "H1", "GroupID")

					// Add two rows with same ReportID
					f.SetCellValue("Sheet1", "A2", "https://app.powerbi.com/groups/group-123/reports/report-123")
					f.SetCellValue("Sheet1", "B2", "powerbi")
					f.SetCellValue("Sheet1", "C2", "Test Report")
					f.SetCellValue("Sheet1", "D2", "Finance")
					f.SetCellValue("Sheet1", "E2", "Financial")
					f.SetCellValue("Sheet1", "F2", "Test Description")
					f.SetCellValue("Sheet1", "G2", "report-123")
					f.SetCellValue("Sheet1", "H2", "group-123")

					f.SetCellValue("Sheet1", "A3", "https://app.powerbi.com/groups/group-123/reports/report-123")
					f.SetCellValue("Sheet1", "B3", "powerbi")
					f.SetCellValue("Sheet1", "C3", "Test Report 2")
					f.SetCellValue("Sheet1", "D3", "Finance")
					f.SetCellValue("Sheet1", "E3", "Financial")
					f.SetCellValue("Sheet1", "F3", "Test Description 2")
					f.SetCellValue("Sheet1", "G3", "report-123")
					f.SetCellValue("Sheet1", "H3", "group-123")
				})
			},
			wantResult: &BulkCreateReportResult{
				TotalProcessed: 2,
				ErrorCount:     2, // Both will fail - first due to PowerBI token, second due to duplicate
				Errors: []BulkUploadError{
					{
						RowNumber: 2,
						ReportID:  "report-123",
						Message:   "Failed to get Power BI access token",
					},
					{
						RowNumber: 3,
						ReportID:  "report-123",
						Message:   "Duplicate Report ID 'report-123' (first seen in row 2)",
					},
				},
			},
			wantErr: false,
		},
		{
			name: "invalid Power BI URL",
			mockClosure: func(t *testing.T, mock sqlmock.Sqlmock) {
				userEmail := "<EMAIL>"
				validUser := model.User{ID: 1, Email: userEmail}

				mock.ExpectQuery(`SELECT \* FROM "users" WHERE .*`).
					WithArgs(userEmail, 1).
					WillReturnRows(sqlmock.NewRows([]string{"id", "email"}).AddRow(validUser.ID, validUser.Email))

				mock.ExpectQuery(`SELECT uda.domain_id FROM user_domain_access AS uda JOIN roles r ON r.id = uda.role_id WHERE .*`).
					WithArgs(validUser.ID, true).
					WillReturnRows(sqlmock.NewRows([]string{"domain_id"}).AddRow(1))

				mock.ExpectQuery(`SELECT \* FROM "domains" WHERE .*`).
					WithArgs(1).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).AddRow(1, "Finance"))

				// No report existence check needed as URL validation fails first
			},
			fileSetup: func(t *testing.T) *multipart.FileHeader {
				tempDir := t.TempDir()
				return createTestFileHeader(t, tempDir, "test_invalid_url.xlsx", func(f *excelize.File) {
					// Set all required headers to prevent index out of range
					f.SetCellValue("Sheet1", "A1", "URL")
					f.SetCellValue("Sheet1", "B1", "SourceSystem")
					f.SetCellValue("Sheet1", "C1", "Name")
					f.SetCellValue("Sheet1", "D1", "Domain")
					f.SetCellValue("Sheet1", "E1", "Category")
					f.SetCellValue("Sheet1", "F1", "Description")
					f.SetCellValue("Sheet1", "G1", "ReportID")
					f.SetCellValue("Sheet1", "H1", "GroupID")

					// Add invalid Power BI URL (personal workspace)
					f.SetCellValue("Sheet1", "A2", "https://app.powerbi.com/groups/me/reports/report-123")
					f.SetCellValue("Sheet1", "B2", "powerbi")
					f.SetCellValue("Sheet1", "C2", "Test Report")
					f.SetCellValue("Sheet1", "D2", "Finance")
					f.SetCellValue("Sheet1", "E2", "Financial")
					f.SetCellValue("Sheet1", "F2", "Test Description")
					f.SetCellValue("Sheet1", "G2", "report-123")
					f.SetCellValue("Sheet1", "H2", "group-123")
				})
			},
			wantResult: &BulkCreateReportResult{
				TotalProcessed: 1,
				ErrorCount:     1,
				Errors: []BulkUploadError{
					{
						RowNumber: 2,
						ReportID:  "report-123",
						Message:   "Personal workspace URLs (with 'me') are not supported",
					},
				},
			},
			wantErr: false,
		},

		{
			name: "unchanged reports",
			mockClosure: func(t *testing.T, mock sqlmock.Sqlmock) {
				userEmail := "<EMAIL>"
				validUser := model.User{ID: 1, Email: userEmail}

				mock.ExpectQuery(`SELECT \* FROM "users" WHERE .*`).
					WithArgs(userEmail, 1).
					WillReturnRows(sqlmock.NewRows([]string{"id", "email"}).AddRow(validUser.ID, validUser.Email))

				mock.ExpectQuery(`SELECT uda.domain_id FROM user_domain_access AS uda JOIN roles r ON r.id = uda.role_id WHERE .*`).
					WithArgs(validUser.ID, true).
					WillReturnRows(sqlmock.NewRows([]string{"domain_id"}).AddRow(1))

				mock.ExpectQuery(`SELECT \* FROM "domains" WHERE .*`).
					WithArgs(1).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).AddRow(1, "Finance"))

				// Mock existing report
				mock.ExpectQuery(`SELECT \* FROM "reports" WHERE report_id = \$1 ORDER BY "reports"."id" LIMIT \$2`).
					WithArgs("report-123", 1).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name", "report_id", "domain_id", "category", "description", "source_system", "group_id"}).
						AddRow(1, "Test Report", "report-123", 1, "Financial", "Test Description", "powerbi", "group-123"))
			},
			fileSetup: func(t *testing.T) *multipart.FileHeader {
				tempDir := t.TempDir()
				return createTestFileHeader(t, tempDir, "test_unchanged.xlsx", func(f *excelize.File) {
					f.SetCellValue("Sheet1", "A1", "URL")
					f.SetCellValue("Sheet1", "B1", "SourceSystem")
					f.SetCellValue("Sheet1", "C1", "Name")
					f.SetCellValue("Sheet1", "D1", "Domain")
					f.SetCellValue("Sheet1", "E1", "Category")
					f.SetCellValue("Sheet1", "F1", "Description")
					f.SetCellValue("Sheet1", "G1", "ReportID")
					f.SetCellValue("Sheet1", "H1", "GroupID")

					// Add row with same data as existing report
					f.SetCellValue("Sheet1", "A2", "https://app.powerbi.com/groups/group-123/reports/report-123")
					f.SetCellValue("Sheet1", "B2", "powerbi")
					f.SetCellValue("Sheet1", "C2", "Test Report")
					f.SetCellValue("Sheet1", "D2", "Finance")
					f.SetCellValue("Sheet1", "E2", "Financial")
					f.SetCellValue("Sheet1", "F2", "Test Description")
					f.SetCellValue("Sheet1", "G2", "report-123")
					f.SetCellValue("Sheet1", "H2", "group-123")
				})
			},
			wantResult: &BulkCreateReportResult{
				TotalProcessed: 1,
				UnchangedCount: 1,
				UnchangedReports: []model.ReportResponse{
					{
						ID:           1,
						Name:         "Test Report",
						ReportID:     "report-123",
						DomainName:   "Finance",
						SourceSystem: "powerbi",
						Description:  "Test Description",
						Category:     "Financial",
					},
				},
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup fresh mock DB for each test
			db, mock := utilsTest.SetupMockDB(t)
			config.DB = db
			defer utilsTest.CloseMockDB(db)

			// Set up environment variables to avoid Azure AD token errors
			t.Setenv("AZURE_CLIENT_ID", "test-client-id")
			t.Setenv("AZURE_CLIENT_SECRET", "test-client-secret")
			t.Setenv("AZURE_TENANT_ID", "test-tenant-id")

			// Common test data
			userEmail := "<EMAIL>"
			createdBy := "1"

			// Setup mock expectations
			tt.mockClosure(t, mock)
			testFile := tt.fileSetup(t)

			result, err := BulkCreateReports(testFile, createdBy, userEmail)

			if tt.wantErr {
				assert.Error(t, err)
				if tt.errorContains != "" {
					assert.Contains(t, err.Error(), tt.errorContains)
				}
			} else {
				assert.NoError(t, err)
				if tt.wantResult != nil {
					assert.Equal(t, tt.wantResult.TotalProcessed, result.TotalProcessed)
					assert.Equal(t, tt.wantResult.SuccessCount, result.SuccessCount)
					assert.Equal(t, tt.wantResult.ErrorCount, result.ErrorCount)
					assert.Equal(t, tt.wantResult.UnchangedCount, result.UnchangedCount)

					if len(tt.wantResult.Reports) > 0 && len(result.Reports) > 0 {
						assert.Equal(t, tt.wantResult.Reports[0].Name, result.Reports[0].Name)
						assert.Equal(t, tt.wantResult.Reports[0].ReportID, result.Reports[0].ReportID)
					}

					if len(tt.wantResult.Errors) > 0 && len(result.Errors) > 0 {
						// Check if any error message contains the expected substring
						found := false
						for _, actualError := range result.Errors {
							for _, expectedError := range tt.wantResult.Errors {
								if strings.Contains(actualError.Message, expectedError.Message) {
									found = true
									break
								}
							}
							if found {
								break
							}
						}
						if !found {
							t.Logf("Expected error containing: %v", tt.wantResult.Errors[0].Message)
							t.Logf("Actual errors: %v", result.Errors)
						}
					}

					if len(tt.wantResult.UnchangedReports) > 0 && len(result.UnchangedReports) > 0 {
						assert.Equal(t, tt.wantResult.UnchangedReports[0].Name, result.UnchangedReports[0].Name)
						assert.Equal(t, tt.wantResult.UnchangedReports[0].ReportID, result.UnchangedReports[0].ReportID)
					}
				}
			}

			// Check that all expectations were met
			if err := mock.ExpectationsWereMet(); err != nil {
				t.Errorf("there were unfulfilled expectations: %s", err)
			}
		})
	}
}
func TestExtractIDsFromPowerBIURL(t *testing.T) {
	tests := []struct {
		name        string
		url         string
		wantReport  string
		wantGroup   string
		wantErr     bool
		errContains string
	}{
		{
			name:       "valid group workspace URL",
			url:        "https://app.powerbi.com/groups/group-123/reports/report-123",
			wantReport: "report-123",
			wantGroup:  "group-123",
			wantErr:    false,
		},
		{
			name:       "valid URL with query params",
			url:        "https://app.powerbi.com?reportId=report-123&groupId=group-123",
			wantReport: "report-123",
			wantGroup:  "group-123",
			wantErr:    false,
		},
		{
			name:        "personal workspace URL",
			url:         "https://app.powerbi.com/groups/me/reports/report-123",
			wantErr:     true,
			errContains: "personal workspace URLs",
		},
		{
			name:        "missing group ID",
			url:         "https://app.powerbi.com/reports/report-123",
			wantErr:     true,
			errContains: "groupID not found",
		},
		{
			name:        "invalid URL",
			url:         "not-a-valid-url",
			wantErr:     true,
			errContains: "reportID not found", // Changed from "invalid URL format"
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			reportID, groupID, err := ExtractIDsFromPowerBIURL(tt.url)

			if tt.wantErr {
				assert.Error(t, err)
				if tt.errContains != "" {
					assert.Contains(t, err.Error(), tt.errContains)
				}
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.wantReport, reportID)
				assert.Equal(t, tt.wantGroup, groupID)
			}
		})
	}
}

func TestExtractLookerDashboardID(t *testing.T) {
	tests := []struct {
		name    string
		url     string
		wantID  string
		wantErr bool
	}{
		{
			name:   "valid URL with path",
			url:    "https://looker.example.com/dashboards/123",
			wantID: "123",
		},
		{
			name:   "valid URL with query param",
			url:    "https://looker.example.com/looks?dashboard=456",
			wantID: "456",
		},
		{
			name:    "invalid URL",
			url:     "not-a-valid-url",
			wantErr: true,
		},
		{
			name:    "missing dashboard ID",
			url:     "https://looker.example.com/other",
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			dashboardID, err := ExtractLookerDashboardID(tt.url)

			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.wantID, dashboardID)
			}
		})
	}
}

func TestIsPowerBIURL(t *testing.T) {
	tests := []struct {
		name string
		url  string
		want bool
	}{
		{"powerbi.com", "https://app.powerbi.com/reports", true},
		{"analysis.windows.net", "https://analysis.windows.net/reports", true},
		{"other domain", "https://example.com", false},
		{"empty string", "", false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equal(t, tt.want, IsPowerBIURL(tt.url))
		})
	}
}

func TestIsLookerURL(t *testing.T) {
	t.Setenv("LOOKER_CUSTOM_DOMAIN", "looker.example.com")

	tests := []struct {
		name string
		url  string
		want bool
	}{
		{"looker.com", "https://looker.com/dashboards", true},
		{"custom domain", "https://looker.example.com/dashboards", true},
		{"other domain", "https://example.com", false},
		{"empty string", "", false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equal(t, tt.want, IsLookerURL(tt.url))
		})
	}
}
