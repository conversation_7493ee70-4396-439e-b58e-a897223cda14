'use client'
import {
    <PERSON><PERSON><PERSON> as ArrowBackIcon,
    AttachFile as AttachFileIcon,
    <PERSON><PERSON><PERSON> as ChartIcon,
    Send as SendIcon,
} from '@mui/icons-material'
import { AppBar, Box, CircularProgress, IconButton, TextField, Toolbar, Typography } from '@mui/material'
import React, { lazy, Suspense, useEffect, useRef, useState } from 'react'
import {
    AUDIT_QA,
    DC_SPECIFIC_QA,
    EMPLOYEE_PERFORMANCE_QA,
    GENERIC_RESPONSES,
    NETWORK_DATA_QA,
    ORDER_TOTE_ERRORS_QA,
} from '../data/omnibotData'
import { fetchTopicContextService, getTopicIdToKeyMap, getTopicKeywords } from '../services'
import { Message, TopicContext } from '../types'
import ChatMessage from './ChatMessage'
import ExplorationQuestions from './ExplorationQuestions'

// Dynamically import chart components
const NetworkDataComboChart = lazy(() => import('./charts/NetworkDataComboChart'))
const DCSpecificTrendsChart = lazy(() => import('./charts/DCSpecificTrendsChart'))
const EmployeePerformanceChart = lazy(() => import('./charts/EmployeePerformanceChart'))

interface ChatInterfaceProps {
    chatTitle: string
    onClose: () => void
}

interface MessageMatchResult {
    responseText: string
    newTopic: string | null
    matchedExplorationQuestion: boolean
}

const MESSAGE_ID_BOT = '1'
const SCROLL_BEHAVIOR: ScrollBehavior = 'smooth'
const MIN_WORD_LENGTH = 3
const MATCH_THRESHOLD_DEFAULT = 3

const ChatInterface: React.FC<ChatInterfaceProps> = ({ chatTitle, onClose }) => {
    const [messages, setMessages] = useState<Message[]>([])
    const [inputValue, setInputValue] = useState('')
    const [currentTopic, setCurrentTopic] = useState<string | null>(null)
    const [showChart, setShowChart] = useState(false)
    const [showExplorationQuestions, setShowExplorationQuestions] = useState(false)
    const [topicContext, setTopicContext] = useState<TopicContext | null>(null)
    const [topicIdMap, setTopicIdMap] = useState<Record<string, string>>({})
    const [topicKeywords, setTopicKeywords] = useState<Record<string, string[]>>({})
    const messagesEndRef = useRef<HTMLDivElement>(null)

    useEffect(() => {
        const loadMessages = async () => {
            try {
                setMessages([
                    {
                        id: MESSAGE_ID_BOT,
                        sender: 'bot',
                        text: "Hello! I'm Omnibot, your AI assistant.",
                        timestamp: new Date(),
                    },
                ])
            } catch (error) {
                // eslint-disable-next-line no-console
                console.error('Error loading messages:', error)
            }
        }

        loadMessages()
    }, [chatTitle])

    useEffect(() => {
        // Fetch topic context on mount
        const initializeTopicData = async () => {
            const context = await fetchTopicContextService()
            setTopicContext(context)

            setTopicIdMap(getTopicIdToKeyMap())
            setTopicKeywords(getTopicKeywords())
        }

        initializeTopicData()
    }, [])

    // Function to render the appropriate chart based on the current topic
    const renderTopicChart = () => {
        if (!currentTopic || !showChart) return null

        // Map topic keys to chart components
        const chartComponents: Record<string, React.ReactNode> = {
            networkData: <NetworkDataComboChart />,
            dcSpecificTrends: <DCSpecificTrendsChart />,
            employeePerformance: <EmployeePerformanceChart />,
        }

        return (
            <Suspense
                fallback={
                    <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
                        <CircularProgress sx={{ color: '#FFC107' }} />
                    </Box>
                }
            >
                {chartComponents[currentTopic] || (
                    <Box sx={{ p: 4, textAlign: 'center' }}>
                        <Typography variant="body1" color="text.secondary">
                            No chart available for this topic.
                        </Typography>
                    </Box>
                )}
            </Suspense>
        )
    }

    // Scroll to bottom of messages when messages change
    useEffect(() => {
        messagesEndRef.current?.scrollIntoView({ behavior: SCROLL_BEHAVIOR })
    }, [messages])

    // Helper: Detect topic from topicId or topicPrompt
    function detectTopic(
        topicId: string | null,
        topicPrompt: string | null,
        topicIdMap: Record<string, string>,
        topicKeywords: Record<string, string[]>,
    ): string | null {
        if (topicId && topicIdMap[topicId]) {
            return topicIdMap[topicId]
        }
        if (topicPrompt) {
            const promptLower = topicPrompt.toLowerCase()
            for (const topicKey of Object.keys(topicKeywords)) {
                const keywords = topicKeywords[topicKey]
                if (keywords.some((keyword) => promptLower.includes(keyword))) {
                    return topicKey
                }
            }
        }
        return null
    }

    // Helper: Create welcome message for a topic
    function createWelcomeMessage(context: TopicContext[string], messageId: string): Message {
        let welcomeText = `# ${context.title}\n\n`
        welcomeText += `${context.context}\n\n`
        if (context.insights && context.insights.length > 0) {
            welcomeText += `## Key Findings\n\n`
            const bulletPoints = context.insights.map((insight: string) => '• ' + insight).join('\n')
            welcomeText += bulletPoints
        }
        return {
            id: messageId,
            sender: 'bot',
            text: welcomeText,
            timestamp: new Date(),
        }
    }

    // Check for pre-filled message from topic
    useEffect(() => {
        if (!topicContext) return

        const topicPrompt = sessionStorage.getItem('topicPrompt')
        const topicId = sessionStorage.getItem('topicId')

        if (!(topicId || topicPrompt)) return

        const detectedTopic = detectTopic(topicId, topicPrompt, topicIdMap, topicKeywords)

        if (detectedTopic) {
            setCurrentTopic(detectedTopic)
            const context = topicContext[detectedTopic as keyof typeof topicContext]
            if (context?.title) {
                setMessages([createWelcomeMessage(context, MESSAGE_ID_BOT)])
                setShowExplorationQuestions(false)
            } else {
                // eslint-disable-next-line no-console
                console.error(`Topic context for "${detectedTopic}" is missing or incomplete`)
                setMessages([
                    {
                        id: MESSAGE_ID_BOT,
                        sender: 'bot',
                        text: "Hello! I'm Omnibot, your AI assistant. How can I help you today?",
                        timestamp: new Date(),
                    },
                ])
            }
        }

        sessionStorage.removeItem('topicPrompt')
        sessionStorage.removeItem('topicId')

        setTimeout(() => {
            const inputElement = document.querySelector('input[placeholder="Ask Omnibot..."]') as HTMLInputElement
            if (inputElement) {
                inputElement.focus()
            }
        }, 300)
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [topicContext, topicIdMap, topicKeywords])

    // Helper: Try to match a QA array
    function matchQA(userMessageLower: string, qaArray: { keywords: string[]; answer: string }[]) {
        for (const qa of qaArray) {
            if (qa.keywords.every((keyword: string) => userMessageLower.includes(keyword))) {
                return qa.answer
            }
        }
        return null
    }

    // Helper: Try to match exploration questions
    function matchExplorationQuestion(
        userMessageLower: string,
        context: TopicContext[string] | undefined,
        minWordLength: number,
        matchThresholdDefault: number,
    ) {
        if (!context?.explorationQuestions) return null
        for (const q of context.explorationQuestions) {
            const questionWords = q.question
                .toLowerCase()
                .split(' ')
                .filter((word) => word.length > minWordLength)
                .filter(
                    (word) =>
                        ![
                            'what',
                            'which',
                            'where',
                            'when',
                            'how',
                            'would',
                            'this',
                            'that',
                            'have',
                            'does',
                            'show',
                        ].includes(word),
                )
            const matchCount = questionWords.filter((word) => userMessageLower.includes(word)).length
            const matchThreshold = Math.min(matchThresholdDefault, Math.floor(questionWords.length / 2))
            if (matchCount >= matchThreshold) {
                return q.answer
            }
        }
        return null
    }

    // Helper: Try to match topic keywords
    function matchTopicKeyword(userMessageLower: string, topicKeywords: Record<string, string[]>): string | null {
        for (const topicKey of Object.keys(topicKeywords)) {
            const keywords = topicKeywords[topicKey]
            if (keywords.some((keyword) => userMessageLower.includes(keyword))) {
                return topicKey
            }
        }
        return null
    }

    // Helper: Get generic response for topic
    function getGenericResponseForTopic(topicKey: string) {
        if (topicKey === 'networkData') return GENERIC_RESPONSES.networkData
        if (topicKey === 'dcSpecificTrends') return GENERIC_RESPONSES.dcSpecificTrends
        if (topicKey === 'employeePerformance') return GENERIC_RESPONSES.employeePerformance
        if (topicKey === 'orderToteErrors') return GENERIC_RESPONSES.orderToteErrors
        return null
    }

    // Helper: Check if user wants a chart
    function userWantsChart(userMessageLower: string) {
        return (
            userMessageLower.includes('chart') ||
            userMessageLower.includes('visual') ||
            userMessageLower.includes('graph') ||
            userMessageLower.includes('data') ||
            userMessageLower.includes('show me') ||
            userMessageLower.includes('yes')
        )
    }

    // NEW: Try to match QA responses in priority order
    const tryMatchQAResponses = (userMessageLower: string): { answer: string; newTopic: string | null } | null => {
        // 1. Try to match audit QA
        const auditAnswer = matchQA(userMessageLower, AUDIT_QA)
        if (auditAnswer) {
            return { answer: auditAnswer, newTopic: null }
        }

        // 2. Try to match DC QA
        const dcAnswer = matchQA(userMessageLower, DC_SPECIFIC_QA)
        if (dcAnswer) {
            return { answer: dcAnswer, newTopic: 'dcSpecificTrends' }
        }

        // 3. Try to match employee performance QA
        const empAnswer = matchQA(userMessageLower, EMPLOYEE_PERFORMANCE_QA)
        if (empAnswer) {
            return { answer: empAnswer, newTopic: 'employeePerformance' }
        }

        // 4. Try to match order/tote errors QA
        const orderAnswer = matchQA(userMessageLower, ORDER_TOTE_ERRORS_QA)
        if (orderAnswer) {
            return { answer: orderAnswer, newTopic: 'orderToteErrors' }
        }

        // 6. Try to match network data QA
        const networkAnswer = matchQA(userMessageLower, NETWORK_DATA_QA)
        if (networkAnswer) {
            return { answer: networkAnswer, newTopic: 'networkData' }
        }

        return null
    }

    // NEW: Handle chart requests
    const handleChartRequest = (userMessageLower: string): string | null => {
        if (!userWantsChart(userMessageLower)) return null

        if (currentTopic && topicContext) {
            const context = topicContext[currentTopic as keyof typeof topicContext]
            if (context?.title) {
                setShowChart(true)
                return GENERIC_RESPONSES.chartAvailable(context.title)
            } else {
                return GENERIC_RESPONSES.chartUnavailable
            }
        } else {
            return GENERIC_RESPONSES.chartRequest
        }
    }

    // NEW: Try topic keyword matching for generic responses
    const tryTopicKeywordMatch = (userMessageLower: string): { response: string; topic: string } | null => {
        const topicKey = matchTopicKeyword(userMessageLower, topicKeywords)
        if (!topicKey) return null

        const genericResponse = getGenericResponseForTopic(topicKey)
        if (genericResponse) {
            return { response: genericResponse, topic: topicKey }
        }
        return null
    }

    // NEW: Process user message and determine response
    const processUserMessage = (userMessageLower: string): MessageMatchResult => {
        let responseText = ''
        let newTopic: string | null = null
        let matchedExplorationQuestion = false

        // Try QA responses first
        const qaMatch = tryMatchQAResponses(userMessageLower)
        if (qaMatch) {
            responseText = qaMatch.answer
            newTopic = qaMatch.newTopic
            matchedExplorationQuestion = true
        }

        // Try exploration questions if no QA match
        if (!matchedExplorationQuestion && currentTopic && topicContext) {
            const context = topicContext[currentTopic as keyof typeof topicContext]
            const explorationAnswer = matchExplorationQuestion(
                userMessageLower,
                context,
                MIN_WORD_LENGTH,
                MATCH_THRESHOLD_DEFAULT,
            )
            if (explorationAnswer) {
                responseText = explorationAnswer
                matchedExplorationQuestion = true
            }
        }

        // Try topic keyword matching if no exploration question match
        if (!matchedExplorationQuestion && !newTopic) {
            const topicMatch = tryTopicKeywordMatch(userMessageLower)
            if (topicMatch) {
                newTopic = topicMatch.topic
                responseText = topicMatch.response
            }
        }

        // Handle chart requests
        if (!newTopic && !matchedExplorationQuestion) {
            const chartResponse = handleChartRequest(userMessageLower)
            if (chartResponse) {
                responseText = chartResponse
            }
        }

        // Fallback response
        if (!matchedExplorationQuestion && !newTopic && !responseText) {
            const queryPreview = userMessageLower.split(' ').slice(0, 3).join(' ')
            responseText = GENERIC_RESPONSES.fallback(queryPreview)
        }

        return {
            responseText,
            newTopic,
            matchedExplorationQuestion,
        }
    }

    // NEW: Handle bot response creation and topic updates
    const handleBotResponse = (result: MessageMatchResult, previousTopic: string | null) => {
        const { responseText, newTopic } = result

        if (newTopic) {
            setCurrentTopic(newTopic)
        }

        const botMessage: Message = {
            id: (Date.now() + 1).toString(),
            sender: 'bot',
            text: responseText,
            timestamp: new Date(),
        }
        setMessages((prev) => [...prev, botMessage])

        if (newTopic && newTopic !== previousTopic) {
            setTimeout(() => {
                addTopicMessages(newTopic)
            }, 1000)
        }
    }

    const handleSendMessage = () => {
        if (!inputValue.trim()) return

        const userMessage: Message = {
            id: Date.now().toString(),
            sender: 'user',
            text: inputValue,
            timestamp: new Date(),
        }

        setMessages((prev) => [...prev, userMessage])
        setInputValue('')
        setShowExplorationQuestions(false)

        const userMessageLower = userMessage.text.toLowerCase()
        const detectedTopic = matchTopicKeyword(userMessageLower, topicKeywords)

        if (detectedTopic) {
            setCurrentTopic(detectedTopic)
        }

        setTimeout(() => {
            const previousTopic = currentTopic
            const result = processUserMessage(userMessageLower)
            handleBotResponse(result, previousTopic)

            // eslint-disable-next-line no-console
            // console.log(`Message sent in chat "${chatTitle}": ${userMessage.text}`)
        }, 1000)
    }

    const handleKeyDown = (e: React.KeyboardEvent) => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault()
            handleSendMessage()
        }
    }

    const handleExplorationQuestionClick = (question: string) => {
        const userMessage: Message = {
            id: Date.now().toString(),
            sender: 'user',
            text: question,
            timestamp: new Date(),
        }

        setMessages((prev) => [...prev, userMessage])

        if (currentTopic && topicContext) {
            const context = topicContext[currentTopic as keyof typeof topicContext]

            if (context?.explorationQuestions) {
                const questionObj = context.explorationQuestions.find((q) => q.question === question)

                if (questionObj) {
                    setTimeout(() => {
                        const botMessage: Message = {
                            id: (Date.now() + 1).toString(),
                            sender: 'bot',
                            text: questionObj.answer,
                            timestamp: new Date(),
                        }

                        setMessages((prev) => [...prev, botMessage])
                    }, 500)
                }
            }
        }

        setShowExplorationQuestions(false)
    }

    const addTopicMessages = (topic: string | null) => {
        if (!topic || !topicContext) return

        const context = topicContext[topic as keyof typeof topicContext]

        if (context?.title) {
            let introText = `# ${context.title}\n\n`

            introText += `${context.context}\n\n`

            if (context.insights && context.insights.length > 0) {
                introText += `## Key Findings\n\n`
                const bulletPoints = context.insights.map((insight: string) => '• ' + insight).join('\n')
                introText += bulletPoints
            }

            const introMessage: Message = {
                id: (Date.now() + 1).toString(),
                sender: 'bot',
                text: introText,
                timestamp: new Date(),
            }

            setMessages((prev) => [...prev, introMessage])
            setShowExplorationQuestions(false)
        } else {
            // eslint-disable-next-line no-console
            console.error(`Topic context for "${topic}" is missing or incomplete`)

            const fallbackMessage: Message = {
                id: (Date.now() + 1).toString(),
                sender: 'bot',
                text: GENERIC_RESPONSES.missingContext,
                timestamp: new Date(),
            }

            setMessages((prev) => [...prev, fallbackMessage])
        }
    }

    return (
        <Box
            className="chat-interface"
            sx={{
                display: 'flex',
                flexDirection: 'column',
                height: '100%',
                width: '950px',
                margin: '0 auto',
                overflow: 'hidden',
                gap: '16px',
                p: 0,
                pb: 2,
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                maxWidth: 'calc(100% - 20px)',
            }}
        >
            {/* Chat Header */}
            <AppBar
                position="static"
                elevation={0}
                sx={{
                    backgroundColor: 'white',
                    color: 'black',
                    borderBottom: '1px solid',
                    borderColor: 'divider',
                }}
            >
                <Toolbar sx={{ minHeight: '64px' }}>
                    <IconButton edge="start" color="inherit" onClick={onClose} sx={{ mr: 2 }}>
                        <ArrowBackIcon />
                    </IconButton>
                    <Typography variant="h6" component="div" sx={{ flexGrow: 1, fontWeight: 'bold' }}>
                        {chatTitle}
                    </Typography>
                    {currentTopic && (
                        <IconButton
                            color="inherit"
                            onClick={() => setShowChart(!showChart)}
                            sx={{
                                color: showChart ? '#FFC107' : 'text.secondary',
                                '&:hover': {
                                    backgroundColor: 'rgba(255, 193, 7, 0.1)',
                                },
                            }}
                        >
                            <ChartIcon />
                        </IconButton>
                    )}
                </Toolbar>
            </AppBar>

            {/* Messages Area */}
            <Box
                sx={{
                    flexGrow: 1,
                    overflowY: 'auto',
                    height: 'calc(100vh - 240px)',
                    display: 'flex',
                    flexDirection: 'column',
                    gap: 2,
                    p: 3,
                    pb: 2,
                    pr: 4,
                    '&::-webkit-scrollbar': {
                        width: '8px',
                    },
                    '&::-webkit-scrollbar-track': {
                        background: '#f1f1f1',
                        borderRadius: '10px',
                    },
                    '&::-webkit-scrollbar-thumb': {
                        background: '#FFC10780',
                        borderRadius: '10px',
                    },
                    '&::-webkit-scrollbar-thumb:hover': {
                        background: '#FFC107',
                    },
                }}
            >
                {messages.map((message) => (
                    <ChatMessage key={message.id} message={message} />
                ))}
                {showChart && renderTopicChart()}
                {showExplorationQuestions &&
                    topicContext?.[currentTopic as keyof typeof topicContext]?.explorationQuestions && (
                        <ExplorationQuestions
                            questions={
                                topicContext[currentTopic as keyof typeof topicContext].explorationQuestions || []
                            }
                            onQuestionClick={handleExplorationQuestionClick}
                        />
                    )}
                <div ref={messagesEndRef} />
            </Box>

            {/* Input Area */}
            <Box
                sx={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px',
                    alignSelf: 'stretch',
                    padding: '8px 12px',
                    border: '1px solid',
                    borderColor: 'divider',
                    borderRadius: '32px',
                    backgroundColor: 'white',
                    boxShadow: '0 2px 6px rgba(0,0,0,0.05)',
                    transition: 'all 0.2s ease',
                    maxWidth: '800px',
                    margin: '0 auto',
                    width: '100%',
                    '&:focus-within': {
                        boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
                        borderColor: '#FFC107',
                    },
                }}
            >
                <IconButton size="small" sx={{ color: 'text.secondary', ml: 0.5 }}>
                    <AttachFileIcon fontSize="small" />
                </IconButton>

                <TextField
                    fullWidth
                    placeholder="Ask Omnibot..."
                    value={inputValue}
                    onChange={(e) => setInputValue(e.target.value)}
                    onKeyDown={handleKeyDown}
                    variant="standard"
                    sx={{
                        '& .MuiInput-underline:before': { borderBottomStyle: 'none' },
                        '& .MuiInputBase-root': {
                            fontSize: '0.95rem',
                            padding: '8px 0',
                        },
                    }}
                    slotProps={{
                        input: {
                            disableUnderline: true,
                        },
                    }}
                />

                <IconButton
                    onClick={handleSendMessage}
                    disabled={!inputValue.trim()}
                    sx={{
                        backgroundColor: inputValue.trim() ? '#FFC107' : '#e0e0e0',
                        color: inputValue.trim() ? 'black' : 'white',
                        transition: 'all 0.2s ease',
                        '&:hover': {
                            backgroundColor: inputValue.trim() ? '#FFB000' : '#e0e0e0',
                            transform: inputValue.trim() ? 'scale(1.05)' : 'none',
                        },
                        width: 36,
                        height: 36,
                        mr: 0.5,
                    }}
                >
                    <SendIcon fontSize="small" />
                </IconButton>
            </Box>
        </Box>
    )
}

export default ChatInterface
