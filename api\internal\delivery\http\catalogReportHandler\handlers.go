package catalogReportHandler

import (
	"bytes"
	"dng-module/config"

	// "dng-module/internal/app/catalogReportService"
	"dng-module/internal/app/catalogReportService"
	"dng-module/internal/infra/db"
	"dng-module/internal/model"
	"encoding/json"

	// "dng-module/internal/utils"
	"errors"
	"fmt"
	"net/http"
	"os"
	"path/filepath"
	"strings"

	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v4"
)

const (
	errMissingAuthClaims = "Missing authentication claims"
	errInvalidClaims     = "Invalid claims structure"
	errUPNNotFound       = "UPN not found in token"
)

// CreateReportHandler - API for creating a report
// CreateReportHandler godoc
// @Summary Create or update a report
// @Description Create a new report or update an existing one
// @Tags reports
// @Accept json
// @Produce json
// @Param report body model.CreateReportDto true "Report data to create or update"
// @Success 201 {object} map[string]interface{} "Successfully created report"
// @Failure 400 {object} map[string]string "Invalid input"
// @Router /dng/api/reports/upsert-report [post]
func CreateReportHandler(c *gin.Context) {
	// First, read the raw JSON to filter out problematic values
	var rawData map[string]interface{}
	if err := c.ShouldBindJSON(&rawData); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"status":  "error",
			"message": "Invalid JSON data",
		})
		return
	}

	// Clean up problematic fields
	// If "id" is an empty string, remove it completely
	if idVal, exists := rawData["id"]; exists {
		if idStr, ok := idVal.(string); ok && idStr == "" {
			delete(rawData, "id")
		}
	}

	// Remove created_at and updated_at if they exist
	delete(rawData, "created_at")
	delete(rawData, "updated_at")

	// Convert the cleaned map back to JSON
	cleanJSON, err := json.Marshal(rawData)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"status":  "error",
			"message": "Error processing request",
		})
		return
	}

	// Now bind the cleaned JSON to our DTO
	var report model.CreateReportDto
	if err := json.Unmarshal(cleanJSON, &report); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"status":  "error",
			"message": "Invalid JSON structure",
		})
		return
	}

	// Extract user email from JWT
	claimsRaw, exists := c.Get("claims")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": errMissingAuthClaims})
		return
	}

	claims, ok := claimsRaw.(jwt.MapClaims)
	if !ok {
		c.JSON(http.StatusUnauthorized, gin.H{"error": errInvalidClaims})
		return
	}

	upnVal, ok := claims["upn"]
	if !ok {
		c.JSON(http.StatusUnauthorized, gin.H{"error": errUPNNotFound})
		return
	}

	userEmail, ok := upnVal.(string)
	if !ok {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "UPN in token is not a valid string"})
		return
	}

	createdReport, err := catalogReportService.CreateReport(report, userEmail)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"status":  "error",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"status":  "success",
		"message": "Report created successfully",
		"data":    createdReport,
	})
}

// @Summary Retrieve catalog reports
// @Description Fetch catalog reports based on query parameters and user context with pagination support
// @Tags reports
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(10)
// @Param search query string false "Search term"
// @Param sort_by query string false "Sort field" default(created_at)
// @Param sort_order query string false "Sort order" Enums(asc, desc) default(desc)
// @Param request body model.UserRequest true "User context for report retrieval"
// @Success 200 {object} model.PaginatedReportsResponse "Successfully retrieved reports"
// @Failure 400 {object} model.ErrorResponse "Invalid query parameters"
// @Failure 401 {object} model.ErrorResponse "Unauthorized"
// @Failure 500 {object} model.ErrorResponse "Internal server error"
// @Router /dng/api/reports [post]
func GetReportsHandler(c *gin.Context) {

	// Bind query parameters
	var request model.GetReportsQueryParams
	var req model.UserRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid JSON input", "message": err.Error()})
		return
	}

	// Bind query parameters
	if err := c.ShouldBindQuery(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"status": "error", "message": err.Error()})
		return
	}

	// Call service
	reports, err := catalogReportService.GetReports(db.DB, request, req.UserID, req.RoleName)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"status": "error", "message": err.Error()})
		return
	}

	// Send response
	c.JSON(http.StatusOK, reports)
}

// @Summary Retrieve search options
// @Description Get available search options and filters based on user role and permissions
// @Tags reports
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body model.GetSearchOptionsRequest true "Search options request including user context"
// @Success 200 {object} model.ReportSearchOption "Successfully retrieved search options"
// @Failure 400 {object} model.ErrorResponse "Invalid request"
// @Failure 401 {object} model.ErrorResponse "Unauthorized"
// @Failure 500 {object} model.ErrorResponse "Internal server error"
// @Router /dng/api/reports/search [post]
func GetSearchOptionsHandler(c *gin.Context) {
	var req model.GetSearchOptionsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "message": err.Error()})
		return
	}

	// Optionally: Verify the user and roles exist.
	// For now, we directly proceed to fetch search options.

	options, err := catalogReportService.GetSearchOptions(db.DB, req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch search options", "message": err.Error()})
		return
	}

	c.JSON(http.StatusOK, options)
}

// @Summary Update report favorite status
// @Description Update the favorite/saved/view status of a report for a user
// @Tags user-preferences
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body model.FavoriteReportRequest true "Favorite report update request"
// @Success 200 {object} model.ReportWithPreference "Successfully updated preference"
// @Failure 400 {object} model.ErrorResponse "Invalid request"
// @Failure 401 {object} model.ErrorResponse "Unauthorized"
// @Failure 404 {object} model.ErrorResponse "Preference not found"
// @Failure 500 {object} model.ErrorResponse "Internal server error"
// @Router /dng/api/reports/user-preferences [post]
func UpdateIsFavorite(c *gin.Context) {
	var req model.FavoriteReportRequest

	// Bind and validate request body
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request body",
			"message": err.Error(), // This will give more context on the validation error
		})
		return
	}

	// Call service to update preference
	updateFavorite, err := catalogReportService.UpdatePreference(db.DB, req.UserID, req.ReportID, req.IsFavorite, req.IsSaved, req.View)
	if err != nil {
		if err.Error() == "preference not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		}
		return
	}

	// Send response
	c.JSON(http.StatusOK, updateFavorite)
}

// @Summary Bulk create reports from Excel
// @Description Upload an Excel file to create multiple reports with validation
// @Tags reports
// @Accept multipart/form-data
// @Produce json
// @Security BearerAuth
// @Param file formData file true "Excel file to upload (must be .xlsx or .xls)"
// @Param created_by formData string false "User who created the reports"
// @Success 200 {object} model.ReportResponse "All records successfully processed"
// @Success 206 {object} model.ReportResponse "Partial success - some records processed"
// @Success 304 {object} model.ReportResponse "No records modified - all unchanged"
// @Failure 400 {object} model.ErrorResponse "Invalid file or processing error"
// @Failure 401 {object} model.ErrorResponse "Unauthorized"
// @Failure 500 {object} model.ErrorResponse "Internal server error"
// @Router /dng/api/reports/bulk-insert [post]
func BulkCreateReportsHandler(c *gin.Context, cfg config.Config) {
	// Get the file from the request
	file, err := c.FormFile("file")
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"status":  "error",
			"message": "Invalid or missing file",
		})
		return
	}

	// Validate file type (must be Excel)
	extension := strings.ToLower(filepath.Ext(file.Filename))
	if extension != ".xlsx" && extension != ".xls" {
		c.JSON(http.StatusBadRequest, gin.H{
			"status":  "error",
			"message": "Only Excel files (.xlsx or .xls) are allowed",
		})
		return
	}

	// Extract createdBy from form data (optional)
	createdBy := c.PostForm("created_by")
	if createdBy == "" {
		createdBy = "system"
	}

	// Extract authenticated user's email (UPN) from JWT
	claimsRaw, exists := c.Get("claims")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": errMissingAuthClaims})
		return
	}

	claims, ok := claimsRaw.(jwt.MapClaims)
	if !ok {
		c.JSON(http.StatusUnauthorized, gin.H{"error": errInvalidClaims})
		return
	}

	upnVal, ok := claims["upn"]
	if !ok {
		c.JSON(http.StatusUnauthorized, gin.H{"error": errUPNNotFound})
		return
	}

	userEmail, ok := upnVal.(string)
	if !ok {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "UPN is not a valid string"})
		return
	}

	// Process the file with access enforcement
	result, err := catalogReportService.BulkCreateReports(cfg, file, createdBy, userEmail)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"status":  "error",
			"message": err.Error(),
		})
		return
	}

	// Count summaries
	successCount := result.SuccessCount
	errorCount := result.ErrorCount
	unchangedCount := result.UnchangedCount
	totalProcessed := result.TotalProcessed

	// Dynamic responses based on result
	switch {
	case successCount == 0 && errorCount > 0:
		c.JSON(http.StatusBadRequest, gin.H{
			"status":  "error",
			"message": fmt.Sprintf("Failed to process any records. %d errors occurred.", errorCount),
			"data":    result,
		})
	case successCount == 0 && unchangedCount == totalProcessed:
		c.JSON(http.StatusNotModified, gin.H{
			"status":  "not_modified",
			"message": "All records already exist and are unchanged",
			"data":    result,
		})
	case errorCount > 0 || (successCount > 0 && successCount < totalProcessed):
		c.JSON(http.StatusPartialContent, gin.H{
			"status": "partial_success",
			"message": fmt.Sprintf("Processed with partial success: %d inserted/updated, %d unchanged, %d failed",
				successCount, unchangedCount, errorCount),
			"data": result,
		})
	default:
		c.JSON(http.StatusOK, gin.H{
			"status":  "success",
			"message": "Excel file processed successfully: all records were updated/inserted",
			"data":    result,
		})
	}
}

// DownloadExcelTemplateHandler godoc
// @Summary Download Excel Template
// @Description Provides an Excel template with proper formatting for bulk report upload
// @Tags reports
// @Produce application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
// @Security BearerAuth
// @Success 200 {file} file "Excel template downloaded successfully"
// @Header 200 {string} Content-Disposition "attachment; filename=report_template.xlsx"
// @Header 200 {string} Content-Type "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
// @Failure 401 {object} model.ErrorResponse "Unauthorized"
// @Failure 500 {object} model.ErrorResponse "Internal server error"
// @Router /dng/api/reports/download-template [get]
func DownloadExcelTemplateHandler(c *gin.Context) {
	err := catalogReportService.GenerateExcelTemplate(c.Writer)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"status":  "error",
			"message": err.Error(),
		})
		return
	}
}

// GetPowerBIEmbedToken godoc
// @Summary Retrieve Power BI Embed Token
// @Description Get an embed token for a specific Power BI report with access validation
// @Tags powerbi
// @Produce json
// @Param report_id query string true "Power BI Report ID"
// @Param group_id query string true "Power BI Group ID (use 'me' for personal workspace)"
// @Security BearerAuth
// @Success 200 {object} model.EmbedTokenResponse "Successfully retrieved embed token"
// @Failure 400 {object} model.ErrorResponse "Missing required parameters"
// @Failure 401 {object} model.ErrorResponse "Unauthorized"
// @Failure 500 {object} model.ErrorResponse "Failed to retrieve embed token"
// @Router /dng/api/reports/pbi/embed-token [get]
func GetPowerBIEmbedToken(c *gin.Context, cfg config.Config) {
	reportID := c.Query("report_id")
	groupID := c.Query("group_id")

	authHeader := c.GetHeader("Authorization")
	if groupID == "" || reportID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"status":  "error",
			"message": "Missing required parameters: groupId and reportId",
		})
		return
	}
	token, err := GetTokenByWorkspace(cfg, groupID, authHeader)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to fetch access token",
			"message": err.Error(),
		})
		return
	}

	embedToken, err := catalogReportService.GetEmbedToken(groupID, reportID, token)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to fetch embed token",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"status":  "success",
		"message": "Successfully retrieved Power BI embed token",
		"data":    embedToken,
	})
}

// LookerEmbedHandler godoc
// @Summary Retrieve Looker Embed URL
// @Description Get a secure embed URL for a specific Looker dashboard with SSO
// @Tags looker
// @Produce json
// @Param dashboard_id query string true "Looker Dashboard ID"
// @Security BearerAuth
// @Success 200 {object} model.LookerEmbedResponse "Successfully retrieved Looker embed URL"
// @Failure 400 {object} model.ErrorResponse "Missing dashboard ID"
// @Failure 401 {object} model.ErrorResponse "Unauthorized"
// @Failure 500 {object} model.ErrorResponse "Failed to retrieve embed URL"
// @Router /dng/api/reports/looker/looker-embed [get]
func LookerEmbedHandler(c *gin.Context, cfg config.Config) {
	dashboardID := c.Query("dashboard_id")

	if dashboardID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"status":  "error",
			"message": "Missing 'dashboard_id' in query parameters",
		})
		return
	}

	embedURL, err := catalogReportService.GetLookerEmbedURL(dashboardID, cfg)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"status":  "error",
			"message": "Failed to retrieve Looker embed URL",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"status":  "success",
		"message": "Successfully retrieved Looker embed URL",
		"data": gin.H{
			"url": embedURL,
		},
	})
}

// LookerPDFHandler godoc
// @Summary Generate Looker Dashboard PDF
// @Description Create and download a PDF of a Looker dashboard with specified filters
// @Tags looker
// @Accept json
// @Produce application/pdf
// @Param request body model.DashboardRequest true "Dashboard PDF Generation Request"
// @Security BearerAuth
// @Success 200 {file} file "PDF file of the dashboard"
// @Header 200 {string} Content-Disposition "attachment; filename=dashboard_{id}.pdf"
// @Header 200 {string} Content-Type "application/pdf"
// @Failure 400 {object} model.ErrorResponse "Invalid request"
// @Failure 401 {object} model.ErrorResponse "Unauthorized"
// @Failure 500 {object} model.ErrorResponse "Failed to generate PDF"
// @Router /dng/api/reports/looker/render-pdf [post]
func LookerPDFHandler(c *gin.Context, cfg config.Config) {
	var req model.DashboardRequest
	if err := c.ShouldBindJSON(&req); err != nil || req.DashboardID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "dashboard_id is required"})
		return
	}

	accessToken, err := catalogReportService.GetLookerAccessToken(cfg)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	taskID, err := catalogReportService.CreateRenderTask(req.DashboardID, accessToken, cfg)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	if err := catalogReportService.CheckRenderStatus(taskID, accessToken, cfg); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	pdfData, err := catalogReportService.DownloadPDF(taskID, accessToken, cfg)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.DataFromReader(http.StatusOK, int64(len(pdfData)), "application/pdf",
		bytes.NewReader(pdfData), map[string]string{
			"Content-Disposition": `attachment; filename="dashboard_` + req.DashboardID + `.pdf"`,
		})
}

func GetFilterOptionsHandler(c *gin.Context) {
	claimsRaw, exists := c.Get("claims")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": errMissingAuthClaims})
		return
	}

	claims, ok := claimsRaw.(jwt.MapClaims)
	if !ok {
		c.JSON(http.StatusUnauthorized, gin.H{"error": errInvalidClaims})
		return
	}

	upnVal, ok := claims["upn"]
	if !ok {
		c.JSON(http.StatusUnauthorized, gin.H{"error": errUPNNotFound})
		return
	}

	email, ok := upnVal.(string)
	if !ok {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "UPN in token is not a valid string"})
		return
	}

	filterData, err := catalogReportService.GetFilterOptions(db.DB, email)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch filter data"})
		return
	}

	c.JSON(http.StatusOK, filterData)
}

// DownloadErrorFileHandler godoc
// @Summary Download error file
// @Description Download an error Excel file containing validation errors from bulk upload
// @Tags reports
// @Produce application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
// @Param filePath query string true "Temporary file path of the error file"
// @Security BearerAuth
// @Success 200 {file} file "Excel file with error details"
// @Header 200 {string} Content-Disposition "attachment; filename=bulk_upload_errors.xlsx"
// @Header 200 {string} Content-Type "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
// @Failure 400 {object} model.ErrorResponse "Invalid file path"
// @Failure 401 {object} model.ErrorResponse "Unauthorized"
// @Failure 404 {object} model.ErrorResponse "File not found"
// @Router /dng/api/reports/bulk-insert-errors [get]
func DownloadErrorFileHandler(c *gin.Context) {
	filePath := c.Query("filePath")
	if filePath == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "File path is required"})
		return
	}

	// Ensure the file is within a safe temp directory
	if !filepath.IsAbs(filePath) || !strings.HasPrefix(filePath, os.TempDir()) {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid file path"})
		return
	}

	// Check if the file exists
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		c.JSON(http.StatusNotFound, gin.H{"error": "File not found"})
		return
	}

	// Get filename from path
	filename := filepath.Base(filePath)

	// Set headers for file download
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=\"%s\"", filename))
	c.Header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")

	// Serve file
	c.File(filePath)
}

// @Summary Delete multiple reports
// @Description Delete reports by their IDs (admin or users with edit permission on the report's domain)
// @Tags reports
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body object true "Delete reports request" Example: {"user_id": 123, "report_ids": [1,2,3]}
// @Success 200 {object} model.SuccessResponse "Reports deleted successfully"
// @Failure 400 {object} model.ErrorResponse "Invalid request format or no report IDs provided"
// @Failure 403 {object} model.ErrorResponse "Permission denied for one or more reports"
// @Failure 404 {object} model.ErrorResponse "No matching reports found"
// @Failure 500 {object} model.ErrorResponse "Internal server error"
// @Router /dng/api/reports [delete]
func DeleteReportsHandler(c *gin.Context) {
	var requestBody struct {
		UserID    int   `json:"user_id"`    // Changed from string to int
		ReportIDs []int `json:"report_ids"` // Changed from []string to []int
	}

	if err := c.ShouldBindJSON(&requestBody); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format"})
		return
	}

	if len(requestBody.ReportIDs) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "No report IDs provided"})
		return
	}

	err := catalogReportService.DeleteReports(requestBody.UserID, requestBody.ReportIDs)
	if err != nil {
		c.JSON(http.StatusForbidden, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Reports deleted successfully"})
}

// GetPowerBIMetaHandler godoc
// @Summary Get Power BI metadata
// @Description Retrieve metadata for a Power BI report including datasets and parameters
// @Tags powerbi
// @Produce json
// @Param report_id query string true "Power BI Report ID"
// @Param group_id query string true "Power BI Group ID (use 'me' for personal workspace)"
// @Security BearerAuth
// @Success 200 {object} model.PowerBIReportMetaData "Successfully retrieved metadata"
// @Failure 400 {object} model.ErrorResponse "Missing required parameters"
// @Failure 401 {object} model.ErrorResponse "Unauthorized"
// @Failure 422 {object} model.ErrorResponse "Invalid report ID or group ID"
// @Failure 500 {object} model.ErrorResponse "Failed to fetch metadata"
// @Router /dng/api/reports/pbi/metadata [get]
func GetPowerBIMetaHandler(c *gin.Context, cfg config.Config) {
	reportID := c.Query("report_id")
	groupID := c.Query("group_id")
	authHeader := c.GetHeader("Authorization")

	if groupID == "" || reportID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"status":  "error",
			"message": "Missing required parameters: groupId and reportId",
		})
		return
	}

	token, err := GetTokenByWorkspace(cfg, groupID, authHeader)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"status":  "error",
			"message": "Failed to fetch access token",
			"details": err.Error(),
		})
		return
	}

	data, err := catalogReportService.GetPowerBIMetaData(groupID, reportID, token)
	if err != nil {
		if apiErr, ok := err.(*catalogReportService.PowerBIAPIError); ok {
			switch apiErr.StatusCode {
			case http.StatusUnauthorized:
				c.JSON(http.StatusUnauthorized, gin.H{
					"status":  "error",
					"message": "Unauthorized to access Power BI resource",
					"details": apiErr.Message,
				})
				return
			case http.StatusNotFound, http.StatusBadRequest:
				c.JSON(http.StatusUnprocessableEntity, gin.H{
					"status":  "error",
					"message": "Invalid report ID or group ID",
					"details": apiErr.Message,
				})
				return
			default:
				c.JSON(http.StatusInternalServerError, gin.H{
					"status":  "error",
					"message": "Unexpected error from Power BI API",
					"details": apiErr.Message,
				})
				return
			}
		}

		// fallback for unexpected error types
		c.JSON(http.StatusInternalServerError, gin.H{
			"status":  "error",
			"message": "Failed to fetch Power BI metadata",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"status":  "success",
		"message": "Successfully retrieved Power BI meta data",
		"data":    data,
	})
}

// GetLookerMetaHandler godoc
// @Summary Get Looker metadata
// @Description Retrieve metadata for a Looker dashboard including fields and filters
// @Tags looker
// @Produce json
// @Param dashboard_id query string true "Looker Dashboard ID"
// @Security BearerAuth
// @Success 200 {object} model.LookerDashboardMetadata "Successfully retrieved metadata"
// @Failure 400 {object} model.ErrorResponse "Missing dashboard ID"
// @Failure 401 {object} model.ErrorResponse "Unauthorized"
// @Failure 422 {object} model.ErrorResponse "Invalid dashboard ID"
// @Failure 500 {object} model.ErrorResponse "Failed to fetch metadata"
// @Router /dng/api/reports/looker/metadata [get]
func GetLookerMetaHandler(c *gin.Context, cfg config.Config) {
	dashboardID := c.Query("dashboard_id")

	if dashboardID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"status":  "error",
			"message": "Missing 'dashboard_id' in query parameters",
		})
		return
	}

	metaData, err := catalogReportService.GetLookerDashMetadata(dashboardID, cfg)
	if err != nil {
		if apiErr, ok := err.(*catalogReportService.LookerAPIError); ok {
			switch apiErr.StatusCode {
			case http.StatusUnauthorized:
				c.JSON(http.StatusUnauthorized, gin.H{
					"status":  "error",
					"message": "Unauthorized access to Looker API",
					"details": apiErr.Message,
				})
				return
			case http.StatusNotFound:
				c.JSON(http.StatusUnprocessableEntity, gin.H{
					"status":  "error",
					"message": "Invalid Looker dashboard ID",
					"details": apiErr.Message,
				})
				return
			default:
				c.JSON(http.StatusInternalServerError, gin.H{
					"status":  "error",
					"message": "Looker API returned an error",
					"details": apiErr.Message,
				})
				return
			}
		}

		// Fallback for non-API errors
		c.JSON(http.StatusInternalServerError, gin.H{
			"status":  "error",
			"message": "Failed to retrieve Looker dashboard metadata",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"status":  "success",
		"message": "Successfully retrieved Looker dashboard metadata",
		"data":    metaData,
	})
}

func GetTokenByWorkspace(cfg config.Config, groupID string, authHeader string) (string, error) {
	if groupID == "" || groupID == "me" {
		if authHeader == "" {
			return "", errors.New("missing Authorization header for My Workspace")
		}

		parts := strings.SplitN(authHeader, " ", 2)
		if len(parts) != 2 || !strings.EqualFold(parts[0], "Bearer") {
			return "", errors.New("invalid Authorization header format (expected 'Bearer <token>')")
		}

		return parts[1], nil
	}

	token, err := GetTokenByWorkspace(cfg, groupID, authHeader)
	if err != nil {
		return "", fmt.Errorf("failed to fetch service principal token: %w", err)
	}

	return token, nil
}

func TableauTokenHandler(c *gin.Context, cfg config.Config) {

	// baseURL := cfg.Dapr.SecretKeys["LOOKERSDK_BASE_URL"]
	// clientID := cfg.Dapr.SecretKeys["LOOKER_CLIENT_ID"]
	// clientSecret := cfg.Dapr.SecretKeys["LOOKER_CLIENT_SECRET"]

	clientID := cfg.Dapr.SecretKeys["TABLEAU_CLIENT_ID"]
	secretID := cfg.Dapr.SecretKeys["TABLEAU_SECRET_ID"]
	secretValue := cfg.Dapr.SecretKeys["TABLEAU_SECRET_VALUE"]
	userEmail := cfg.Dapr.SecretKeys["TABLEAU_USER_EMAIL"]

	if clientID == "" || secretID == "" || secretValue == "" || userEmail == "" {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Missing required environment variables"})
		return
	}

	now := time.Now().Unix()

	claims := jwt.MapClaims{
		"iss": clientID,
		"sub": userEmail,
		"aud": "tableau",
		"exp": now + 60, // 1 min expiry
		"jti": time.Now().Format("20060102150405"),
		"scp": []string{"tableau:views:embed", "tableau:content:read"},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	token.Header["kid"] = secretID

	signedToken, err := token.SignedString([]byte(secretValue))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate Tableau JWT"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"token": signedToken})
}
