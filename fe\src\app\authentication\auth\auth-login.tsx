import LogoutIcon from '@mui/icons-material/Logout'
import { Alert, Button, CircularProgress, Snackbar, Typography } from '@mui/material'
import { signIn } from 'next-auth/react'
import { useSearchParams } from 'next/navigation'
import { useEffect, useState } from 'react'

interface LoginType {
    title?: string
    subtitle?: JSX.Element | JSX.Element[]
    subtext?: JSX.Element | JSX.Element[]
}

const AuthLogin = ({ title, subtitle, subtext }: LoginType) => {
    const [isLoading, setIsLoading] = useState(false)
    const [error, setError] = useState<string | null>(null)
    const searchParams = useSearchParams()

    useEffect(() => {
        // Show a generic error message if any error param is present
        const errorParam = searchParams.get('error')
        if (errorParam) {
            setError('An unexpected error occurred. Please contact support.')
        }
    }, [searchParams])

    const handleLogin = async () => {
        setIsLoading(true)
        // Redirect to registration page
        await signIn('azure-ad', { callbackUrl: '/' })
    }

    return (
        <>
            <Typography variant="subtitle1" textAlign="center" color="textSecondary" mb={1}>
                Sign In
            </Typography>

            <Button
                color="primary"
                variant="contained"
                size="small"
                fullWidth
                onClick={handleLogin}
                disabled={isLoading}
                endIcon={isLoading ? <CircularProgress size={20} color="secondary" /> : <LogoutIcon />}
            >
                {isLoading ? 'Signing In...' : 'Sign In With SSO'}
            </Button>

            <Snackbar
                open={!!error}
                autoHideDuration={10000}
                onClose={() => setError(null)}
                anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
            >
                <Alert onClose={() => setError(null)} severity="error" sx={{ width: '100%' }}>
                    {error}
                </Alert>
            </Snackbar>
        </>
    )
}

export default AuthLogin
