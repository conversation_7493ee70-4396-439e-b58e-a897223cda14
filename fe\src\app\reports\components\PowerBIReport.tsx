'use client'

import { Box, CircularProgress, Typography } from '@mui/material'
import { models } from 'powerbi-client'
import { PowerBIEmbed } from 'powerbi-client-react'
import { useCallback, useEffect, useState } from 'react'
import { pbiEmbedTokenService } from '@/app/reports/services'

interface PowerBIReportProps {
    reportId: string
    groupId: string
}

export default function PowerBIReport({ reportId, groupId }: Readonly<PowerBIReportProps>) {
    const [embedToken, setEmbedToken] = useState<string | null>(null)
    const [isLoading, setIsLoading] = useState(false)
    const [error, setError] = useState<string | null>(null)

    const embedUrl = `https://app.powerbi.com/reportEmbed?reportId=${reportId}`

    const fetchAndSetEmbedToken = useCallback(async () => {
        setIsLoading(true)
        setError(null) // Clear any previous error
        try {
            const token = await pbiEmbedTokenService(reportId, groupId)
            setEmbedToken(token)
        } catch (err) {
            console.error('Error fetching embed token:', err)
            setError('Failed to load the report. Please try again later.')
        } finally {
            setIsLoading(false)
        }
    }, [reportId, groupId])

    useEffect(() => {
        const cachedData = sessionStorage.getItem(`reportConfig-${reportId}`)
        if (cachedData) {
            const { embedToken } = JSON.parse(cachedData)
            setEmbedToken(embedToken)
        } else {
            fetchAndSetEmbedToken()
        }
    }, [reportId, fetchAndSetEmbedToken])

    if (isLoading) {
        return (
            <Box display="flex" justifyContent="center" my={10}>
                <CircularProgress color="info" size={150} />
            </Box>
        )
    }

    if (error) {
        return (
            <Box display="flex" justifyContent="center" alignItems="center" flexDirection="column" mt={20}>
                <Typography color="warning" variant="h6" gutterBottom>
                    {error}
                </Typography>
                <Typography variant="body1">If the problem persists, please contact support.</Typography>
            </Box>
        )
    }

    if (!embedToken) {
        return (
            <Box display="flex" justifyContent="center" mt={20}>
                <Typography variant="body1">Initializing...</Typography>
            </Box>
        )
    }

    return (
        <PowerBIEmbed
            embedConfig={{
                type: 'report',
                id: reportId,
                embedUrl: embedUrl,
                accessToken: embedToken,
                tokenType: models.TokenType.Embed,
                settings: {
                    panes: {
                        filters: { visible: false },
                        pageNavigation: { visible: true },
                    },
                },
            }}
            cssClassName="powerbi-report-container"
            eventHandlers={
                new Map([
                    ['loaded', () => console.log('Report Loaded')],
                    ['rendered', () => console.log('Report Rendered')],
                    ['error', (event: any) => console.error(event.detail)],
                ])
            }
        />
    )
}
