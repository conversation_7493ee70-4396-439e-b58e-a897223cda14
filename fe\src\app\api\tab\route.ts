// app/api/tableau-token/route.ts
import { NextRequest, NextResponse } from 'next/server'
import jwt, { JwtHeader, SignOptions } from 'jsonwebtoken'

const CLIENT_ID = process.env.TABLEAU_CLIENT_ID!
const SECRET_ID = process.env.TABLEAU_SECRET_ID!
const SECRET_VALUE = process.env.TABLEAU_SECRET_VALUE!
const TABLEAU_USER_EMAIL = process.env.TABLEAU_USER_EMAIL! // Must be a valid Tableau user / account email id

export async function GET(req: NextRequest) {
    try {
        const now = Math.floor(Date.now() / 1000)

        const payload = {
            iss: CLIENT_ID,
            sub: TABLEAU_USER_EMAIL,
            aud: 'tableau',
            exp: now + 1 * 60,
            jti: `${now}`,
            scp: ['tableau:views:embed', 'tableau:content:read'],
        }

        const header: JwtHeader = {
            alg: 'HS256',
            kid: SECRET_ID,
        }

        const options: SignOptions = {
            algorithm: 'HS256',
            header,
        }

        const token = jwt.sign(payload, SECRET_VALUE, options)
        return NextResponse.json({ token })
    } catch (error) {
        console.error('JWT generation failed:', error)
        return NextResponse.json({ error: 'Failed to generate Tableau JWT' }, { status: 500 })
    }
}
