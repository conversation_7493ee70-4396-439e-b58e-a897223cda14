'use client'

import React from 'react'
import { Box } from '@mui/material'

interface FullPageImageErrorProps {
  imagePath?: string
}

/**
 * A full-page error component that displays only the error image
 * This creates a clean, visual-only error experience
 */
const FullPageImageError: React.FC<FullPageImageErrorProps> = ({
  imagePath = '/images/500_Err.png'
}) => {
  // Add a style to the body to prevent scrolling
  React.useEffect(() => {
    // Save the original styles
    const originalOverflow = document.body.style.overflow;
    const originalHeight = document.body.style.height;

    // Apply new styles to prevent scrolling
    document.body.style.overflow = 'hidden';
    document.body.style.height = '100%';

    // Cleanup function to restore original styles
    return () => {
      document.body.style.overflow = originalOverflow;
      document.body.style.height = originalHeight;
    };
  }, []);

  return (
    <Box
      sx={{
        position: 'fixed',
        top: 0,
        left: 0,
        width: '100vw',
        height: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: '#fff',
        zIndex: 9999,
        overflow: 'hidden',
        // Ensure this covers everything including sidebars
        margin: 0,
        padding: 0,
        border: 'none',
        boxSizing: 'border-box'
      }}
    >
      <Box
        component="img"
        src={imagePath}
        alt="Error"
        sx={{
          maxWidth: '100%',
          maxHeight: '100%',
          width: 'auto',
          height: 'auto',
          objectFit: 'contain',
          padding: 0,
          display: 'block'
        }}
        onError={(e) => {
          console.error('Error loading error image');
          // Fallback to a simple colored box if image fails to load
          const target = e.target as HTMLImageElement;
          target.style.display = 'none';
        }}
      />
    </Box>
  )
}

export default FullPageImageError
