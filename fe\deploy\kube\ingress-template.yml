apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: {{CI_PROJECT_NAME}}
  namespace: {{KUBE_NAMESPACE}}
  labels: 
    cattle.io/creator: {{GITLAB_USER_LOGIN}}
  annotations:
    #The below parameter disables client re-tries to the pod.
    nginx.ingress.kubernetes.io/proxy-next-upstream-tries: "0"
spec:
  ingressClassName: "nginx"
  rules:
  - host: {{KUBE_APP_HOST}}
    http:
      paths:
      - path: /{{INGRESS_BASE_PATH}}
        pathType: Prefix
        backend:
          service:
            name: {{KUBE_SERVICE_NAME}}
            port:
              number: {{PORT_MAPPING}}
  - host: {{KUBE_DOLGEN_APP_CNAME}}
    http:
      paths:
      - path: /{{INGRESS_BASE_PATH}}
        pathType: Prefix
        backend:
          service:
            name: {{KUBE_SERVICE_NAME}}
            port:
              number: {{PORT_MAPPING}}
