import FeatureCard from '@/app/(landing)/components/FeatureCard'
import '@testing-library/jest-dom'
import { render, screen } from '@testing-library/react'

jest.mock('next/image', () => (props: any) => <img {...props} alt={props.alt || 'mock-image'} />)

describe('FeatureCard', () => {
    const baseProps = {
        icon: 'chart' as const,
        title: '',
        link: '',
    }

    it('renders with internal link when title is not "Self-Service"', () => {
        render(<FeatureCard {...baseProps} title="Reports Catalog" link="/reports" />)

        const title = screen.getByText('Reports Catalog')
        expect(title).toBeInTheDocument()

        const link = screen.getByRole('link')
        expect(link).toHaveAttribute('href', '/reports')
    })

    it('renders with external link when title is "Self-Service"', () => {
        render(<FeatureCard {...baseProps} title="Self-Service" link="https://external.link" />)

        const title = screen.getByText('Self-Service')
        expect(title).toBeInTheDocument()

        const link = screen.getByRole('link')
        expect(link).toHaveAttribute('href', 'https://external.link')
        expect(link).toHaveAttribute('target', '_blank')
        expect(link).toHaveAttribute('rel', 'noopener noreferrer')
    })

    it('renders the correct icon image', () => {
        render(<FeatureCard {...baseProps} title="Omni Bot" icon="chat" link="/omni-bot" />)

        const image = screen.getByAltText('chat')
        expect(image).toBeInTheDocument()
        expect(image).toHaveAttribute('src', expect.stringContaining('/images/landing/message.svg'))
    })
})
