package catalogReportService

import (
	"dng-module/internal/utils"
	"errors"
	"fmt"
	"net/http"

	"github.com/xuri/excelize/v2"
)

// GenerateExcelTemplate - Creates an Excel template file with an instructions sheet
func GenerateExcelTemplate(w http.ResponseWriter) error {
	utils.Logger.Info("Generating Excel report upload template")

	f := excelize.NewFile()

	// Define sheet names
	templateSheet := "Template"
	instructionsSheet := "Instructions"

	// Create new sheets
	templateIndex, err := f.NewSheet(templateSheet)
	if err != nil {
		utils.Logger.WithError(err).Error("Failed to create template sheet")
		return errors.New("failed to create template sheet")
	}

	_, err = f.NewSheet(instructionsSheet)
	if err != nil {
		utils.Logger.WithError(err).Error("Failed to create instructions sheet")
		return errors.New("failed to create instructions sheet")
	}

	f.DeleteSheet("Sheet1")
	utils.Logger.Debug("Default 'Sheet1' removed from Excel")

	// Headers for the template sheet - Report URL is now first
	headers := []string{"Report URL", "Report Type", "Name", "Domain", "Category", "Description", "Report Id", "Group Id", "Published On"}
	for i, header := range headers {
		cell := string(rune('A'+i)) + "1"
		f.SetCellValue(templateSheet, cell, header)
	}
	utils.Logger.Debug("Template headers set")

	// Sample data - Reordered to match new column order
	sampleData := []interface{}{
		"https://app.powerbi.com/report/xyz", // Report URL first
		"PowerBI",                            //Report Type
		"Example Report",                     // Name
		"Finance",                            // Domain
		"Financial",                          // Category
		"Sample description",                 // Description
		"RPT001",                             // Report Id
		"GRP001",                             // Group Id
		"2025-03-29",                         // Published On
	}
	for i, value := range sampleData {
		cell := string(rune('A'+i)) + "2"
		f.SetCellValue(templateSheet, cell, value)
	}
	utils.Logger.Debug("Template sample data added")

	// Instructions Table Headers
	instructionHeaders := []string{"Column", "Description", "Required", "Format/Example"}
	for i, header := range instructionHeaders {
		cell := string(rune('A'+i)) + "1"
		f.SetCellValue(instructionsSheet, cell, header)
	}
	utils.Logger.Debug("Instruction headers set")

	// Instructions Data - Updated to match new column order
	instructionData := [][]interface{}{
		{"Report URL", "Direct report URL (PowerBI, Looker, etc.)", "No", "https://app.powerbi.com/report/xyz"},
		{"Report Type", "Report type identifier", "Yes", "PowerBI, Looker, WebFocus"},
		{"Name", "Report name", "Yes", "Monthly Revenue Report"},
		{"Domain", "Business domain name", "Yes", "Finance, Operations, Marketing"},
		{"Category", "Report category", "Yes", "Financial, Operational, Analytical"},
		{"Description", "Detailed description", "Yes", "Brief description of the report"},
		{"Report Id", "Unique report identifier", "Yes", "RPT001"},
		{"Group Id", "Group identifier (if applicable)", "No", "GRP001 or NULL"},
		{"Published On", "Date of report publication. Format: YYYY-MM-DD", "No", "YYYY-MM-DD"},
	}

	// Populate Instructions Sheet
	for rowIdx, row := range instructionData {
		for colIdx, value := range row {
			cell := string(rune('A'+colIdx)) + fmt.Sprint(rowIdx+2) // Start from row 2
			f.SetCellValue(instructionsSheet, cell, value)
		}
	}
	utils.Logger.Debug("Instruction data populated")

	// Template sheet will be active sheet
	f.SetActiveSheet(templateIndex)

	// Set response headers for file download
	w.Header().Set("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	w.Header().Set("Content-Disposition", "attachment; filename=Report_Template.xlsx")
	w.Header().Set("File-Name", "Report_Template.xlsx")
	w.Header().Set("Access-Control-Expose-Headers", "File-Name")

	if err := f.Write(w); err != nil {
		utils.Logger.WithError(err).Error("Failed to write Excel file to response")
		return errors.New("failed to write Excel file to response")
	}

	utils.Logger.Info("Excel report upload template successfully generated and sent")
	return nil
}
