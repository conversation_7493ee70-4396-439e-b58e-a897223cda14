'use client'

import GenericModal from '@/components/common/utility/GenericModal'
import { Box, Button, Divider, Grid2 as Grid, <PERSON>ack, Typography } from '@mui/material'
import { useState } from 'react'
import ReportForm from '../reportCards/components/ReportForm'
import { BulkUploadResponse } from '../types'
import BulkUploadReports from './BulkUpload'
import DownloadTemplate from './DownloadTemplate'
import { setRefreshFlag } from '@/app/reports/slice'
import { useAppDispatch } from '@/store/hooks'

export default function AddReport() {
    const [modalOpen, setModalOpen] = useState(false)
    const [formOpen, setFormOpen] = useState(false)
    const [uploadResponse, setUploadResponse] = useState<BulkUploadResponse | null>(null)
    const dispatch = useAppDispatch()

    const handleModalOpen = () => setModalOpen(true)
    const handleModalClose = () => setModalOpen(false)

    const handleFormOpen = () => {
        setFormOpen(true)
        setModalOpen(false) // Close the main modal when opening the form
    }
    const handleFormClose = () => setFormOpen(false)

    return (
        <>
            <Button variant="contained" onClick={handleModalOpen} color="info" sx={{ fontWeight: 600 }} size="small">
                + Add Reports
            </Button>
            <GenericModal
                open={modalOpen}
                onClose={async () => {
                    uploadResponse?.message && dispatch(setRefreshFlag(true))
                    handleModalClose()
                }}
                title="Add Reports"
            >
                <Box my={2}>
                    <Grid container justifyContent="space-evenly">
                        <Stack gap={2}>
                            <DownloadTemplate />
                            <Divider variant="fullWidth" />
                            <BulkUploadReports uploadResponse={uploadResponse} setUploadResponse={setUploadResponse} />
                        </Stack>
                        <Divider orientation="vertical" variant="fullWidth">
                            OR
                        </Divider>
                        <Stack gap={2}>
                            <Typography variant="h6" gutterBottom>
                                Add Single Report
                            </Typography>
                            <Button variant="contained" color="primary" onClick={handleFormOpen}>
                                Add Report
                            </Button>
                        </Stack>
                    </Grid>
                </Box>
            </GenericModal>

            {formOpen && <ReportForm open={formOpen} onClose={handleFormClose} />}
        </>
    )
}
