// Chat message type
export interface Message {
    id: string
    sender: 'user' | 'bot'
    text: string
    timestamp: Date
}

// Chat type
export interface Chat {
    id: string
    title: string
}

// Topic type
export interface Topic {
    id: number
    title: string
    description: string
    fullContent: string
    charts?: {
        id: string
        type: string
        title: string
        data: {
            labels: string[]
            datasets: {
                label: string
                data: number[]
                backgroundColor?: string | string[]
                borderColor?: string
                fill?: boolean
            }[]
        }
    }[]
}

// Exploration question type
export interface ExplorationQuestion {
    question: string
    answer: string
}

// Topic context type
export interface TopicContext {
    [key: string]: {
        title: string
        context: string
        insights: string[]
        questions?: string[]  // Optional array of common questions for this topic
        explorationQuestions?: ExplorationQuestion[]  // Optional array of exploration questions with answers
    }
}
