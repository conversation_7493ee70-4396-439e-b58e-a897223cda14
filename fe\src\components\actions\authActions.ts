'use server'

import authOptions from '@/utils/authOptions'
import { ADMIN_ROLE } from '@/utils/constants'
import { getServerSession } from 'next-auth'

export const getSession = async () => getServerSession(authOptions)

export const isAuthenticated = async () => {
    const session = await getSession()
    return !!session
}

export const isAdmin = async () => {
    const session = await getSession()
    return !!session?.user?.roles?.some((role) => role.role === ADMIN_ROLE)
}
