import '@testing-library/jest-dom'
import { fireEvent, render, screen, waitFor } from '@testing-library/react'
import TopicsGrid from '../../app/omni-bot/components/TopicsGrid'
import { Topic } from '../../app/omni-bot/types'

const topics: Topic[] = [
    { id: 1, title: 'Topic 1', description: 'desc1', fullContent: 'full1' },
    { id: 2, title: 'Topic 2', description: 'desc2', fullContent: 'full2' },
]

describe('TopicsGrid', () => {
    it('renders topics and heading', () => {
        render(<TopicsGrid topics={topics} onReadClick={() => {}} />)
        expect(screen.getByRole('heading', { name: /Topics For You/i })).toBeInTheDocument()
        expect(screen.getByText('Topic 1')).toBeInTheDocument()
        expect(screen.getByText('Topic 2')).toBeInTheDocument()
    })

    it('shows modal with topic details and closes it', async () => {
        render(<TopicsGrid topics={topics} onReadClick={() => {}} />)
        // Open modal
        const readMoreButtons = await screen.findAllByRole('button', { name: /Read More/i })
        fireEvent.click(readMoreButtons[0])
        await waitFor(() => {
            expect(screen.getByText(/Explore Topic 1/i)).toBeInTheDocument()
        })
        // Close modal (find the close icon button by role and SVG icon)
        const closeButtons = screen.getAllByRole('button')
        const closeButton = closeButtons.find((btn) => btn.querySelector('svg[data-testid="CloseIcon"]'))
        expect(closeButton).toBeTruthy()
        fireEvent.click(closeButton!)
        await waitFor(() => {
            expect(screen.queryByText(/Explore Topic 1/i)).not.toBeInTheDocument()
        })
    })

    it('calls onReadClick when "Ask Omnibot About This Topic" is clicked', async () => {
        const onReadClick = jest.fn()
        render(<TopicsGrid topics={topics} onReadClick={onReadClick} />)
        // Open modal
        const readMoreButtons = await screen.findAllByRole('button', { name: /Read More/i })
        fireEvent.click(readMoreButtons[0])
        // Click ask button
        await waitFor(() => {
            expect(screen.getByRole('button', { name: /Ask Omnibot About This Topic/i })).toBeInTheDocument()
        })
        fireEvent.click(screen.getByRole('button', { name: /Ask Omnibot About This Topic/i }))
        expect(onReadClick).toHaveBeenCalledWith(1)
    })

    it('shows "No topics available" if topics is empty', () => {
        render(<TopicsGrid topics={[]} onReadClick={() => {}} />)
        expect(screen.getByText(/No topics available/i)).toBeInTheDocument()
    })
})
