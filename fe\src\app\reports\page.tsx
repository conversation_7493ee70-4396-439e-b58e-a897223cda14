import GenericLayout from '@/components/common/layout/GenericLayout'
import PageTitle from '@/components/common/utility/PageTitle'
import ReportTable from '@/app/reports/components/ReportTable'
import ReportsHeader from '@/app/reports/reportSearch/components/ReportsHeader'
import { getReportView } from '@/utils/helper'
import { Box } from '@mui/material'

export default function Report({ searchParams }: Readonly<{ searchParams: { view: string } }>) {
    const view = getReportView(searchParams.view)
    return (
        <GenericLayout showSidebar={true} useContainer={true}>
            <PageTitle title="Reports Catalog" />
            <Box mt={2}>
                <ReportsHeader />
            </Box>
            <Box mt={1}>
                <ReportTable view={view} />
            </Box>
        </GenericLayout>
    )
}
