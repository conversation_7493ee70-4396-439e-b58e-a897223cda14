import '@testing-library/jest-dom'
import { fireEvent, render, screen, waitFor } from '@testing-library/react'
import Page from '../../app/omni-bot/page'

// Mock scrollIntoView for jsdom
beforeAll(() => {
    window.HTMLElement.prototype.scrollIntoView = function () {}
})

// Mock the fetchTopicsService and fetchChatsService to always return topics/chats
jest.mock('../../app/omni-bot/services', () => {
    const original = jest.requireActual('../../app/omni-bot/services')
    return {
        ...original,
        fetchTopicsService: jest.fn(async () => [
            { id: 1, title: 'Test Topic', description: 'desc', fullContent: 'full', charts: [] },
        ]),
        fetchChatsService: jest.fn(async () => [{ id: 't1', title: 'Network Accuracy Report' }]),
    }
})

describe('Omnibot Page', () => {
    it('renders the topics heading', async () => {
        render(<Page />)
        // Wait for topics to load and heading to appear
        const heading = await screen.findByRole('heading', { name: /Topics For You/i })
        expect(heading).toBeInTheDocument()
    })

    it('renders the floating new chat button', async () => {
        render(<Page />)
        const fab = await screen.findByRole('button', { name: /start new chat/i })
        expect(fab).toBeInTheDocument()
    })

    it('renders the topics grid', async () => {
        render(<Page />)
        // Wait for topics to load and heading to appear
        const heading = await screen.findByRole('heading', { name: /Topics For You/i })
        expect(heading).toBeInTheDocument()
    })

    it('shows a modal with topic details when a topic is clicked', async () => {
        render(<Page />)
        // Wait for topics to load
        await screen.findByRole('heading', { name: /Topics For You/i })
        // Click the first "Read More" button in the carousel
        const readMoreButtons = await screen.findAllByRole('button', { name: /Read More/i })
        fireEvent.click(readMoreButtons[0])
        // Modal should open with "Ask Omnibot About This Topic"
        await waitFor(() => {
            expect(screen.getByRole('button', { name: /Ask Omnibot About This Topic/i })).toBeInTheDocument()
        })
    })

    it('renders a text input field with the correct placeholder in chat mode', async () => {
        render(<Page />)
        // Wait for chats to load
        await screen.findByText(/Network Accuracy Report/i)
        // Open a chat by clicking the first chat in the sidebar
        const sidebarChat = screen.getByText(/Network Accuracy Report/i)
        fireEvent.click(sidebarChat)
        // Input should appear with the correct placeholder
        await waitFor(() => {
            expect(screen.getByPlaceholderText('Ask Omnibot...')).toBeInTheDocument()
        })
    })

    // xit('shows the snackbar when renaming a chat', async () => {
    //     render(<Page />)
    //     // Wait for chats to load
    //     await screen.findByText(/Network Accuracy Report/i)
    //     // Open chat menu (three dots) for the first chat
    //     const moreButtons = await screen.findAllByRole('button', { name: /more/i })
    //     fireEvent.click(moreButtons[0])
    //     // Wait for the Rename menu item to appear in the document (portal)
    //     let renameMenuItem: HTMLElement | null = null
    //     await waitFor(() => {
    //         // Use a flexible matcher to find the Rename menu item
    //         const candidates = Array.from(document.body.querySelectorAll('[role="menuitem"]'))
    //         // Find the first menuitem that contains "Rename" (case-insensitive, trimmed, or partial match)
    //         renameMenuItem = candidates.find(
    //             (el) => el.textContent && el.textContent.replace(/\s+/g, '').toLowerCase().includes('rename'),
    //         ) as HTMLElement | null
    //         expect(renameMenuItem).toBeTruthy()
    //     })
    //     fireEvent.click(renameMenuItem!)
    //     // Change the chat name and save
    //     const input = screen.getByLabelText(/Chat Name/i)
    //     fireEvent.change(input, { target: { value: 'Renamed Chat' } })
    //     const saveButton = screen.getByRole('button', { name: /Save/i })
    //     fireEvent.click(saveButton)
    //     // Snackbar should appear
    //     await waitFor(() => {
    //         expect(screen.getByText(/Chat renamed to "Renamed Chat"/i)).toBeInTheDocument()
    //     })
    // })
})
